{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\PolicyHolder.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Table, Button, Form, Modal, Row, Col } from 'react-bootstrap';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PolicyHolder = () => {\n  _s();\n  const [showModal, setShowModal] = useState(false);\n  const [search, setSearch] = useState('');\n  const [holders, setHolders] = useState([\n    // {\n    //   id: 1,\n    //   holderName: 'Anand <PERSON>',\n    //   contactNumber: '9876543210',\n    //   policyName: 'AutoSecure Collision Plan',\n    //   category: 'Auto Insurance',\n    //   subCategory: 'Comprehensive Coverage',\n    //   sumAssured: '80000',\n    //   premium: '8000',\n    //   tenure: '36',\n    //   status: 'Active'\n    // },\n    // {\n    //   id: 2,\n    //   holderName: '<PERSON><PERSON><PERSON>',\n    //   contactNumber: '9876543770',\n    //   policyName: 'FamilyShield Term Plan',\n    //   category: 'Life Insurance',\n    //   subCategory: 'Term Life Insurance',\n    //   sumAssured: '150000',\n    //   premium: '20000',\n    //   tenure: '24',\n    //   status: 'Pending'\n    // }\n  ]);\n  const [form, setForm] = useState({\n    holderName: '',\n    contactNumber: '',\n    policyName: '',\n    category: '',\n    subCategory: '',\n    sumAssured: '',\n    premium: '',\n    tenure: ''\n  });\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSave = () => {\n    const newHolder = {\n      ...form,\n      id: holders.length + 1,\n      status: 'Active'\n    };\n    setHolders([...holders, newHolder]);\n    setShowModal(false);\n    setForm({\n      holderName: '',\n      contactNumber: '',\n      policyName: '',\n      category: '',\n      subCategory: '',\n      sumAssured: '',\n      premium: '',\n      tenure: ''\n    });\n  };\n  const filteredHolders = holders.filter(h => h.holderName.toLowerCase().includes(search.toLowerCase()) || h.policyName.toLowerCase().includes(search.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Policy Holders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search policy holders...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Holder Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Contact Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sum Assured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Premium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tenure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredHolders.map(h => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.holderName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.contactNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.policyName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.subCategory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.sumAssured, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.premium, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.tenure, \" months\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge bg-${h.status === 'Active' ? 'success' : 'warning'}`,\n              children: h.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, h.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"New Policy Holder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Holder Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"holderName\",\n              value: form.holderName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Contact Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"contactNumber\",\n              value: form.contactNumber,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"policyName\",\n              value: form.policyName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"category\",\n                value: form.category,\n                onChange: handleChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Auto Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Life Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Travel Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sub Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"subCategory\",\n                value: form.subCategory,\n                onChange: handleChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Comprehensive Coverage\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Term Life Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Travel Cancellation Insurance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sum Assured\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"sumAssured\",\n                value: form.sumAssured,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"premium\",\n                value: form.premium,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tenure (Months)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"tenure\",\n              value: form.tenure,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(PolicyHolder, \"1cv58aNrxbJlsBYBwUk1lU2bxHo=\");\n_c = PolicyHolder;\nexport default PolicyHolder;\nvar _c;\n$RefreshReg$(_c, \"PolicyHolder\");", "map": {"version": 3, "names": ["React", "useState", "Table", "<PERSON><PERSON>", "Form", "Modal", "Row", "Col", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "PolicyHolder", "_s", "showModal", "setShowModal", "search", "setSearch", "holders", "setHolders", "form", "setForm", "<PERSON><PERSON><PERSON>", "contactNumber", "policyName", "category", "subCategory", "sumAssured", "premium", "tenure", "handleChange", "e", "target", "name", "value", "handleSave", "newHolder", "id", "length", "status", "filteredHolders", "filter", "h", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/PolicyHolder.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Table, Button, Form, Modal, Row, Col } from 'react-bootstrap';\r\nimport { FaEdit, FaTrash } from 'react-icons/fa';\r\n\r\nconst PolicyHolder = () => {\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [search, setSearch] = useState('');\r\n\r\n  const [holders, setHolders] = useState([\r\n    // {\r\n    //   id: 1,\r\n    //   holderName: '<PERSON>',\r\n    //   contactNumber: '9876543210',\r\n    //   policyName: 'AutoSecure Collision Plan',\r\n    //   category: 'Auto Insurance',\r\n    //   subCategory: 'Comprehensive Coverage',\r\n    //   sumAssured: '80000',\r\n    //   premium: '8000',\r\n    //   tenure: '36',\r\n    //   status: 'Active'\r\n    // },\r\n    // {\r\n    //   id: 2,\r\n    //   holderName: '<PERSON><PERSON><PERSON>',\r\n    //   contactNumber: '9876543770',\r\n    //   policyName: 'FamilyShield Term Plan',\r\n    //   category: 'Life Insurance',\r\n    //   subCategory: 'Term Life Insurance',\r\n    //   sumAssured: '150000',\r\n    //   premium: '20000',\r\n    //   tenure: '24',\r\n    //   status: 'Pending'\r\n    // }\r\n  ]);\r\n\r\n  const [form, setForm] = useState({\r\n    holderName: '',\r\n    contactNumber: '',\r\n    policyName: '',\r\n    category: '',\r\n    subCategory: '',\r\n    sumAssured: '',\r\n    premium: '',\r\n    tenure: ''\r\n  });\r\n\r\n  const handleChange = (e) => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSave = () => {\r\n    const newHolder = { ...form, id: holders.length + 1, status: 'Active' };\r\n    setHolders([...holders, newHolder]);\r\n    setShowModal(false);\r\n    setForm({\r\n      holderName: '', contactNumber: '', policyName: '', category: '',\r\n      subCategory: '', sumAssured: '', premium: '', tenure: ''\r\n    });\r\n  };\r\n\r\n  const filteredHolders = holders.filter(h =>\r\n    h.holderName.toLowerCase().includes(search.toLowerCase()) ||\r\n    h.policyName.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h4 className=\"fw-bold text-uppercase\">Policy Holders</h4>\r\n        <Button variant=\"primary\" onClick={() => setShowModal(true)}>+ New</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search policy holders...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Policy Holder Name</th>\r\n            <th>Contact Number</th>\r\n            <th>Policy Name</th>\r\n            <th>Category</th>\r\n            <th>Sub Category</th>\r\n            <th>Sum Assured</th>\r\n            <th>Premium</th>\r\n            <th>Tenure</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredHolders.map((h) => (\r\n            <tr key={h.id}>\r\n              <td>{h.holderName}</td>\r\n              <td>{h.contactNumber}</td>\r\n              <td>{h.policyName}</td>\r\n              <td>{h.category}</td>\r\n              <td>{h.subCategory}</td>\r\n              <td>{h.sumAssured} PHP</td>\r\n              <td>{h.premium} PHP</td>\r\n              <td>{h.tenure} months</td>\r\n              <td><span className={`badge bg-${h.status === 'Active' ? 'success' : 'warning'}`}>{h.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>New Policy Holder</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Holder Name</Form.Label>\r\n              <Form.Control name=\"holderName\" value={form.holderName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Contact Number</Form.Label>\r\n              <Form.Control name=\"contactNumber\" value={form.contactNumber} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Name</Form.Label>\r\n              <Form.Control name=\"policyName\" value={form.policyName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Category</Form.Label>\r\n                <Form.Select name=\"category\" value={form.category} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  <option>Auto Insurance</option>\r\n                  <option>Life Insurance</option>\r\n                  <option>Travel Insurance</option>\r\n                </Form.Select>\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Sub Category</Form.Label>\r\n                <Form.Select name=\"subCategory\" value={form.subCategory} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  <option>Comprehensive Coverage</option>\r\n                  <option>Term Life Insurance</option>\r\n                  <option>Travel Cancellation Insurance</option>\r\n                </Form.Select>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Sum Assured</Form.Label>\r\n                <Form.Control name=\"sumAssured\" value={form.sumAssured} onChange={handleChange} />\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Premium</Form.Label>\r\n                <Form.Control name=\"premium\" value={form.premium} onChange={handleChange} />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Tenure (Months)</Form.Label>\r\n              <Form.Control name=\"tenure\" value={form.tenure} onChange={handleChange} />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={handleSave}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PolicyHolder;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACtE,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD,CAAC;EAEF,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC;IAC/BqB,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BV,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAACW,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAG;MAAE,GAAGhB,IAAI;MAAEiB,EAAE,EAAEnB,OAAO,CAACoB,MAAM,GAAG,CAAC;MAAEC,MAAM,EAAE;IAAS,CAAC;IACvEpB,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAEkB,SAAS,CAAC,CAAC;IACnCrB,YAAY,CAAC,KAAK,CAAC;IACnBM,OAAO,CAAC;MACNC,UAAU,EAAE,EAAE;MAAEC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAC/DC,WAAW,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IACxD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,eAAe,GAAGtB,OAAO,CAACuB,MAAM,CAACC,CAAC,IACtCA,CAAC,CAACpB,UAAU,CAACqB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,MAAM,CAAC2B,WAAW,CAAC,CAAC,CAAC,IACzDD,CAAC,CAAClB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,MAAM,CAAC2B,WAAW,CAAC,CAAC,CAC1D,CAAC;EAED,oBACEhC,OAAA;IAAKkC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCnC,OAAA;MAAKkC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrEnC,OAAA;QAAIkC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DvC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,IAAI,CAAE;QAAA+B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eAENvC,OAAA;MAAKkC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CnC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DvC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DvC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DvC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DvC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENvC,OAAA;MAAKkC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBnC,OAAA,CAACP,IAAI,CAACkD,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,0BAA0B;QACtCtB,KAAK,EAAElB,MAAO;QACdyC,QAAQ,EAAG1B,CAAC,IAAKd,SAAS,CAACc,CAAC,CAACC,MAAM,CAACE,KAAK;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvC,OAAA,CAACT,KAAK;MAACwD,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACf,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDnC,OAAA;QAAOkC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BnC,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YAAAmC,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BvC,OAAA;YAAAmC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBvC,OAAA;YAAAmC,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBvC,OAAA;YAAAmC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBvC,OAAA;YAAAmC,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBvC,OAAA;YAAAmC,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBvC,OAAA;YAAAmC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBvC,OAAA;YAAAmC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfvC,OAAA;YAAAmC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfvC,OAAA;YAAAmC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRvC,OAAA;QAAAmC,QAAA,EACGN,eAAe,CAACqB,GAAG,CAAEnB,CAAC,iBACrB/B,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YAAAmC,QAAA,EAAKJ,CAAC,CAACpB;UAAU;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBvC,OAAA;YAAAmC,QAAA,EAAKJ,CAAC,CAACnB;UAAa;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BvC,OAAA;YAAAmC,QAAA,EAAKJ,CAAC,CAAClB;UAAU;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBvC,OAAA;YAAAmC,QAAA,EAAKJ,CAAC,CAACjB;UAAQ;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBvC,OAAA;YAAAmC,QAAA,EAAKJ,CAAC,CAAChB;UAAW;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBvC,OAAA;YAAAmC,QAAA,GAAKJ,CAAC,CAACf,UAAU,EAAC,MAAI;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BvC,OAAA;YAAAmC,QAAA,GAAKJ,CAAC,CAACd,OAAO,EAAC,MAAI;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBvC,OAAA;YAAAmC,QAAA,GAAKJ,CAAC,CAACb,MAAM,EAAC,SAAO;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BvC,OAAA;YAAAmC,QAAA,eAAInC,OAAA;cAAMkC,SAAS,EAAE,YAAYH,CAAC,CAACH,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAG;cAAAO,QAAA,EAAEJ,CAAC,CAACH;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxGvC,OAAA;YAAAmC,QAAA,gBACEnC,OAAA,CAACR,MAAM;cAACgD,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAACnC,OAAA,CAACH,MAAM;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxEvC,OAAA,CAACR,MAAM;cAACgD,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAACnC,OAAA,CAACF,OAAO;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAbER,CAAC,CAACL,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcT,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRvC,OAAA,CAACN,KAAK;MAACyD,IAAI,EAAEhD,SAAU;MAACiD,MAAM,EAAEA,CAAA,KAAMhD,YAAY,CAAC,KAAK,CAAE;MAACiD,QAAQ;MAAAlB,QAAA,gBACjEnC,OAAA,CAACN,KAAK,CAAC4D,MAAM;QAACC,WAAW;QAAApB,QAAA,eACvBnC,OAAA,CAACN,KAAK,CAAC8D,KAAK;UAAArB,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACfvC,OAAA,CAACN,KAAK,CAAC+D,IAAI;QAAAtB,QAAA,eACTnC,OAAA,CAACP,IAAI;UAAA0C,QAAA,gBACHnC,OAAA,CAACP,IAAI,CAACiE,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACkE,KAAK;cAAAxB,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CvC,OAAA,CAACP,IAAI,CAACkD,OAAO;cAACrB,IAAI,EAAC,YAAY;cAACC,KAAK,EAAEd,IAAI,CAACE,UAAW;cAACmC,QAAQ,EAAE3B;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbvC,OAAA,CAACP,IAAI,CAACiE,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACkE,KAAK;cAAAxB,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCvC,OAAA,CAACP,IAAI,CAACkD,OAAO;cAACrB,IAAI,EAAC,eAAe;cAACC,KAAK,EAAEd,IAAI,CAACG,aAAc;cAACkC,QAAQ,EAAE3B;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEbvC,OAAA,CAACP,IAAI,CAACiE,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACkE,KAAK;cAAAxB,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCvC,OAAA,CAACP,IAAI,CAACkD,OAAO;cAACrB,IAAI,EAAC,YAAY;cAACC,KAAK,EAAEd,IAAI,CAACI,UAAW;cAACiC,QAAQ,EAAE3B;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbvC,OAAA,CAACL,GAAG;YAACuC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA,CAACJ,GAAG;cAAAuC,QAAA,gBACFnC,OAAA,CAACP,IAAI,CAACkE,KAAK;gBAAAxB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCvC,OAAA,CAACP,IAAI,CAACmE,MAAM;gBAACtC,IAAI,EAAC,UAAU;gBAACC,KAAK,EAAEd,IAAI,CAACK,QAAS;gBAACgC,QAAQ,EAAE3B,YAAa;gBAAAgB,QAAA,gBACxEnC,OAAA;kBAAQuB,KAAK,EAAC,EAAE;kBAAAY,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNvC,OAAA,CAACJ,GAAG;cAAAuC,QAAA,gBACFnC,OAAA,CAACP,IAAI,CAACkE,KAAK;gBAAAxB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCvC,OAAA,CAACP,IAAI,CAACmE,MAAM;gBAACtC,IAAI,EAAC,aAAa;gBAACC,KAAK,EAAEd,IAAI,CAACM,WAAY;gBAAC+B,QAAQ,EAAE3B,YAAa;gBAAAgB,QAAA,gBAC9EnC,OAAA;kBAAQuB,KAAK,EAAC,EAAE;kBAAAY,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCvC,OAAA;kBAAAmC,QAAA,EAAQ;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA,CAACL,GAAG;YAACuC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA,CAACJ,GAAG;cAAAuC,QAAA,gBACFnC,OAAA,CAACP,IAAI,CAACkE,KAAK;gBAAAxB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCvC,OAAA,CAACP,IAAI,CAACkD,OAAO;gBAACrB,IAAI,EAAC,YAAY;gBAACC,KAAK,EAAEd,IAAI,CAACO,UAAW;gBAAC8B,QAAQ,EAAE3B;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNvC,OAAA,CAACJ,GAAG;cAAAuC,QAAA,gBACFnC,OAAA,CAACP,IAAI,CAACkE,KAAK;gBAAAxB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChCvC,OAAA,CAACP,IAAI,CAACkD,OAAO;gBAACrB,IAAI,EAAC,SAAS;gBAACC,KAAK,EAAEd,IAAI,CAACQ,OAAQ;gBAAC6B,QAAQ,EAAE3B;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA,CAACP,IAAI,CAACiE,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACkE,KAAK;cAAAxB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxCvC,OAAA,CAACP,IAAI,CAACkD,OAAO;cAACrB,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAEd,IAAI,CAACS,MAAO;cAAC4B,QAAQ,EAAE3B;YAAa;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbvC,OAAA,CAACN,KAAK,CAACmE,MAAM;QAAA1B,QAAA,gBACXnC,OAAA,CAACR,MAAM;UAACgD,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EvC,OAAA,CAACR,MAAM;UAACgD,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEjB,UAAW;UAAAW,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrC,EAAA,CA5LID,YAAY;AAAA6D,EAAA,GAAZ7D,YAAY;AA8LlB,eAAeA,YAAY;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}