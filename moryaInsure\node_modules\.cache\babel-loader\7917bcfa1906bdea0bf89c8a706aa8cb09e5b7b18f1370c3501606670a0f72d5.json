{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\dashboards\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalPolicyHolders: 0,\n    pendingPolicies: 0,\n    approvedPolicies: 0,\n    deniedPolicies: 0,\n    totalTickets: 0,\n    pendingTickets: 0,\n    closedTickets: 0,\n    activeFields: 0,\n    totalEmployees: 0,\n    totalCustomers: 0,\n    monthlyRevenue: 0,\n    systemAlerts: 0\n  });\n\n  // Simulate data fetching\n  useEffect(() => {\n    const fetchedStats = {\n      totalPolicyHolders: 1250,\n      pendingPolicies: 45,\n      approvedPolicies: 980,\n      deniedPolicies: 125,\n      totalTickets: 180,\n      pendingTickets: 35,\n      closedTickets: 145,\n      activeFields: 12,\n      totalEmployees: 25,\n      totalCustomers: 1200,\n      monthlyRevenue: 125000,\n      systemAlerts: 3\n    };\n    setStats(fetchedStats);\n  }, []);\n  const cardData = [{\n    label: 'Total Customers',\n    count: stats.totalCustomers,\n    icon: 'bi-people-fill',\n    color: 'primary'\n  }, {\n    label: 'Total Employees',\n    count: stats.totalEmployees,\n    icon: 'bi-person-badge-fill',\n    color: 'info'\n  }, {\n    label: 'Policy Holders',\n    count: stats.totalPolicyHolders,\n    icon: 'bi-shield-fill-check',\n    color: 'success'\n  }, {\n    label: 'Monthly Revenue',\n    count: `$${stats.monthlyRevenue.toLocaleString()}`,\n    icon: 'bi-currency-dollar',\n    color: 'warning'\n  }, {\n    label: 'Pending Policies',\n    count: stats.pendingPolicies,\n    icon: 'bi-hourglass-split',\n    color: 'warning'\n  }, {\n    label: 'Approved Policies',\n    count: stats.approvedPolicies,\n    icon: 'bi-check-circle-fill',\n    color: 'success'\n  }, {\n    label: 'Support Tickets',\n    count: stats.totalTickets,\n    icon: 'bi-ticket-detailed-fill',\n    color: 'info'\n  }, {\n    label: 'System Alerts',\n    count: stats.systemAlerts,\n    icon: 'bi-exclamation-triangle-fill',\n    color: 'danger'\n  }];\n  const quickActions = [{\n    title: 'Manage Users',\n    description: 'Add, edit, or remove users',\n    link: '/users',\n    icon: 'bi-people'\n  }, {\n    title: 'System Settings',\n    description: 'Configure system parameters',\n    link: '/system-settings',\n    icon: 'bi-gear'\n  }, {\n    title: 'View Reports',\n    description: 'Generate and view reports',\n    link: '/report-tool',\n    icon: 'bi-graph-up'\n  }, {\n    title: 'Manage Staff',\n    description: 'Employee management',\n    link: '/staff',\n    icon: 'bi-person-workspace'\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"fw-bold\",\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"lead\",\n        children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-primary fw-semibold\",\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 25\n        }, this), \"! Here's your system overview.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: cardData.map((item, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: `shadow-sm border-0 bg-${item.color} text-white h-100`,\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"d-flex align-items-center justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"card-title mb-2\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"fw-bold\",\n                children: item.count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${item.icon} fs-2`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"fw-bold mb-4\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: quickActions.map((action, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm border-0 h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${action.icon} text-primary fs-1 mb-3`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-3\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              href: action.link,\n              className: \"w-100\",\n              children: \"Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"Recent System Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"list-group list-group-flush\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"New user registration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"John Doe registered as customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"2 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Policy approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Life insurance policy #LP-2024-001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"4 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Support ticket resolved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Ticket #ST-2024-045 closed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"6 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"System Health\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Server Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-success\",\n                  children: \"Online\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Database\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-success\",\n                  children: \"Connected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Backup Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-warning\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Security Alerts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-danger\",\n                  children: stats.systemAlerts\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"QTpw6KxO6XfHUNY1kst13BY2iHs=\", false, function () {\n  return [useAuth];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "useAuth", "reportsAPI", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "stats", "setStats", "totalPolicyHolders", "pendingPolicies", "approvedPolicies", "deniedPolicies", "totalTickets", "pendingTickets", "closedTickets", "activeFields", "totalEmployees", "totalCustomers", "monthlyRevenue", "systemAlerts", "fetchedStats", "cardData", "label", "count", "icon", "color", "toLocaleString", "quickActions", "title", "description", "link", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "item", "idx", "lg", "md", "Body", "action", "variant", "href", "Header", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/dashboards/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI } from '../../services/api';\n\nconst AdminDashboard = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalPolicyHolders: 0,\n    pendingPolicies: 0,\n    approvedPolicies: 0,\n    deniedPolicies: 0,\n    totalTickets: 0,\n    pendingTickets: 0,\n    closedTickets: 0,\n    activeFields: 0,\n    totalEmployees: 0,\n    totalCustomers: 0,\n    monthlyRevenue: 0,\n    systemAlerts: 0,\n  });\n\n  // Simulate data fetching\n  useEffect(() => {\n    const fetchedStats = {\n      totalPolicyHolders: 1250,\n      pendingPolicies: 45,\n      approvedPolicies: 980,\n      deniedPolicies: 125,\n      totalTickets: 180,\n      pendingTickets: 35,\n      closedTickets: 145,\n      activeFields: 12,\n      totalEmployees: 25,\n      totalCustomers: 1200,\n      monthlyRevenue: 125000,\n      systemAlerts: 3,\n    };\n    setStats(fetchedStats);\n  }, []);\n\n  const cardData = [\n    { label: 'Total Customers', count: stats.totalCustomers, icon: 'bi-people-fill', color: 'primary' },\n    { label: 'Total Employees', count: stats.totalEmployees, icon: 'bi-person-badge-fill', color: 'info' },\n    { label: 'Policy Holders', count: stats.totalPolicyHolders, icon: 'bi-shield-fill-check', color: 'success' },\n    { label: 'Monthly Revenue', count: `$${stats.monthlyRevenue.toLocaleString()}`, icon: 'bi-currency-dollar', color: 'warning' },\n    { label: 'Pending Policies', count: stats.pendingPolicies, icon: 'bi-hourglass-split', color: 'warning' },\n    { label: 'Approved Policies', count: stats.approvedPolicies, icon: 'bi-check-circle-fill', color: 'success' },\n    { label: 'Support Tickets', count: stats.totalTickets, icon: 'bi-ticket-detailed-fill', color: 'info' },\n    { label: 'System Alerts', count: stats.systemAlerts, icon: 'bi-exclamation-triangle-fill', color: 'danger' },\n  ];\n\n  const quickActions = [\n    { title: 'Manage Users', description: 'Add, edit, or remove users', link: '/users', icon: 'bi-people' },\n    { title: 'System Settings', description: 'Configure system parameters', link: '/system-settings', icon: 'bi-gear' },\n    { title: 'View Reports', description: 'Generate and view reports', link: '/report-tool', icon: 'bi-graph-up' },\n    { title: 'Manage Staff', description: 'Employee management', link: '/staff', icon: 'bi-person-workspace' },\n  ];\n\n  return (\n    <Container className=\"mt-4\">\n      <div className=\"mb-4\">\n        <h2 className=\"fw-bold\">Admin Dashboard</h2>\n        <p className=\"lead\">\n          Welcome back, <span className=\"text-primary fw-semibold\">{user.name}</span>! \n          Here's your system overview.\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <Row className=\"g-4 mb-5\">\n        {cardData.map((item, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>\n              <Card.Body className=\"d-flex align-items-center justify-content-between\">\n                <div>\n                  <h6 className=\"card-title mb-2\">{item.label}</h6>\n                  <h4 className=\"fw-bold\">{item.count}</h4>\n                </div>\n                <i className={`bi ${item.icon} fs-2`}></i>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Quick Actions */}\n      <Row>\n        <Col>\n          <h4 className=\"fw-bold mb-4\">Quick Actions</h4>\n        </Col>\n      </Row>\n      <Row className=\"g-4 mb-5\">\n        {quickActions.map((action, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className=\"shadow-sm border-0 h-100\">\n              <Card.Body className=\"text-center\">\n                <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>\n                <h5 className=\"fw-bold\">{action.title}</h5>\n                <p className=\"text-muted mb-3\">{action.description}</p>\n                <Button variant=\"primary\" href={action.link} className=\"w-100\">\n                  Access\n                </Button>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Recent Activity */}\n      <Row>\n        <Col lg={8}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">Recent System Activity</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"list-group list-group-flush\">\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">New user registration</h6>\n                    <small className=\"text-muted\">John Doe registered as customer</small>\n                  </div>\n                  <small className=\"text-muted\">2 hours ago</small>\n                </div>\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">Policy approved</h6>\n                    <small className=\"text-muted\">Life insurance policy #LP-2024-001</small>\n                  </div>\n                  <small className=\"text-muted\">4 hours ago</small>\n                </div>\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">Support ticket resolved</h6>\n                    <small className=\"text-muted\">Ticket #ST-2024-045 closed</small>\n                  </div>\n                  <small className=\"text-muted\">6 hours ago</small>\n                </div>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col lg={4}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">System Health</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Server Status</span>\n                  <span className=\"badge bg-success\">Online</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Database</span>\n                  <span className=\"badge bg-success\">Connected</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Backup Status</span>\n                  <span className=\"badge bg-warning\">Pending</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Security Alerts</span>\n                  <span className=\"badge bg-danger\">{stats.systemAlerts}</span>\n                </div>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AACnE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC;IACjCgB,kBAAkB,EAAE,CAAC;IACrBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA1B,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAG;MACnBZ,kBAAkB,EAAE,IAAI;MACxBC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,GAAG;MACrBC,cAAc,EAAE,GAAG;MACnBC,YAAY,EAAE,GAAG;MACjBC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,MAAM;MACtBC,YAAY,EAAE;IAChB,CAAC;IACDZ,QAAQ,CAACa,YAAY,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,QAAQ,GAAG,CACf;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAEjB,KAAK,CAACW,cAAc;IAAEO,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnG;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAEjB,KAAK,CAACU,cAAc;IAAEQ,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAO,CAAC,EACtG;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAEjB,KAAK,CAACE,kBAAkB;IAAEgB,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5G;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,IAAIjB,KAAK,CAACY,cAAc,CAACQ,cAAc,CAAC,CAAC,EAAE;IAAEF,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9H;IAAEH,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAEjB,KAAK,CAACG,eAAe;IAAEe,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzG;IAAEH,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAEjB,KAAK,CAACI,gBAAgB;IAAEc,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7G;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAEjB,KAAK,CAACM,YAAY;IAAEY,IAAI,EAAE,yBAAyB;IAAEC,KAAK,EAAE;EAAO,CAAC,EACvG;IAAEH,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAEjB,KAAK,CAACa,YAAY;IAAEK,IAAI,EAAE,8BAA8B;IAAEC,KAAK,EAAE;EAAS,CAAC,CAC7G;EAED,MAAME,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,4BAA4B;IAAEC,IAAI,EAAE,QAAQ;IAAEN,IAAI,EAAE;EAAY,CAAC,EACvG;IAAEI,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,6BAA6B;IAAEC,IAAI,EAAE,kBAAkB;IAAEN,IAAI,EAAE;EAAU,CAAC,EACnH;IAAEI,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,2BAA2B;IAAEC,IAAI,EAAE,cAAc;IAAEN,IAAI,EAAE;EAAc,CAAC,EAC9G;IAAEI,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,qBAAqB;IAAEC,IAAI,EAAE,QAAQ;IAAEN,IAAI,EAAE;EAAsB,CAAC,CAC3G;EAED,oBACEtB,OAAA,CAACR,SAAS;IAACqC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACzB9B,OAAA;MAAK6B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9B,OAAA;QAAI6B,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5ClC,OAAA;QAAG6B,SAAS,EAAC,MAAM;QAAAC,QAAA,GAAC,gBACJ,eAAA9B,OAAA;UAAM6B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAE3B,IAAI,CAACgC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,kCAE7E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNlC,OAAA,CAACP,GAAG;MAACoC,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBX,QAAQ,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACtBtC,OAAA,CAACN,GAAG;QAAC6C,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChB9B,OAAA,CAACL,IAAI;UAACkC,SAAS,EAAE,yBAAyBQ,IAAI,CAACd,KAAK,mBAAoB;UAAAO,QAAA,eACtE9B,OAAA,CAACL,IAAI,CAAC8C,IAAI;YAACZ,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBACtE9B,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAI6B,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEO,IAAI,CAACjB;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjDlC,OAAA;gBAAI6B,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEO,IAAI,CAAChB;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNlC,OAAA;cAAG6B,SAAS,EAAE,MAAMQ,IAAI,CAACf,IAAI;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GATeI,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlC,OAAA,CAACP,GAAG;MAAAqC,QAAA,eACF9B,OAAA,CAACN,GAAG;QAAAoC,QAAA,eACF9B,OAAA;UAAI6B,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNlC,OAAA,CAACP,GAAG;MAACoC,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBL,YAAY,CAACW,GAAG,CAAC,CAACM,MAAM,EAAEJ,GAAG,kBAC5BtC,OAAA,CAACN,GAAG;QAAC6C,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChB9B,OAAA,CAACL,IAAI;UAACkC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACxC9B,OAAA,CAACL,IAAI,CAAC8C,IAAI;YAACZ,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAChC9B,OAAA;cAAG6B,SAAS,EAAE,MAAMa,MAAM,CAACpB,IAAI;YAA0B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DlC,OAAA;cAAI6B,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEY,MAAM,CAAChB;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3ClC,OAAA;cAAG6B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEY,MAAM,CAACf;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDlC,OAAA,CAACJ,MAAM;cAAC+C,OAAO,EAAC,SAAS;cAACC,IAAI,EAAEF,MAAM,CAACd,IAAK;cAACC,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GAVeI,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlC,OAAA,CAACP,GAAG;MAAAqC,QAAA,gBACF9B,OAAA,CAACN,GAAG;QAAC6C,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9B,OAAA,CAACL,IAAI;UAACkC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzB9B,OAAA,CAACL,IAAI,CAACkD,MAAM;YAAAf,QAAA,eACV9B,OAAA;cAAI6B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACdlC,OAAA,CAACL,IAAI,CAAC8C,IAAI;YAAAX,QAAA,eACR9B,OAAA;cAAK6B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9B,OAAA;gBAAK6B,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChF9B,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/ClC,OAAA;oBAAO6B,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACNlC,OAAA;kBAAO6B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChF9B,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzClC,OAAA;oBAAO6B,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNlC,OAAA;kBAAO6B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNlC,OAAA;gBAAK6B,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChF9B,OAAA;kBAAA8B,QAAA,gBACE9B,OAAA;oBAAI6B,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjDlC,OAAA;oBAAO6B,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNlC,OAAA;kBAAO6B,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlC,OAAA,CAACN,GAAG;QAAC6C,EAAE,EAAE,CAAE;QAAAT,QAAA,eACT9B,OAAA,CAACL,IAAI;UAACkC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzB9B,OAAA,CAACL,IAAI,CAACkD,MAAM;YAAAf,QAAA,eACV9B,OAAA;cAAI6B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACdlC,OAAA,CAACL,IAAI,CAAC8C,IAAI;YAAAX,QAAA,gBACR9B,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9B,OAAA;gBAAK6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BlC,OAAA;kBAAM6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9B,OAAA;gBAAK6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrBlC,OAAA;kBAAM6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9B,OAAA;gBAAK6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BlC,OAAA;kBAAM6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9B,OAAA;gBAAK6B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7C9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5BlC,OAAA;kBAAM6B,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAE1B,KAAK,CAACa;gBAAY;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAChC,EAAA,CA9KID,cAAc;EAAA,QACDJ,OAAO;AAAA;AAAAiD,EAAA,GADpB7C,cAAc;AAgLpB,eAAeA,cAAc;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}