{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\SubCategories.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Table, Form, Modal, Container, Row, Col, Card, Alert, Spinner } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\nimport { subCategoriesAPI, categoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SubCategories = () => {\n  _s();\n  const [username] = useState('Sushil');\n  const [subCategories, setSubCategories] = useState([]);\n  const [search, setSearch] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n\n  // Modal states\n  const [showNewModal, setShowNewModal] = useState(false);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  // New form state\n  const [newSubCategory, setNewSubCategory] = useState({\n    name: '',\n    category: '',\n    status: 'Active'\n  });\n  const categoriesList = [];\n  //  ['Health Insurance', 'Car Insurance', 'Life Insurance', 'Property Insurance']\n\n  useEffect(() => {\n    const dummyData = [\n      //   { id: 1, name: 'Medic Care', category: 'Health Insurance', status: 'Active' },\n      //   { id: 2, name: 'Term Life Insurance', category: 'Life Insurance', status: 'Active' },\n      //   { id: 3, name: 'Auto Premium', category: 'Car Insurance', status: 'Active' },\n      //   { id: 4, name: 'Homeowners Insurance', category: 'Property Insurance', status: 'Active' },\n      //   { id: 5, name: 'Comprehensive Coverage', category: 'Car Insurance', status: 'Active' },\n    ];\n    setSubCategories(dummyData);\n  }, []);\n  const filteredSubCategories = subCategories.filter(item => item.name.toLowerCase().includes(search.toLowerCase()) && (selectedCategory === 'All' || item.category === selectedCategory));\n  const handleNewInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewSubCategory(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAddSubCategory = () => {\n    const newEntry = {\n      id: subCategories.length + 1,\n      ...newSubCategory\n    };\n    setSubCategories([...subCategories, newEntry]);\n    setShowNewModal(false);\n    setNewSubCategory({\n      name: '',\n      category: '',\n      status: 'Active'\n    });\n  };\n  const handleFileUpload = () => {\n    if (selectedFile) {\n      alert(`File uploaded: ${selectedFile.name}`);\n      setShowImportModal(false);\n      setSelectedFile(null);\n    } else {\n      alert('Please select a file first.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Insurance Sub Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowNewModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowImportModal(true),\n        children: \"Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2 align-items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n        onSelect: val => setSelectedCategory(val),\n        children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n          variant: \"outline-dark\",\n          size: \"sm\",\n          children: selectedCategory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Item, {\n            eventKey: \"All\",\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), categoriesList.map((cat, idx) => /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n            eventKey: cat,\n            children: cat\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      style: {\n        maxWidth: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search SubCategories...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredSubCategories.map(sub => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: sub.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: sub.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge bg-success\",\n              children: sub.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, sub.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showNewModal,\n      onHide: () => setShowNewModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add New Sub Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Sub Category Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"name\",\n              value: newSubCategory.name,\n              onChange: handleNewInputChange,\n              placeholder: \"Enter subcategory name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"category\",\n              value: newSubCategory.category,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"-- Select Category --\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), categoriesList.map((cat, idx) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cat,\n                children: cat\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"status\",\n              value: newSubCategory.status,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowNewModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAddSubCategory,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImportModal,\n      onHide: () => setShowImportModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Import SubCategories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Select file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"file\",\n            accept: \".csv, .xlsx\",\n            onChange: e => setSelectedFile(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImportModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(SubCategories, \"HYyul/mw0cWJV8Yei3knWR2BLnc=\");\n_c = SubCategories;\nexport default SubCategories;\nvar _c;\n$RefreshReg$(_c, \"SubCategories\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Table", "Form", "Modal", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "FaPlus", "FaEdit", "FaTrash", "FaFileImport", "FaSearch", "subCategoriesAPI", "categoriesAPI", "jsxDEV", "_jsxDEV", "SubCategories", "_s", "username", "subCategories", "setSubCategories", "search", "setSearch", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showNewModal", "setShowNewModal", "showImportModal", "setShowImportModal", "selectedFile", "setSelectedFile", "newSubCategory", "setNewSubCategory", "name", "category", "status", "categoriesList", "dummyData", "filteredSubCategories", "filter", "item", "toLowerCase", "includes", "handleNewInputChange", "e", "value", "target", "prev", "handleAddSubCategory", "newEntry", "id", "length", "handleFileUpload", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "Dropdown", "onSelect", "val", "Toggle", "size", "<PERSON><PERSON>", "<PERSON><PERSON>", "eventKey", "map", "cat", "idx", "style", "max<PERSON><PERSON><PERSON>", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "sub", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "accept", "files", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/SubCategories.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport {\r\n  Button, Table, Form, Modal, Container, Row, Col, Card, Alert, Spinner\r\n} from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\r\nimport { subCategoriesAPI, categoriesAPI } from '../services/api';\r\n\r\nconst SubCategories = () => {\r\n  const [username] = useState('Sushil');\r\n  const [subCategories, setSubCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('All');\r\n\r\n  // Modal states\r\n  const [showNewModal, setShowNewModal] = useState(false);\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n\r\n  // New form state\r\n  const [newSubCategory, setNewSubCategory] = useState({\r\n    name: '',\r\n    category: '',\r\n    status: 'Active',\r\n  });\r\n\r\n   const categoriesList = [];\r\n  //  ['Health Insurance', 'Car Insurance', 'Life Insurance', 'Property Insurance']\r\n\r\n  useEffect(() => {\r\n    const dummyData = [\r\n    //   { id: 1, name: 'Medic Care', category: 'Health Insurance', status: 'Active' },\r\n    //   { id: 2, name: 'Term Life Insurance', category: 'Life Insurance', status: 'Active' },\r\n    //   { id: 3, name: 'Auto Premium', category: 'Car Insurance', status: 'Active' },\r\n    //   { id: 4, name: 'Homeowners Insurance', category: 'Property Insurance', status: 'Active' },\r\n    //   { id: 5, name: 'Comprehensive Coverage', category: 'Car Insurance', status: 'Active' },\r\n     ];\r\n    setSubCategories(dummyData);\r\n  }, []);\r\n\r\n  const filteredSubCategories = subCategories.filter((item) =>\r\n    item.name.toLowerCase().includes(search.toLowerCase()) &&\r\n    (selectedCategory === 'All' || item.category === selectedCategory)\r\n  );\r\n\r\n  const handleNewInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setNewSubCategory((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleAddSubCategory = () => {\r\n    const newEntry = {\r\n      id: subCategories.length + 1,\r\n      ...newSubCategory,\r\n    };\r\n    setSubCategories([...subCategories, newEntry]);\r\n    setShowNewModal(false);\r\n    setNewSubCategory({ name: '', category: '', status: 'Active' });\r\n  };\r\n\r\n  const handleFileUpload = () => {\r\n    if (selectedFile) {\r\n      alert(`File uploaded: ${selectedFile.name}`);\r\n      setShowImportModal(false);\r\n      setSelectedFile(null);\r\n    } else {\r\n      alert('Please select a file first.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      {/* Header */}\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h3 className=\"fw-bold text-uppercase\">Insurance Sub Categories</h3>\r\n      </div>\r\n\r\n      {/* Action buttons */}\r\n      <div className=\"mb-3 d-flex gap-2\">\r\n        <Button variant=\"primary\" onClick={() => setShowNewModal(true)}>+ New</Button>\r\n        <Button variant=\"secondary\" onClick={() => setShowImportModal(true)}>Import</Button>\r\n      </div>\r\n\r\n      {/* Export + Filter */}\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2 align-items-center\">\r\n        <Dropdown onSelect={(val) => setSelectedCategory(val)}>\r\n          <Dropdown.Toggle variant=\"outline-dark\" size=\"sm\">\r\n            {selectedCategory}\r\n          </Dropdown.Toggle>\r\n          <Dropdown.Menu>\r\n            <Dropdown.Item eventKey=\"All\">All</Dropdown.Item>\r\n            {categoriesList.map((cat, idx) => (\r\n              <Dropdown.Item key={idx} eventKey={cat}>{cat}</Dropdown.Item>\r\n            ))}\r\n          </Dropdown.Menu>\r\n        </Dropdown>\r\n\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-3\" style={{ maxWidth: '300px' }}>\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search SubCategories...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Sub Category Name</th>\r\n            <th>Category</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredSubCategories.map((sub) => (\r\n            <tr key={sub.id}>\r\n              <td>{sub.name}</td>\r\n              <td>{sub.category}</td>\r\n              <td><span className=\"badge bg-success\">{sub.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\">\r\n                  <FaEdit />\r\n                </Button>\r\n                <Button variant=\"danger\" size=\"sm\">\r\n                  <FaTrash />\r\n                </Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal - New SubCategory */}\r\n      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add New Sub Category</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Sub Category Name</Form.Label>\r\n              <Form.Control\r\n                type=\"text\"\r\n                name=\"name\"\r\n                value={newSubCategory.name}\r\n                onChange={handleNewInputChange}\r\n                placeholder=\"Enter subcategory name\"\r\n              />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Category</Form.Label>\r\n              <Form.Select\r\n                name=\"category\"\r\n                value={newSubCategory.category}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"\">-- Select Category --</option>\r\n                {categoriesList.map((cat, idx) => (\r\n                  <option key={idx} value={cat}>{cat}</option>\r\n                ))}\r\n              </Form.Select>\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Status</Form.Label>\r\n              <Form.Select\r\n                name=\"status\"\r\n                value={newSubCategory.status}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"Active\">Active</option>\r\n                <option value=\"Inactive\">Inactive</option>\r\n              </Form.Select>\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowNewModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleAddSubCategory}>\r\n            Save Changes\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal - Import File */}\r\n      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Import SubCategories</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form.Group>\r\n            <Form.Label>Select file</Form.Label>\r\n            <Form.Control\r\n              type=\"file\"\r\n              accept=\".csv, .xlsx\"\r\n              onChange={(e) => setSelectedFile(e.target.files[0])}\r\n            />\r\n          </Form.Group>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowImportModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleFileUpload}>\r\n            Upload\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubCategories;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAChE,iBAAiB;AACxB,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,gBAAgB;AAChF,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,QAAQ,CAAC;EACrC,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC;IACnDqC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EAED,MAAMC,cAAc,GAAG,EAAE;EAC1B;;EAEAzC,SAAS,CAAC,MAAM;IACd,MAAM0C,SAAS,GAAG;MAClB;MACA;MACA;MACA;MACA;IAAA,CACE;IACFjB,gBAAgB,CAACiB,SAAS,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,qBAAqB,GAAGnB,aAAa,CAACoB,MAAM,CAAEC,IAAI,IACtDA,IAAI,CAACP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,MAAM,CAACoB,WAAW,CAAC,CAAC,CAAC,KACrDlB,gBAAgB,KAAK,KAAK,IAAIiB,IAAI,CAACN,QAAQ,KAAKX,gBAAgB,CACnE,CAAC;EAED,MAAMoB,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,iBAAiB,CAAEe,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACd,IAAI,GAAGY;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE/B,aAAa,CAACgC,MAAM,GAAG,CAAC;MAC5B,GAAGpB;IACL,CAAC;IACDX,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE8B,QAAQ,CAAC,CAAC;IAC9CvB,eAAe,CAAC,KAAK,CAAC;IACtBM,iBAAiB,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC,CAAC;EACjE,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvB,YAAY,EAAE;MAChBwB,KAAK,CAAC,kBAAkBxB,YAAY,CAACI,IAAI,EAAE,CAAC;MAC5CL,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM;MACLuB,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCxC,OAAA;MAAKuC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrExC,OAAA;QAAIuC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,IAAI,CAAE;QAAA6B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9E5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC,IAAI,CAAE;QAAA2B,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7DxC,OAAA,CAAC+C,QAAQ;QAACC,QAAQ,EAAGC,GAAG,IAAKxC,mBAAmB,CAACwC,GAAG,CAAE;QAAAT,QAAA,gBACpDxC,OAAA,CAAC+C,QAAQ,CAACG,MAAM;UAACL,OAAO,EAAC,cAAc;UAACM,IAAI,EAAC,IAAI;UAAAX,QAAA,EAC9ChC;QAAgB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAClB5C,OAAA,CAAC+C,QAAQ,CAACK,IAAI;UAAAZ,QAAA,gBACZxC,OAAA,CAAC+C,QAAQ,CAACM,IAAI;YAACC,QAAQ,EAAC,KAAK;YAAAd,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EAChDvB,cAAc,CAACkC,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBAC3BzD,OAAA,CAAC+C,QAAQ,CAACM,IAAI;YAAWC,QAAQ,EAAEE,GAAI;YAAAhB,QAAA,EAAEgB;UAAG,GAAxBC,GAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqC,CAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEX5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,mBAAmB;QAACM,IAAI,EAAC,IAAI;QAAAX,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3D5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,mBAAmB;QAACM,IAAI,EAAC,IAAI;QAAAX,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,mBAAmB;QAACM,IAAI,EAAC,IAAI;QAAAX,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5D5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,mBAAmB;QAACM,IAAI,EAAC,IAAI;QAAAX,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D5C,OAAA,CAAClB,MAAM;QAAC+D,OAAO,EAAC,mBAAmB;QAACM,IAAI,EAAC,IAAI;QAAAX,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,MAAM;MAACmB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ,CAAE;MAAAnB,QAAA,eACjDxC,OAAA,CAAChB,IAAI,CAAC4E,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,yBAAyB;QACrChC,KAAK,EAAExB,MAAO;QACdyD,QAAQ,EAAGlC,CAAC,IAAKtB,SAAS,CAACsB,CAAC,CAACE,MAAM,CAACD,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5C,OAAA,CAACjB,KAAK;MAACiF,QAAQ;MAACC,KAAK;MAACC,UAAU;MAAC3B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDxC,OAAA;QAAOuC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAAwC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B5C,OAAA;YAAAwC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB5C,OAAA;YAAAwC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5C,OAAA;YAAAwC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5C,OAAA;QAAAwC,QAAA,EACGjB,qBAAqB,CAACgC,GAAG,CAAEY,GAAG,iBAC7BnE,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAAwC,QAAA,EAAK2B,GAAG,CAACjD;UAAI;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnB5C,OAAA;YAAAwC,QAAA,EAAK2B,GAAG,CAAChD;UAAQ;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5C,OAAA;YAAAwC,QAAA,eAAIxC,OAAA;cAAMuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE2B,GAAG,CAAC/C;YAAM;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D5C,OAAA;YAAAwC,QAAA,gBACExC,OAAA,CAAClB,MAAM;cAAC+D,OAAO,EAAC,SAAS;cAACM,IAAI,EAAC,IAAI;cAACZ,SAAS,EAAC,MAAM;cAAAC,QAAA,eAClDxC,OAAA,CAACP,MAAM;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACT5C,OAAA,CAAClB,MAAM;cAAC+D,OAAO,EAAC,QAAQ;cAACM,IAAI,EAAC,IAAI;cAAAX,QAAA,eAChCxC,OAAA,CAACN,OAAO;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAXEuB,GAAG,CAAChC,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGR5C,OAAA,CAACf,KAAK;MAACmF,IAAI,EAAE1D,YAAa;MAAC2D,MAAM,EAAEA,CAAA,KAAM1D,eAAe,CAAC,KAAK,CAAE;MAAC2D,QAAQ;MAAA9B,QAAA,gBACvExC,OAAA,CAACf,KAAK,CAACsF,MAAM;QAACC,WAAW;QAAAhC,QAAA,eACvBxC,OAAA,CAACf,KAAK,CAACwF,KAAK;UAAAjC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACf5C,OAAA,CAACf,KAAK,CAACyF,IAAI;QAAAlC,QAAA,eACTxC,OAAA,CAAChB,IAAI;UAAAwD,QAAA,gBACHxC,OAAA,CAAChB,IAAI,CAAC2F,KAAK;YAACpC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAAChB,IAAI,CAAC4F,KAAK;cAAApC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1C5C,OAAA,CAAChB,IAAI,CAAC4E,OAAO;cACXC,IAAI,EAAC,MAAM;cACX3C,IAAI,EAAC,MAAM;cACXY,KAAK,EAAEd,cAAc,CAACE,IAAK;cAC3B6C,QAAQ,EAAEnC,oBAAqB;cAC/BkC,WAAW,EAAC;YAAwB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb5C,OAAA,CAAChB,IAAI,CAAC2F,KAAK;YAACpC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAAChB,IAAI,CAAC4F,KAAK;cAAApC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjC5C,OAAA,CAAChB,IAAI,CAAC6F,MAAM;cACV3D,IAAI,EAAC,UAAU;cACfY,KAAK,EAAEd,cAAc,CAACG,QAAS;cAC/B4C,QAAQ,EAAEnC,oBAAqB;cAAAY,QAAA,gBAE/BxC,OAAA;gBAAQ8B,KAAK,EAAC,EAAE;gBAAAU,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CvB,cAAc,CAACkC,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBAC3BzD,OAAA;gBAAkB8B,KAAK,EAAE0B,GAAI;gBAAAhB,QAAA,EAAEgB;cAAG,GAArBC,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA2B,CAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACb5C,OAAA,CAAChB,IAAI,CAAC2F,KAAK;YAACpC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAAChB,IAAI,CAAC4F,KAAK;cAAApC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/B5C,OAAA,CAAChB,IAAI,CAAC6F,MAAM;cACV3D,IAAI,EAAC,QAAQ;cACbY,KAAK,EAAEd,cAAc,CAACI,MAAO;cAC7B2C,QAAQ,EAAEnC,oBAAqB;cAAAY,QAAA,gBAE/BxC,OAAA;gBAAQ8B,KAAK,EAAC,QAAQ;gBAAAU,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC5C,OAAA;gBAAQ8B,KAAK,EAAC,UAAU;gBAAAU,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb5C,OAAA,CAACf,KAAK,CAAC6F,MAAM;QAAAtC,QAAA,gBACXxC,OAAA,CAAClB,MAAM;UAAC+D,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,KAAK,CAAE;UAAA6B,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5C,OAAA,CAAClB,MAAM;UAAC+D,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEb,oBAAqB;UAAAO,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR5C,OAAA,CAACf,KAAK;MAACmF,IAAI,EAAExD,eAAgB;MAACyD,MAAM,EAAEA,CAAA,KAAMxD,kBAAkB,CAAC,KAAK,CAAE;MAACyD,QAAQ;MAAA9B,QAAA,gBAC7ExC,OAAA,CAACf,KAAK,CAACsF,MAAM;QAACC,WAAW;QAAAhC,QAAA,eACvBxC,OAAA,CAACf,KAAK,CAACwF,KAAK;UAAAjC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACf5C,OAAA,CAACf,KAAK,CAACyF,IAAI;QAAAlC,QAAA,eACTxC,OAAA,CAAChB,IAAI,CAAC2F,KAAK;UAAAnC,QAAA,gBACTxC,OAAA,CAAChB,IAAI,CAAC4F,KAAK;YAAApC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC5C,OAAA,CAAChB,IAAI,CAAC4E,OAAO;YACXC,IAAI,EAAC,MAAM;YACXkB,MAAM,EAAC,aAAa;YACpBhB,QAAQ,EAAGlC,CAAC,IAAKd,eAAe,CAACc,CAAC,CAACE,MAAM,CAACiD,KAAK,CAAC,CAAC,CAAC;UAAE;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACb5C,OAAA,CAACf,KAAK,CAAC6F,MAAM;QAAAtC,QAAA,gBACXxC,OAAA,CAAClB,MAAM;UAAC+D,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC,KAAK,CAAE;UAAA2B,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5C,OAAA,CAAClB,MAAM;UAAC+D,OAAO,EAAC,SAAS;UAACC,OAAO,EAAET,gBAAiB;UAAAG,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAtNID,aAAa;AAAAgF,EAAA,GAAbhF,aAAa;AAwNnB,eAAeA,aAAa;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}