{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Component\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaBars, FaUser, FaSignOutAlt, FaCog } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport NotificationCenter from '../components/NotificationCenter';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  toggleSidebar\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const handleLogin = () => {\n    navigate('/login');\n  };\n  const handleRegister = () => {\n    navigate('/register');\n  };\n  return /*#__PURE__*/_jsxDEV(BootstrapNavbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    fixed: \"top\",\n    className: \"shadow-sm\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-light\",\n        onClick: toggleSidebar,\n        className: \"me-3\",\n        style: {\n          border: 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Brand, {\n        as: Link,\n        to: \"/dashboard\",\n        className: \"fw-bold\",\n        children: \"Morya Insurance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/dashboard\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/about\",\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/services\",\n            children: \"Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/contact\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"ms-auto\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(NotificationCenter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 23\n                }, this), user.firstName, \" (\", user.role, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 21\n              }, this),\n              id: \"user-dropdown\",\n              align: \"end\",\n              children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                as: Link,\n                to: \"/profile\",\n                children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 21\n                }, this), \"Profile\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                as: Link,\n                to: \"/settings\",\n                children: [/*#__PURE__*/_jsxDEV(FaCog, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: handleLogout,\n                children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-light\",\n              onClick: handleLogin,\n              className: \"me-2\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"light\",\n              onClick: handleRegister,\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"EsGDKgL54E/wOPDVzAaSuv67qJE=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "FaBars", "FaUser", "FaSignOutAlt", "FaCog", "useAuth", "NotificationCenter", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "toggleSidebar", "_s", "navigate", "user", "logout", "isAuthenticated", "handleLogout", "handleLogin", "handleRegister", "bg", "variant", "expand", "fixed", "className", "children", "fluid", "onClick", "style", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Brand", "as", "to", "Toggle", "Collapse", "id", "title", "firstName", "role", "align", "<PERSON><PERSON>", "Divider", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Component/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaBars, FaUser, FaSignOutAlt, FaCog } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport NotificationCenter from '../components/NotificationCenter';\n\nconst Navbar = ({ toggleSidebar }) => {\n  const navigate = useNavigate();\n  const { user, logout, isAuthenticated } = useAuth();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const handleLogin = () => {\n    navigate('/login');\n  };\n\n  const handleRegister = () => {\n    navigate('/register');\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" fixed=\"top\" className=\"shadow-sm\">\n      <Container fluid>\n        {/* Sidebar Toggle Button */}\n        <Button\n          variant=\"outline-light\"\n          onClick={toggleSidebar}\n          className=\"me-3\"\n          style={{ border: 'none' }}\n        >\n          <FaBars />\n        </Button>\n\n        {/* Brand */}\n        <BootstrapNavbar.Brand as={Link} to=\"/dashboard\" className=\"fw-bold\">\n          Morya Insurance\n        </BootstrapNavbar.Brand>\n\n        {/* Toggle for mobile */}\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          {/* Main Navigation Links */}\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/dashboard\">Home</Nav.Link>\n            <Nav.Link as={Link} to=\"/about\">About</Nav.Link>\n            <Nav.Link as={Link} to=\"/services\">Services</Nav.Link>\n            <Nav.Link as={Link} to=\"/contact\">Contact</Nav.Link>\n          </Nav>\n\n          {/* User Authentication Section */}\n          <Nav className=\"ms-auto\">\n            {isAuthenticated() ? (\n              <>\n                {/* Notification Center */}\n                <NotificationCenter />\n\n                {/* User Dropdown */}\n                <NavDropdown\n                  title={\n                    <span>\n                      <FaUser className=\"me-1\" />\n                      {user.firstName} ({user.role})\n                    </span>\n                  }\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item as={Link} to=\"/profile\">\n                    <FaUser className=\"me-2\" />\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item as={Link} to=\"/settings\">\n                    <FaCog className=\"me-2\" />\n                    Settings\n                  </NavDropdown.Item>\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    <FaSignOutAlt className=\"me-2\" />\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n              </>\n            ) : (\n              <>\n                <Button\n                  variant=\"outline-light\"\n                  onClick={handleLogin}\n                  className=\"me-2\"\n                >\n                  Login\n                </Button>\n                <Button\n                  variant=\"light\"\n                  onClick={handleRegister}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,eAAe,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AAChG,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,KAAK,QAAQ,gBAAgB;AACpE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMjB,MAAM,GAAGA,CAAC;EAAEkB;AAAc,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EAEnD,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBF,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBL,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3BN,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,oBACEL,OAAA,CAACd,eAAe;IAAC0B,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAACC,KAAK,EAAC,KAAK;IAACC,SAAS,EAAC,WAAW;IAAAC,QAAA,eACrFjB,OAAA,CAACX,SAAS;MAAC6B,KAAK;MAAAD,QAAA,gBAEdjB,OAAA,CAACV,MAAM;QACLuB,OAAO,EAAC,eAAe;QACvBM,OAAO,EAAEhB,aAAc;QACvBa,SAAS,EAAC,MAAM;QAChBI,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAJ,QAAA,eAE1BjB,OAAA,CAACP,MAAM;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGTzB,OAAA,CAACd,eAAe,CAACwC,KAAK;QAACC,EAAE,EAAEpC,IAAK;QAACqC,EAAE,EAAC,YAAY;QAACZ,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAErE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuB,CAAC,eAGxBzB,OAAA,CAACd,eAAe,CAAC2C,MAAM;QAAC,iBAAc;MAAkB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3DzB,OAAA,CAACd,eAAe,CAAC4C,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAd,QAAA,gBAE7CjB,OAAA,CAACb,GAAG;UAAC6B,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtBjB,OAAA,CAACb,GAAG,CAACI,IAAI;YAACoC,EAAE,EAAEpC,IAAK;YAACqC,EAAE,EAAC,YAAY;YAAAX,QAAA,EAAC;UAAI;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACnDzB,OAAA,CAACb,GAAG,CAACI,IAAI;YAACoC,EAAE,EAAEpC,IAAK;YAACqC,EAAE,EAAC,QAAQ;YAAAX,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAChDzB,OAAA,CAACb,GAAG,CAACI,IAAI;YAACoC,EAAE,EAAEpC,IAAK;YAACqC,EAAE,EAAC,WAAW;YAAAX,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtDzB,OAAA,CAACb,GAAG,CAACI,IAAI;YAACoC,EAAE,EAAEpC,IAAK;YAACqC,EAAE,EAAC,UAAU;YAAAX,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAGNzB,OAAA,CAACb,GAAG;UAAC6B,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrBT,eAAe,CAAC,CAAC,gBAChBR,OAAA,CAAAE,SAAA;YAAAe,QAAA,gBAEEjB,OAAA,CAACF,kBAAkB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGtBzB,OAAA,CAACZ,WAAW;cACV4C,KAAK,eACHhC,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA,CAACN,MAAM;kBAACsB,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC1BnB,IAAI,CAAC2B,SAAS,EAAC,IAAE,EAAC3B,IAAI,CAAC4B,IAAI,EAAC,GAC/B;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;cACDM,EAAE,EAAC,eAAe;cAClBI,KAAK,EAAC,KAAK;cAAAlB,QAAA,gBAEXjB,OAAA,CAACZ,WAAW,CAACgD,IAAI;gBAACT,EAAE,EAAEpC,IAAK;gBAACqC,EAAE,EAAC,UAAU;gBAAAX,QAAA,gBACvCjB,OAAA,CAACN,MAAM;kBAACsB,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBzB,OAAA,CAACZ,WAAW,CAACgD,IAAI;gBAACT,EAAE,EAAEpC,IAAK;gBAACqC,EAAE,EAAC,WAAW;gBAAAX,QAAA,gBACxCjB,OAAA,CAACJ,KAAK;kBAACoB,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAE5B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBzB,OAAA,CAACZ,WAAW,CAACiD,OAAO;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvBzB,OAAA,CAACZ,WAAW,CAACgD,IAAI;gBAACjB,OAAO,EAAEV,YAAa;gBAAAQ,QAAA,gBACtCjB,OAAA,CAACL,YAAY;kBAACqB,SAAS,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,eACd,CAAC,gBAEHzB,OAAA,CAAAE,SAAA;YAAAe,QAAA,gBACEjB,OAAA,CAACV,MAAM;cACLuB,OAAO,EAAC,eAAe;cACvBM,OAAO,EAAET,WAAY;cACrBM,SAAS,EAAC,MAAM;cAAAC,QAAA,EACjB;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzB,OAAA,CAACV,MAAM;cACLuB,OAAO,EAAC,OAAO;cACfM,OAAO,EAAER,cAAe;cAAAM,QAAA,EACzB;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACrB,EAAA,CAtGInB,MAAM;EAAA,QACOO,WAAW,EACcK,OAAO;AAAA;AAAAyC,EAAA,GAF7CrD,MAAM;AAwGZ,eAAeA,MAAM;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}