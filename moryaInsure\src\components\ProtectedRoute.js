import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Container, Row, Col, Alert } from 'react-bootstrap';

const ProtectedRoute = ({ children, requiredRoles = [], requireAuth = true }) => {
  const { user, isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-3"></div>
          <p>Loading...</p>
        </div>
      </Container>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated()) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If specific roles are required
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.includes(user.role);
    
    if (!hasRequiredRole) {
      return (
        <Container className="mt-5">
          <Row className="justify-content-center">
            <Col md={8}>
              <Alert variant="danger" className="text-center">
                <h4>Access Denied</h4>
                <p>You don't have permission to access this page.</p>
                <p>Required roles: {requiredRoles.join(', ')}</p>
                <p>Your role: {user.role}</p>
              </Alert>
            </Col>
          </Row>
        </Container>
      );
    }
  }

  return children;
};

export default ProtectedRoute;
