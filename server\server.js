const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined'));

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/insurance23jun');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    console.log('Continuing without database connection for testing...');
  }
};

// Connect to database (non-blocking for testing)
connectDB();

// Socket.IO configuration
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    methods: ['GET', 'POST']
  }
});

// Socket.IO authentication middleware
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication error'));
  }

  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.userId;
    socket.join(`user_${decoded.userId}`);
    next();
  } catch (error) {
    next(new Error('Authentication error'));
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`User ${socket.userId} connected`);

  socket.on('disconnect', () => {
    console.log(`User ${socket.userId} disconnected`);
  });

  // Handle real-time events
  socket.on('join_room', (room) => {
    socket.join(room);
  });

  socket.on('leave_room', (room) => {
    socket.leave(room);
  });
});

// Set Socket.IO instance for notification service
const notificationService = require('./services/notificationService');
notificationService.setSocketIO(io);

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Routes - Load one by one to identify issues
try {
  app.use('/api/auth', require('./routes/auth'));
  console.log('✓ Auth routes loaded');
} catch (error) {
  console.error('✗ Error loading auth routes:', error.message);
}

try {
  app.use('/api/users', require('./routes/users'));
  console.log('✓ Users routes loaded');
} catch (error) {
  console.error('✗ Error loading users routes:', error.message);
}

try {
  app.use('/api/policies', require('./routes/policies'));
  console.log('✓ Policies routes loaded');
} catch (error) {
  console.error('✗ Error loading policies routes:', error.message);
}

try {
  app.use('/api/categories', require('./routes/categories'));
  console.log('✓ Categories routes loaded');
} catch (error) {
  console.error('✗ Error loading categories routes:', error.message);
}

try {
  app.use('/api/tickets', require('./routes/tickets'));
  console.log('✓ Tickets routes loaded');
} catch (error) {
  console.error('✗ Error loading tickets routes:', error.message);
}

try {
  app.use('/api/reports', require('./routes/reports'));
  console.log('✓ Reports routes loaded');
} catch (error) {
  console.error('✗ Error loading reports routes:', error.message);
}

try {
  app.use('/api/claims', require('./routes/claims'));
  console.log('✓ Claims routes loaded');
} catch (error) {
  console.error('✗ Error loading claims routes:', error.message);
}

try {
  app.use('/api/notifications', require('./routes/notifications'));
  console.log('✓ Notifications routes loaded');
} catch (error) {
  console.error('✗ Error loading notifications routes:', error.message);
}

try {
  app.use('/api/uploads', require('./routes/uploads'));
  console.log('✓ Upload routes loaded');
} catch (error) {
  console.error('✗ Error loading upload routes:', error.message);
}

try {
  app.use('/api/settings', require('./routes/settings'));
  console.log('✓ Settings routes loaded');
} catch (error) {
  console.error('✗ Error loading settings routes:', error.message);
}

try {
  app.use('/api/subcategories', require('./routes/subcategories'));
  console.log('✓ Subcategories routes loaded');
} catch (error) {
  console.error('✗ Error loading subcategories routes:', error.message);
}

try {
  app.use('/api/ticket-categories', require('./routes/ticketCategories'));
  console.log('✓ Ticket categories routes loaded');
} catch (error) {
  console.error('✗ Error loading ticket categories routes:', error.message);
}

try {
  app.use('/api/policy-holders', require('./routes/policyHolders'));
  console.log('✓ Policy holders routes loaded');
} catch (error) {
  console.error('✗ Error loading policy holders routes:', error.message);
}

try {
  app.use('/api/tasks', require('./routes/tasks'));
  console.log('✓ Tasks routes loaded');
} catch (error) {
  console.error('✗ Error loading tasks routes:', error.message);
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Morya Insurance API is running',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(e => e.message);
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    });
  }
  
  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return res.status(400).json({
      success: false,
      message: `${field} already exists`
    });
  }
  
  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }
  
  // Default error
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

const PORT = process.env.PORT || 5002;

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Socket.IO enabled for real-time features`);
});
