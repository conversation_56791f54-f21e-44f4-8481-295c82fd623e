{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\ConnectionStatus.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FaWifi, FaExclamationTriangle } from 'react-icons/fa';\nimport { useRealtime } from '../contexts/RealtimeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ConnectionStatus = () => {\n  _s();\n  const {\n    isConnected\n  } = useRealtime();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `connection-status ${isConnected ? 'connected' : 'disconnected'}`,\n    children: isConnected ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(FaWifi, {\n        className: \"me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 11\n      }, this), \"Real-time Connected\"]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(FaWifiSlash, {\n        className: \"me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 11\n      }, this), \"Disconnected\"]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(ConnectionStatus, \"FrcVwiLF2SCdpzsBBlSsSDVXa5I=\", false, function () {\n  return [useRealtime];\n});\n_c = ConnectionStatus;\nexport default ConnectionStatus;\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");", "map": {"version": 3, "names": ["React", "FaWifi", "FaExclamationTriangle", "useRealtime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ConnectionStatus", "_s", "isConnected", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaWifiSlash", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/ConnectionStatus.js"], "sourcesContent": ["import React from 'react';\nimport { FaWifi, FaExclamationTriangle } from 'react-icons/fa';\nimport { useRealtime } from '../contexts/RealtimeContext';\n\nconst ConnectionStatus = () => {\n  const { isConnected } = useRealtime();\n\n  return (\n    <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>\n      {isConnected ? (\n        <>\n          <FaWifi className=\"me-2\" />\n          Real-time Connected\n        </>\n      ) : (\n        <>\n          <FaWifiSlash className=\"me-2\" />\n          Disconnected\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default ConnectionStatus;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,qBAAqB,QAAQ,gBAAgB;AAC9D,SAASC,WAAW,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAY,CAAC,GAAGP,WAAW,CAAC,CAAC;EAErC,oBACEE,OAAA;IAAKM,SAAS,EAAE,qBAAqBD,WAAW,GAAG,WAAW,GAAG,cAAc,EAAG;IAAAE,QAAA,EAC/EF,WAAW,gBACVL,OAAA,CAAAE,SAAA;MAAAK,QAAA,gBACEP,OAAA,CAACJ,MAAM;QAACU,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,uBAE7B;IAAA,eAAE,CAAC,gBAEHX,OAAA,CAAAE,SAAA;MAAAK,QAAA,gBACEP,OAAA,CAACY,WAAW;QAACN,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElC;IAAA,eAAE;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACP,EAAA,CAlBID,gBAAgB;EAAA,QACIL,WAAW;AAAA;AAAAe,EAAA,GAD/BV,gBAAgB;AAoBtB,eAAeA,gBAAgB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}