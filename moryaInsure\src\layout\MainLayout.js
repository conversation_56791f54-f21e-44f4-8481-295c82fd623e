import React, { useState } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "../Component/Sidebar";
import Navbar from "../Component/Navbar";
import Footer from "../Component/Footer";

const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="d-flex flex-column min-vh-100">
      <Navbar toggleSidebar={toggleSidebar} />
      <div className="d-flex flex-grow-1 position-relative">
        <Sidebar collapsed={sidebarCollapsed} />
        <div
          className="flex-grow-1 p-4 main-content"
          style={{
            marginLeft: sidebarCollapsed ? "70px" : "260px",
            transition: "margin-left 0.3s ease",
            paddingTop: "20px"
          }}
        >
          <Outlet />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default MainLayout;
