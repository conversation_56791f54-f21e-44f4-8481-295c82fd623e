{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\Categories.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Table, Form, Modal, Alert, Spinner, Container, Row, Col, Card } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\nimport { categoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesPage = () => {\n  _s();\n  const [username] = useState('');\n  const [categories, setCategories] = useState([]);\n  const [search, setSearch] = useState('');\n\n  // New category modal states\n  const [showNewModal, setShowNewModal] = useState(false);\n  const [newCategory, setNewCategory] = useState({\n    categoryName: '',\n    status: 'Active'\n  });\n\n  // Import modal states\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  useEffect(() => {\n    const dummyData = [\n      //   { id: 1, categoryName: 'Health Insurance', status: 'Active' },\n      //   { id: 2, categoryName: 'Car Insurance', status: 'Active' },\n      //   { id: 3, categoryName: 'Life Insurance', status: 'Active' },\n      //   { id: 4, categoryName: 'Property Insurance', status: 'Active' },\n    ];\n    setCategories(dummyData);\n  }, []);\n  const filteredCategories = categories.filter(cat => cat.categoryName.toLowerCase().includes(search.toLowerCase()));\n\n  // Handlers for new category modal\n  const handleNewInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCategory(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAddCategory = () => {\n    const newEntry = {\n      id: categories.length + 1,\n      ...newCategory\n    };\n    setCategories([...categories, newEntry]);\n    setNewCategory({\n      categoryName: '',\n      status: 'Active'\n    });\n    setShowNewModal(false);\n  };\n\n  // Handlers for import modal\n  const handleFileChange = e => {\n    setSelectedFile(e.target.files[0]);\n  };\n  const handleFileUpload = () => {\n    if (selectedFile) {\n      alert(`Uploaded file: ${selectedFile.name}`);\n      setSelectedFile(null);\n      setShowImportModal(false);\n    } else {\n      alert('Please select a file first.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Insurance Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowNewModal(true),\n        children: \"New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowImportModal(true),\n        children: \"Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 w-100 w-md-50\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search categories...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredCategories.map(cat => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: cat.categoryName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge bg-success\",\n              children: cat.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this)]\n        }, cat.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showNewModal,\n      onHide: () => setShowNewModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add New Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Category Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"categoryName\",\n              placeholder: \"Enter category name\",\n              value: newCategory.categoryName,\n              onChange: handleNewInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"status\",\n              value: newCategory.status,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowNewModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAddCategory,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImportModal,\n      onHide: () => setShowImportModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Import Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select file (CSV, Excel)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel\",\n              onChange: handleFileChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImportModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesPage, \"DKRM2uUob2Q8KdbZf/+bsmp0k38=\");\n_c = CategoriesPage;\nexport default CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Table", "Form", "Modal", "<PERSON><PERSON>", "Spinner", "Container", "Row", "Col", "Card", "FaPlus", "FaEdit", "FaTrash", "FaFileImport", "FaSearch", "categoriesAPI", "jsxDEV", "_jsxDEV", "CategoriesPage", "_s", "username", "categories", "setCategories", "search", "setSearch", "showNewModal", "setShowNewModal", "newCategory", "setNewCategory", "categoryName", "status", "showImportModal", "setShowImportModal", "selectedFile", "setSelectedFile", "dummyData", "filteredCategories", "filter", "cat", "toLowerCase", "includes", "handleNewInputChange", "e", "name", "value", "target", "prev", "handleAddCategory", "newEntry", "id", "length", "handleFileChange", "files", "handleFileUpload", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "accept", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/Categories.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { Button, Table, Form, Modal, <PERSON><PERSON>, Spinner, Container, Row, Col, Card } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\r\nimport { categoriesAPI } from '../services/api';\r\n\r\nconst CategoriesPage = () => {\r\n  const [username] = useState('');\r\n  const [categories, setCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n\r\n  // New category modal states\r\n  const [showNewModal, setShowNewModal] = useState(false);\r\n  const [newCategory, setNewCategory] = useState({\r\n    categoryName: '',\r\n    status: 'Active',\r\n  });\r\n\r\n  // Import modal states\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const dummyData = [\r\n    //   { id: 1, categoryName: 'Health Insurance', status: 'Active' },\r\n    //   { id: 2, categoryName: 'Car Insurance', status: 'Active' },\r\n    //   { id: 3, categoryName: 'Life Insurance', status: 'Active' },\r\n    //   { id: 4, categoryName: 'Property Insurance', status: 'Active' },\r\n     ];\r\n    setCategories(dummyData);\r\n  }, []);\r\n\r\n  const filteredCategories = categories.filter((cat) =>\r\n    cat.categoryName.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  // Handlers for new category modal\r\n  const handleNewInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setNewCategory((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleAddCategory = () => {\r\n    const newEntry = {\r\n      id: categories.length + 1,\r\n      ...newCategory,\r\n    };\r\n    setCategories([...categories, newEntry]);\r\n    setNewCategory({ categoryName: '', status: 'Active' });\r\n    setShowNewModal(false);\r\n  };\r\n\r\n  // Handlers for import modal\r\n  const handleFileChange = (e) => {\r\n    setSelectedFile(e.target.files[0]);\r\n  };\r\n\r\n  const handleFileUpload = () => {\r\n    if (selectedFile) {\r\n      alert(`Uploaded file: ${selectedFile.name}`);\r\n      setSelectedFile(null);\r\n      setShowImportModal(false);\r\n    } else {\r\n      alert('Please select a file first.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      {/* Header */}\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h3 className=\"fw-bold text-uppercase\">Insurance Category</h3>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"mb-3 d-flex gap-2\">\r\n        <Button variant=\"primary\" onClick={() => setShowNewModal(true)}>New</Button>\r\n        <Button variant=\"secondary\" onClick={() => setShowImportModal(true)}>Import</Button>\r\n      </div>\r\n\r\n      {/* Export Buttons */}\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-3 w-100 w-md-50\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search categories...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Category Name</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredCategories.map((cat) => (\r\n            <tr key={cat.id}>\r\n              <td>{cat.categoryName}</td>\r\n              <td><span className=\"badge bg-success\">{cat.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\">\r\n                  <FaEdit />\r\n                </Button>\r\n                <Button variant=\"danger\" size=\"sm\">\r\n                  <FaTrash />\r\n                </Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal - New Category */}\r\n      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add New Category</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Category Name</Form.Label>\r\n              <Form.Control\r\n                type=\"text\"\r\n                name=\"categoryName\"\r\n                placeholder=\"Enter category name\"\r\n                value={newCategory.categoryName}\r\n                onChange={handleNewInputChange}\r\n              />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Status</Form.Label>\r\n              <Form.Select\r\n                name=\"status\"\r\n                value={newCategory.status}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"Active\">Active</option>\r\n                <option value=\"Inactive\">Inactive</option>\r\n              </Form.Select>\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowNewModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleAddCategory}>\r\n            Save Changes\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal - Import File */}\r\n      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Import Categories</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group>\r\n              <Form.Label>Select file (CSV, Excel)</Form.Label>\r\n              <Form.Control type=\"file\" accept=\".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel\" onChange={handleFileChange} />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowImportModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleFileUpload}>\r\n            Upload\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoriesPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,iBAAiB;AACvG,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,gBAAgB;AAChF,SAASC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAExC;EACA,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC;IAC7C8B,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAEtDD,SAAS,CAAC,MAAM;IACd,MAAMqC,SAAS,GAAG;MAClB;MACA;MACA;MACA;IAAA,CACE;IACFb,aAAa,CAACa,SAAS,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGf,UAAU,CAACgB,MAAM,CAAEC,GAAG,IAC/CA,GAAG,CAACT,YAAY,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACjB,MAAM,CAACgB,WAAW,CAAC,CAAC,CAC9D,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCjB,cAAc,CAAEkB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE5B,UAAU,CAAC6B,MAAM,GAAG,CAAC;MACzB,GAAGvB;IACL,CAAC;IACDL,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE2B,QAAQ,CAAC,CAAC;IACxCpB,cAAc,CAAC;MAAEC,YAAY,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC,CAAC;IACtDJ,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMyB,gBAAgB,GAAIT,CAAC,IAAK;IAC9BR,eAAe,CAACQ,CAAC,CAACG,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,YAAY,EAAE;MAChBqB,KAAK,CAAC,kBAAkBrB,YAAY,CAACU,IAAI,EAAE,CAAC;MAC5CT,eAAe,CAAC,IAAI,CAAC;MACrBF,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLsB,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,oBACErC,OAAA;IAAKsC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCvC,OAAA;MAAKsC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEvC,OAAA;QAAIsC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvC,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAAC,IAAI,CAAE;QAAA8B,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5E3C,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAAC,IAAI,CAAE;QAAAwB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CvC,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3D3C,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D3C,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5D3C,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D3C,OAAA,CAACjB,MAAM;QAAC6D,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCvC,OAAA,CAACf,IAAI,CAAC8D,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,sBAAsB;QAClCtB,KAAK,EAAErB,MAAO;QACd4C,QAAQ,EAAGzB,CAAC,IAAKlB,SAAS,CAACkB,CAAC,CAACG,MAAM,CAACD,KAAK;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3C,OAAA,CAAChB,KAAK;MAACmE,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACf,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDvC,OAAA;QAAOsC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAAuC,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB3C,OAAA;YAAAuC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf3C,OAAA;YAAAuC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR3C,OAAA;QAAAuC,QAAA,EACGpB,kBAAkB,CAACmC,GAAG,CAAEjC,GAAG,iBAC1BrB,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAAuC,QAAA,EAAKlB,GAAG,CAACT;UAAY;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3B3C,OAAA;YAAAuC,QAAA,eAAIvC,OAAA;cAAMsC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAElB,GAAG,CAACR;YAAM;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D3C,OAAA;YAAAuC,QAAA,gBACEvC,OAAA,CAACjB,MAAM;cAAC6D,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAClDvC,OAAA,CAACN,MAAM;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACT3C,OAAA,CAACjB,MAAM;cAAC6D,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAChCvC,OAAA,CAACL,OAAO;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAVEtB,GAAG,CAACW,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGR3C,OAAA,CAACd,KAAK;MAACqE,IAAI,EAAE/C,YAAa;MAACgD,MAAM,EAAEA,CAAA,KAAM/C,eAAe,CAAC,KAAK,CAAE;MAACgD,QAAQ;MAAAlB,QAAA,gBACvEvC,OAAA,CAACd,KAAK,CAACwE,MAAM;QAACC,WAAW;QAAApB,QAAA,eACvBvC,OAAA,CAACd,KAAK,CAAC0E,KAAK;UAAArB,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACf3C,OAAA,CAACd,KAAK,CAAC2E,IAAI;QAAAtB,QAAA,eACTvC,OAAA,CAACf,IAAI;UAAAsD,QAAA,gBACHvC,OAAA,CAACf,IAAI,CAAC6E,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BvC,OAAA,CAACf,IAAI,CAAC8E,KAAK;cAAAxB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtC3C,OAAA,CAACf,IAAI,CAAC8D,OAAO;cACXC,IAAI,EAAC,MAAM;cACXtB,IAAI,EAAC,cAAc;cACnBuB,WAAW,EAAC,qBAAqB;cACjCtB,KAAK,EAAEjB,WAAW,CAACE,YAAa;cAChCsC,QAAQ,EAAE1B;YAAqB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb3C,OAAA,CAACf,IAAI,CAAC6E,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BvC,OAAA,CAACf,IAAI,CAAC8E,KAAK;cAAAxB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/B3C,OAAA,CAACf,IAAI,CAAC+E,MAAM;cACVtC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEjB,WAAW,CAACG,MAAO;cAC1BqC,QAAQ,EAAE1B,oBAAqB;cAAAe,QAAA,gBAE/BvC,OAAA;gBAAQ2B,KAAK,EAAC,QAAQ;gBAAAY,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC3C,OAAA;gBAAQ2B,KAAK,EAAC,UAAU;gBAAAY,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb3C,OAAA,CAACd,KAAK,CAAC+E,MAAM;QAAA1B,QAAA,gBACXvC,OAAA,CAACjB,MAAM;UAAC6D,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAAC,KAAK,CAAE;UAAA8B,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA,CAACjB,MAAM;UAAC6D,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEf,iBAAkB;UAAAS,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR3C,OAAA,CAACd,KAAK;MAACqE,IAAI,EAAEzC,eAAgB;MAAC0C,MAAM,EAAEA,CAAA,KAAMzC,kBAAkB,CAAC,KAAK,CAAE;MAAC0C,QAAQ;MAAAlB,QAAA,gBAC7EvC,OAAA,CAACd,KAAK,CAACwE,MAAM;QAACC,WAAW;QAAApB,QAAA,eACvBvC,OAAA,CAACd,KAAK,CAAC0E,KAAK;UAAArB,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACf3C,OAAA,CAACd,KAAK,CAAC2E,IAAI;QAAAtB,QAAA,eACTvC,OAAA,CAACf,IAAI;UAAAsD,QAAA,eACHvC,OAAA,CAACf,IAAI,CAAC6E,KAAK;YAAAvB,QAAA,gBACTvC,OAAA,CAACf,IAAI,CAAC8E,KAAK;cAAAxB,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjD3C,OAAA,CAACf,IAAI,CAAC8D,OAAO;cAACC,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,mGAAmG;cAAChB,QAAQ,EAAEhB;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb3C,OAAA,CAACd,KAAK,CAAC+E,MAAM;QAAA1B,QAAA,gBACXvC,OAAA,CAACjB,MAAM;UAAC6D,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAAC,KAAK,CAAE;UAAAwB,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA,CAACjB,MAAM;UAAC6D,OAAO,EAAC,SAAS;UAACC,OAAO,EAAET,gBAAiB;UAAAG,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzC,EAAA,CAxLID,cAAc;AAAAkE,EAAA,GAAdlE,cAAc;AA0LpB,eAAeA,cAAc;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}