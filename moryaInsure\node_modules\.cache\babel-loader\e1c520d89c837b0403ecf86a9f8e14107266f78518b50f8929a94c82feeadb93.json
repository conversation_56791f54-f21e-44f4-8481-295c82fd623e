{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Button } from 'react-bootstrap';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = ({\n  className = '',\n  style = {}\n}) => {\n  _s();\n  const {\n    theme,\n    toggleTheme,\n    isLoading\n  } = useTheme();\n  if (isLoading) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Button, {\n    variant: \"outline-secondary\",\n    onClick: toggleTheme,\n    className: `theme-toggle ${className}`,\n    style: style,\n    title: `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    \"aria-label\": `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    children: theme === 'light' ? /*#__PURE__*/_jsxDEV(FaMoon, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(FaSun, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"bhs2nNrwnPXVPJcROq0+hArJHTo=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "FaSun", "FaMoon", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "className", "style", "_s", "theme", "toggleTheme", "isLoading", "variant", "onClick", "title", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { Button } from 'react-bootstrap';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', style = {} }) => {\n  const { theme, toggleTheme, isLoading } = useTheme();\n\n  if (isLoading) {\n    return null;\n  }\n\n  return (\n    <Button\n      variant=\"outline-secondary\"\n      onClick={toggleTheme}\n      className={`theme-toggle ${className}`}\n      style={style}\n      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {theme === 'light' ? (\n        <FaMoon size={20} />\n      ) : (\n        <FaSun size={20} />\n      )}\n    </Button>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAC9C,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,KAAK,GAAG,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAEpD,IAAIS,SAAS,EAAE;IACb,OAAO,IAAI;EACb;EAEA,oBACEP,OAAA,CAACL,MAAM;IACLa,OAAO,EAAC,mBAAmB;IAC3BC,OAAO,EAAEH,WAAY;IACrBJ,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IACbO,KAAK,EAAE,aAAaL,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IAChE,cAAY,aAAaA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IAAAM,QAAA,EAEpEN,KAAK,KAAK,OAAO,gBAChBL,OAAA,CAACH,MAAM;MAACe,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBhB,OAAA,CAACJ,KAAK;MAACgB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACZ,EAAA,CAvBIH,WAAW;EAAA,QAC2BH,QAAQ;AAAA;AAAAmB,EAAA,GAD9ChB,WAAW;AAyBjB,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}