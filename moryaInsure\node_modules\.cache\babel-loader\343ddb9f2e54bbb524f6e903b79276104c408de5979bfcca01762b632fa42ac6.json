{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\SubCategories.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Table, Form, Modal, Dropdown } from 'react-bootstrap';\nimport { FaUser, FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SubCategories = () => {\n  _s();\n  const [username] = useState('Sushil');\n  const [subCategories, setSubCategories] = useState([]);\n  const [search, setSearch] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n\n  // Modal states\n  const [showNewModal, setShowNewModal] = useState(false);\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  // New form state\n  const [newSubCategory, setNewSubCategory] = useState({\n    name: '',\n    category: '',\n    status: 'Active'\n  });\n  const categoriesList = ['Health Insurance', 'Car Insurance', 'Life Insurance', 'Property Insurance'];\n  useEffect(() => {\n    const dummyData = [\n      //   { id: 1, name: 'Medic Care', category: 'Health Insurance', status: 'Active' },\n      //   { id: 2, name: 'Term Life Insurance', category: 'Life Insurance', status: 'Active' },\n      //   { id: 3, name: 'Auto Premium', category: 'Car Insurance', status: 'Active' },\n      //   { id: 4, name: 'Homeowners Insurance', category: 'Property Insurance', status: 'Active' },\n      //   { id: 5, name: 'Comprehensive Coverage', category: 'Car Insurance', status: 'Active' },\n    ];\n    setSubCategories(dummyData);\n  }, []);\n  const filteredSubCategories = subCategories.filter(item => item.name.toLowerCase().includes(search.toLowerCase()) && (selectedCategory === 'All' || item.category === selectedCategory));\n  const handleNewInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewSubCategory(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleAddSubCategory = () => {\n    const newEntry = {\n      id: subCategories.length + 1,\n      ...newSubCategory\n    };\n    setSubCategories([...subCategories, newEntry]);\n    setShowNewModal(false);\n    setNewSubCategory({\n      name: '',\n      category: '',\n      status: 'Active'\n    });\n  };\n  const handleFileUpload = () => {\n    if (selectedFile) {\n      alert(`File uploaded: ${selectedFile.name}`);\n      setShowImportModal(false);\n      setSelectedFile(null);\n    } else {\n      alert('Please select a file first.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Insurance Sub Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowNewModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowImportModal(true),\n        children: \"Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2 align-items-center\",\n      children: [/*#__PURE__*/_jsxDEV(Dropdown, {\n        onSelect: val => setSelectedCategory(val),\n        children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n          variant: \"outline-dark\",\n          size: \"sm\",\n          children: selectedCategory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n          children: [/*#__PURE__*/_jsxDEV(Dropdown.Item, {\n            eventKey: \"All\",\n            children: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), categoriesList.map((cat, idx) => /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n            eventKey: cat,\n            children: cat\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      style: {\n        maxWidth: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search SubCategories...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredSubCategories.map(sub => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: sub.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: sub.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge bg-success\",\n              children: sub.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, sub.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showNewModal,\n      onHide: () => setShowNewModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add New Sub Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Sub Category Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"name\",\n              value: newSubCategory.name,\n              onChange: handleNewInputChange,\n              placeholder: \"Enter subcategory name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"category\",\n              value: newSubCategory.category,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"-- Select Category --\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), categoriesList.map((cat, idx) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cat,\n                children: cat\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"status\",\n              value: newSubCategory.status,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowNewModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAddSubCategory,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImportModal,\n      onHide: () => setShowImportModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Import SubCategories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Select file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"file\",\n            accept: \".csv, .xlsx\",\n            onChange: e => setSelectedFile(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImportModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(SubCategories, \"HYyul/mw0cWJV8Yei3knWR2BLnc=\");\n_c = SubCategories;\nexport default SubCategories;\nvar _c;\n$RefreshReg$(_c, \"SubCategories\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Table", "Form", "Modal", "Dropdown", "FaUser", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "SubCategories", "_s", "username", "subCategories", "setSubCategories", "search", "setSearch", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "showNewModal", "setShowNewModal", "showImportModal", "setShowImportModal", "selectedFile", "setSelectedFile", "newSubCategory", "setNewSubCategory", "name", "category", "status", "categoriesList", "dummyData", "filteredSubCategories", "filter", "item", "toLowerCase", "includes", "handleNewInputChange", "e", "value", "target", "prev", "handleAddSubCategory", "newEntry", "id", "length", "handleFileUpload", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "onSelect", "val", "Toggle", "size", "<PERSON><PERSON>", "<PERSON><PERSON>", "eventKey", "map", "cat", "idx", "style", "max<PERSON><PERSON><PERSON>", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "sub", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "accept", "files", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/SubCategories.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport {\r\n  Button, Table, Form, Modal, Dropdown,\r\n} from 'react-bootstrap';\r\nimport { FaUser, FaEdit, FaTrash } from 'react-icons/fa';\r\n\r\nconst SubCategories = () => {\r\n  const [username] = useState('Sushil');\r\n  const [subCategories, setSubCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('All');\r\n\r\n  // Modal states\r\n  const [showNewModal, setShowNewModal] = useState(false);\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n\r\n  // New form state\r\n  const [newSubCategory, setNewSubCategory] = useState({\r\n    name: '',\r\n    category: '',\r\n    status: 'Active',\r\n  });\r\n\r\n  const categoriesList = ['Health Insurance', 'Car Insurance', 'Life Insurance', 'Property Insurance'];\r\n\r\n  useEffect(() => {\r\n    const dummyData = [\r\n    //   { id: 1, name: 'Medic Care', category: 'Health Insurance', status: 'Active' },\r\n    //   { id: 2, name: 'Term Life Insurance', category: 'Life Insurance', status: 'Active' },\r\n    //   { id: 3, name: 'Auto Premium', category: 'Car Insurance', status: 'Active' },\r\n    //   { id: 4, name: 'Homeowners Insurance', category: 'Property Insurance', status: 'Active' },\r\n    //   { id: 5, name: 'Comprehensive Coverage', category: 'Car Insurance', status: 'Active' },\r\n     ];\r\n    setSubCategories(dummyData);\r\n  }, []);\r\n\r\n  const filteredSubCategories = subCategories.filter((item) =>\r\n    item.name.toLowerCase().includes(search.toLowerCase()) &&\r\n    (selectedCategory === 'All' || item.category === selectedCategory)\r\n  );\r\n\r\n  const handleNewInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setNewSubCategory((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleAddSubCategory = () => {\r\n    const newEntry = {\r\n      id: subCategories.length + 1,\r\n      ...newSubCategory,\r\n    };\r\n    setSubCategories([...subCategories, newEntry]);\r\n    setShowNewModal(false);\r\n    setNewSubCategory({ name: '', category: '', status: 'Active' });\r\n  };\r\n\r\n  const handleFileUpload = () => {\r\n    if (selectedFile) {\r\n      alert(`File uploaded: ${selectedFile.name}`);\r\n      setShowImportModal(false);\r\n      setSelectedFile(null);\r\n    } else {\r\n      alert('Please select a file first.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      {/* Header */}\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h3 className=\"fw-bold text-uppercase\">Insurance Sub Categories</h3>\r\n      </div>\r\n\r\n      {/* Action buttons */}\r\n      <div className=\"mb-3 d-flex gap-2\">\r\n        <Button variant=\"primary\" onClick={() => setShowNewModal(true)}>+ New</Button>\r\n        <Button variant=\"secondary\" onClick={() => setShowImportModal(true)}>Import</Button>\r\n      </div>\r\n\r\n      {/* Export + Filter */}\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2 align-items-center\">\r\n        <Dropdown onSelect={(val) => setSelectedCategory(val)}>\r\n          <Dropdown.Toggle variant=\"outline-dark\" size=\"sm\">\r\n            {selectedCategory}\r\n          </Dropdown.Toggle>\r\n          <Dropdown.Menu>\r\n            <Dropdown.Item eventKey=\"All\">All</Dropdown.Item>\r\n            {categoriesList.map((cat, idx) => (\r\n              <Dropdown.Item key={idx} eventKey={cat}>{cat}</Dropdown.Item>\r\n            ))}\r\n          </Dropdown.Menu>\r\n        </Dropdown>\r\n\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-3\" style={{ maxWidth: '300px' }}>\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search SubCategories...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Sub Category Name</th>\r\n            <th>Category</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredSubCategories.map((sub) => (\r\n            <tr key={sub.id}>\r\n              <td>{sub.name}</td>\r\n              <td>{sub.category}</td>\r\n              <td><span className=\"badge bg-success\">{sub.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\">\r\n                  <FaEdit />\r\n                </Button>\r\n                <Button variant=\"danger\" size=\"sm\">\r\n                  <FaTrash />\r\n                </Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal - New SubCategory */}\r\n      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add New Sub Category</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Sub Category Name</Form.Label>\r\n              <Form.Control\r\n                type=\"text\"\r\n                name=\"name\"\r\n                value={newSubCategory.name}\r\n                onChange={handleNewInputChange}\r\n                placeholder=\"Enter subcategory name\"\r\n              />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Category</Form.Label>\r\n              <Form.Select\r\n                name=\"category\"\r\n                value={newSubCategory.category}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"\">-- Select Category --</option>\r\n                {categoriesList.map((cat, idx) => (\r\n                  <option key={idx} value={cat}>{cat}</option>\r\n                ))}\r\n              </Form.Select>\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Status</Form.Label>\r\n              <Form.Select\r\n                name=\"status\"\r\n                value={newSubCategory.status}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"Active\">Active</option>\r\n                <option value=\"Inactive\">Inactive</option>\r\n              </Form.Select>\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowNewModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleAddSubCategory}>\r\n            Save Changes\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal - Import File */}\r\n      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Import SubCategories</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form.Group>\r\n            <Form.Label>Select file</Form.Label>\r\n            <Form.Control\r\n              type=\"file\"\r\n              accept=\".csv, .xlsx\"\r\n              onChange={(e) => setSelectedFile(e.target.files[0])}\r\n            />\r\n          </Form.Group>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowImportModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleFileUpload}>\r\n            Upload\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubCategories;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,QAC/B,iBAAiB;AACxB,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,QAAQ,CAAC;EACrC,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC;IACnD4B,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAG,CAAC,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,oBAAoB,CAAC;EAEpGhC,SAAS,CAAC,MAAM;IACd,MAAMiC,SAAS,GAAG;MAClB;MACA;MACA;MACA;MACA;IAAA,CACE;IACFjB,gBAAgB,CAACiB,SAAS,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,qBAAqB,GAAGnB,aAAa,CAACoB,MAAM,CAAEC,IAAI,IACtDA,IAAI,CAACP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,MAAM,CAACoB,WAAW,CAAC,CAAC,CAAC,KACrDlB,gBAAgB,KAAK,KAAK,IAAIiB,IAAI,CAACN,QAAQ,KAAKX,gBAAgB,CACnE,CAAC;EAED,MAAMoB,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM;MAAEX,IAAI;MAAEY;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCd,iBAAiB,CAAEe,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACd,IAAI,GAAGY;IAAM,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE/B,aAAa,CAACgC,MAAM,GAAG,CAAC;MAC5B,GAAGpB;IACL,CAAC;IACDX,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE8B,QAAQ,CAAC,CAAC;IAC9CvB,eAAe,CAAC,KAAK,CAAC;IACtBM,iBAAiB,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAC,CAAC;EACjE,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvB,YAAY,EAAE;MAChBwB,KAAK,CAAC,kBAAkBxB,YAAY,CAACI,IAAI,EAAE,CAAC;MAC5CL,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM;MACLuB,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCxC,OAAA;MAAKuC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrExC,OAAA;QAAIuC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxC,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,IAAI,CAAE;QAAA6B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9E5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC,IAAI,CAAE;QAAA2B,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7DxC,OAAA,CAACL,QAAQ;QAACoD,QAAQ,EAAGC,GAAG,IAAKvC,mBAAmB,CAACuC,GAAG,CAAE;QAAAR,QAAA,gBACpDxC,OAAA,CAACL,QAAQ,CAACsD,MAAM;UAACJ,OAAO,EAAC,cAAc;UAACK,IAAI,EAAC,IAAI;UAAAV,QAAA,EAC9ChC;QAAgB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAClB5C,OAAA,CAACL,QAAQ,CAACwD,IAAI;UAAAX,QAAA,gBACZxC,OAAA,CAACL,QAAQ,CAACyD,IAAI;YAACC,QAAQ,EAAC,KAAK;YAAAb,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,EAChDvB,cAAc,CAACiC,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBAC3BxD,OAAA,CAACL,QAAQ,CAACyD,IAAI;YAAWC,QAAQ,EAAEE,GAAI;YAAAf,QAAA,EAAEe;UAAG,GAAxBC,GAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqC,CAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEX5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,mBAAmB;QAACK,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3D5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,mBAAmB;QAACK,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,mBAAmB;QAACK,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5D5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,mBAAmB;QAACK,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1D5C,OAAA,CAACT,MAAM;QAACsD,OAAO,EAAC,mBAAmB;QAACK,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,MAAM;MAACkB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ,CAAE;MAAAlB,QAAA,eACjDxC,OAAA,CAACP,IAAI,CAACkE,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,yBAAyB;QACrC/B,KAAK,EAAExB,MAAO;QACdwD,QAAQ,EAAGjC,CAAC,IAAKtB,SAAS,CAACsB,CAAC,CAACE,MAAM,CAACD,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5C,OAAA,CAACR,KAAK;MAACuE,QAAQ;MAACC,KAAK;MAACC,UAAU;MAAC1B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDxC,OAAA;QAAOuC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAAwC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B5C,OAAA;YAAAwC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB5C,OAAA;YAAAwC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5C,OAAA;YAAAwC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5C,OAAA;QAAAwC,QAAA,EACGjB,qBAAqB,CAAC+B,GAAG,CAAEY,GAAG,iBAC7BlE,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAAwC,QAAA,EAAK0B,GAAG,CAAChD;UAAI;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnB5C,OAAA;YAAAwC,QAAA,EAAK0B,GAAG,CAAC/C;UAAQ;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvB5C,OAAA;YAAAwC,QAAA,eAAIxC,OAAA;cAAMuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE0B,GAAG,CAAC9C;YAAM;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D5C,OAAA;YAAAwC,QAAA,gBACExC,OAAA,CAACT,MAAM;cAACsD,OAAO,EAAC,SAAS;cAACK,IAAI,EAAC,IAAI;cAACX,SAAS,EAAC,MAAM;cAAAC,QAAA,eAClDxC,OAAA,CAACH,MAAM;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACT5C,OAAA,CAACT,MAAM;cAACsD,OAAO,EAAC,QAAQ;cAACK,IAAI,EAAC,IAAI;cAAAV,QAAA,eAChCxC,OAAA,CAACF,OAAO;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAXEsB,GAAG,CAAC/B,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGR5C,OAAA,CAACN,KAAK;MAACyE,IAAI,EAAEzD,YAAa;MAAC0D,MAAM,EAAEA,CAAA,KAAMzD,eAAe,CAAC,KAAK,CAAE;MAAC0D,QAAQ;MAAA7B,QAAA,gBACvExC,OAAA,CAACN,KAAK,CAAC4E,MAAM;QAACC,WAAW;QAAA/B,QAAA,eACvBxC,OAAA,CAACN,KAAK,CAAC8E,KAAK;UAAAhC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACf5C,OAAA,CAACN,KAAK,CAAC+E,IAAI;QAAAjC,QAAA,eACTxC,OAAA,CAACP,IAAI;UAAA+C,QAAA,gBACHxC,OAAA,CAACP,IAAI,CAACiF,KAAK;YAACnC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAACP,IAAI,CAACkF,KAAK;cAAAnC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1C5C,OAAA,CAACP,IAAI,CAACkE,OAAO;cACXC,IAAI,EAAC,MAAM;cACX1C,IAAI,EAAC,MAAM;cACXY,KAAK,EAAEd,cAAc,CAACE,IAAK;cAC3B4C,QAAQ,EAAElC,oBAAqB;cAC/BiC,WAAW,EAAC;YAAwB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACb5C,OAAA,CAACP,IAAI,CAACiF,KAAK;YAACnC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAACP,IAAI,CAACkF,KAAK;cAAAnC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjC5C,OAAA,CAACP,IAAI,CAACmF,MAAM;cACV1D,IAAI,EAAC,UAAU;cACfY,KAAK,EAAEd,cAAc,CAACG,QAAS;cAC/B2C,QAAQ,EAAElC,oBAAqB;cAAAY,QAAA,gBAE/BxC,OAAA;gBAAQ8B,KAAK,EAAC,EAAE;gBAAAU,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9CvB,cAAc,CAACiC,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBAC3BxD,OAAA;gBAAkB8B,KAAK,EAAEyB,GAAI;gBAAAf,QAAA,EAAEe;cAAG,GAArBC,GAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA2B,CAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACb5C,OAAA,CAACP,IAAI,CAACiF,KAAK;YAACnC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BxC,OAAA,CAACP,IAAI,CAACkF,KAAK;cAAAnC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/B5C,OAAA,CAACP,IAAI,CAACmF,MAAM;cACV1D,IAAI,EAAC,QAAQ;cACbY,KAAK,EAAEd,cAAc,CAACI,MAAO;cAC7B0C,QAAQ,EAAElC,oBAAqB;cAAAY,QAAA,gBAE/BxC,OAAA;gBAAQ8B,KAAK,EAAC,QAAQ;gBAAAU,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC5C,OAAA;gBAAQ8B,KAAK,EAAC,UAAU;gBAAAU,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb5C,OAAA,CAACN,KAAK,CAACmF,MAAM;QAAArC,QAAA,gBACXxC,OAAA,CAACT,MAAM;UAACsD,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,KAAK,CAAE;UAAA6B,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5C,OAAA,CAACT,MAAM;UAACsD,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEb,oBAAqB;UAAAO,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR5C,OAAA,CAACN,KAAK;MAACyE,IAAI,EAAEvD,eAAgB;MAACwD,MAAM,EAAEA,CAAA,KAAMvD,kBAAkB,CAAC,KAAK,CAAE;MAACwD,QAAQ;MAAA7B,QAAA,gBAC7ExC,OAAA,CAACN,KAAK,CAAC4E,MAAM;QAACC,WAAW;QAAA/B,QAAA,eACvBxC,OAAA,CAACN,KAAK,CAAC8E,KAAK;UAAAhC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACf5C,OAAA,CAACN,KAAK,CAAC+E,IAAI;QAAAjC,QAAA,eACTxC,OAAA,CAACP,IAAI,CAACiF,KAAK;UAAAlC,QAAA,gBACTxC,OAAA,CAACP,IAAI,CAACkF,KAAK;YAAAnC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC5C,OAAA,CAACP,IAAI,CAACkE,OAAO;YACXC,IAAI,EAAC,MAAM;YACXkB,MAAM,EAAC,aAAa;YACpBhB,QAAQ,EAAGjC,CAAC,IAAKd,eAAe,CAACc,CAAC,CAACE,MAAM,CAACgD,KAAK,CAAC,CAAC,CAAC;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACb5C,OAAA,CAACN,KAAK,CAACmF,MAAM;QAAArC,QAAA,gBACXxC,OAAA,CAACT,MAAM;UAACsD,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC,KAAK,CAAE;UAAA2B,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5C,OAAA,CAACT,MAAM;UAACsD,OAAO,EAAC,SAAS;UAACC,OAAO,EAAET,gBAAiB;UAAAG,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1C,EAAA,CArNID,aAAa;AAAA+E,EAAA,GAAb/E,aAAa;AAuNnB,eAAeA,aAAa;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}