# 🔗 Frontend Integration Guide - Phase 3

## 📋 **Overview**

This guide explains how to integrate the enhanced backend APIs with your React frontend to replace static data with dynamic, persistent data.

---

## 🆕 **NEW API SERVICES TO ADD**

### **1. Settings API Service** (`src/services/api.js`)

```javascript
// Add to your existing api.js file
export const settingsAPI = {
  getSettings: () => api.get('/settings'),
  updateSettings: (data) => api.put('/settings', data),
  getPublicSettings: () => api.get('/settings/public'),
  updateTheme: (data) => api.put('/settings/theme', data),
  updateSecurity: (data) => api.put('/settings/security', data),
  updateNotifications: (data) => api.put('/settings/notifications', data),
  resetSettings: () => api.post('/settings/reset')
};

export const subCategoriesAPI = {
  getSubCategories: (params) => api.get('/subcategories', { params }),
  getSubCategoryById: (id) => api.get(`/subcategories/${id}`),
  createSubCategory: (data) => api.post('/subcategories', data),
  updateSubCategory: (id, data) => api.put(`/subcategories/${id}`, data),
  deleteSubCategory: (id) => api.delete(`/subcategories/${id}`),
  getByCategory: (categoryId) => api.get(`/subcategories/category/${categoryId}`),
  importSubCategories: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/subcategories/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
};

export const performanceAPI = {
  getPerformanceReport: (params) => api.get('/reports/performance', { params }),
  assignPolicy: (policyId, staffId) => api.put(`/reports/assign-policy/${policyId}`, { staffId }),
  assignTicket: (ticketId, staffId) => api.put(`/reports/assign-ticket/${ticketId}`, { staffId })
};
```

---

## 🔄 **DASHBOARD DATA INTEGRATION**

### **1. Admin Dashboard Updates**

**Replace static data with API calls:**

```javascript
// In AdminDashboard.js
import { reportsAPI, performanceAPI, userAPI } from '../services/api';

const AdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchPerformanceData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await reportsAPI.getDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const fetchPerformanceData = async () => {
    try {
      const response = await performanceAPI.getPerformanceReport();
      if (response.success) {
        setPerformanceData(response.data);
      }
    } catch (error) {
      console.error('Error fetching performance data:', error);
    }
  };

  // Use dashboardData.overview for statistics cards
  // Use performanceData for revenue and employee performance
};
```

### **2. Employee Dashboard Updates**

**Filter data based on employee assignment:**

```javascript
// In EmployeeDashboard.js
import { reportsAPI } from '../services/api';

const EmployeeDashboard = () => {
  const [myWork, setMyWork] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user?.role === 'employee') {
      fetchMyWork();
    }
  }, [user]);

  const fetchMyWork = async () => {
    try {
      const response = await reportsAPI.getDashboardStats();
      if (response.success && response.data.myWork) {
        setMyWork(response.data);
      }
    } catch (error) {
      console.error('Error fetching my work:', error);
    }
  };

  // Use myWork.overview for employee statistics
  // Use myWork.myWork.policies for assigned policies
  // Use myWork.myWork.tickets for assigned tickets
};
```

### **3. Customer Dashboard Updates**

**Show personal data only:**

```javascript
// In CustomerDashboard.js
import { reportsAPI, policiesAPI } from '../services/api';

const CustomerDashboard = () => {
  const [customerData, setCustomerData] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user?.role === 'customer') {
      fetchCustomerData();
    }
  }, [user]);

  const fetchCustomerData = async () => {
    try {
      const response = await reportsAPI.getDashboardStats();
      if (response.success && response.data.myData) {
        setCustomerData(response.data);
      }
    } catch (error) {
      console.error('Error fetching customer data:', error);
    }
  };

  // Use customerData.myData for personal policies and claims
};
```

---

## 📊 **NEW PAGES TO CREATE**

### **1. System Settings Page**

```javascript
// Create src/Pages/SystemSettings.js
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Tabs, Tab } from 'react-bootstrap';
import { settingsAPI } from '../services/api';

const SystemSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getSettings();
      if (response.success) {
        setSettings(response.data.settings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (sectionData) => {
    try {
      setLoading(true);
      const response = await settingsAPI.updateSettings(sectionData);
      if (response.success) {
        setSettings(response.data.settings);
        alert('Settings updated successfully!');
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      alert('Error updating settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container fluid className="py-4">
      <h2>System Settings</h2>
      <Tabs defaultActiveKey="organization">
        <Tab eventKey="organization" title="Organization">
          {/* Organization settings form */}
        </Tab>
        <Tab eventKey="security" title="Security">
          {/* Security settings form */}
        </Tab>
        <Tab eventKey="theme" title="Theme">
          {/* Theme settings form */}
        </Tab>
      </Tabs>
    </Container>
  );
};

export default SystemSettings;
```

### **2. SubCategories Page**

```javascript
// Create src/Pages/SubCategories.js
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form } from 'react-bootstrap';
import { subCategoriesAPI, categoriesAPI } from '../services/api';

const SubCategories = () => {
  const [subCategories, setSubCategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);

  useEffect(() => {
    fetchSubCategories();
    fetchCategories();
  }, []);

  const fetchSubCategories = async () => {
    try {
      const response = await subCategoriesAPI.getSubCategories();
      if (response.success) {
        setSubCategories(response.data.subcategories);
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesAPI.getCategories();
      if (response.success) {
        setCategories(response.data.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Add CRUD operations for subcategories
  // Add import functionality
  // Add search and filtering
};
```

---

## 🎯 **TASK ASSIGNMENT INTEGRATION**

### **Admin Task Assignment Component**

```javascript
// Create src/components/TaskAssignment.js
import React, { useState, useEffect } from 'react';
import { Modal, Form, Button } from 'react-bootstrap';
import { performanceAPI, userAPI } from '../services/api';

const TaskAssignmentModal = ({ show, onHide, taskType, taskId, onAssigned }) => {
  const [staff, setStaff] = useState([]);
  const [selectedStaff, setSelectedStaff] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (show) {
      fetchStaff();
    }
  }, [show]);

  const fetchStaff = async () => {
    try {
      const response = await userAPI.getUsers({ role: 'employee' });
      if (response.success) {
        setStaff(response.data.users);
      }
    } catch (error) {
      console.error('Error fetching staff:', error);
    }
  };

  const handleAssign = async () => {
    if (!selectedStaff) return;

    try {
      setLoading(true);
      let response;
      
      if (taskType === 'policy') {
        response = await performanceAPI.assignPolicy(taskId, selectedStaff);
      } else if (taskType === 'ticket') {
        response = await performanceAPI.assignTicket(taskId, selectedStaff);
      }

      if (response.success) {
        onAssigned();
        onHide();
        alert('Task assigned successfully!');
      }
    } catch (error) {
      console.error('Error assigning task:', error);
      alert('Error assigning task');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Assign {taskType}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Group>
          <Form.Label>Select Staff Member</Form.Label>
          <Form.Select
            value={selectedStaff}
            onChange={(e) => setSelectedStaff(e.target.value)}
          >
            <option value="">Choose staff member...</option>
            {staff.map(member => (
              <option key={member._id} value={member._id}>
                {member.firstName} {member.lastName}
              </option>
            ))}
          </Form.Select>
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>Cancel</Button>
        <Button 
          variant="primary" 
          onClick={handleAssign}
          disabled={!selectedStaff || loading}
        >
          {loading ? 'Assigning...' : 'Assign'}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
```

---

## 📈 **PERFORMANCE REPORTING INTEGRATION**

### **Performance Reports Component**

```javascript
// Create src/components/PerformanceReports.js
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Form, Button, Table } from 'react-bootstrap';
import { performanceAPI } from '../services/api';

const PerformanceReports = () => {
  const [reportData, setReportData] = useState(null);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    employeeId: ''
  });

  const fetchReport = async () => {
    try {
      const response = await performanceAPI.getPerformanceReport(filters);
      if (response.success) {
        setReportData(response.data);
      }
    } catch (error) {
      console.error('Error fetching performance report:', error);
    }
  };

  return (
    <div>
      {/* Filter controls */}
      <Card className="mb-4">
        <Card.Body>
          <Row>
            <Col md={3}>
              <Form.Group>
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => setFilters({...filters, startDate: e.target.value})}
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group>
                <Form.Label>End Date</Form.Label>
                <Form.Control
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => setFilters({...filters, endDate: e.target.value})}
                />
              </Form.Group>
            </Col>
            <Col md={3}>
              <Button onClick={fetchReport} className="mt-4">
                Generate Report
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Report display */}
      {reportData && (
        <>
          {/* Revenue overview cards */}
          {/* Employee performance table */}
          {/* Policy distribution charts */}
        </>
      )}
    </div>
  );
};
```

---

## 🔧 **IMPLEMENTATION CHECKLIST**

### **✅ API Integration:**
- [ ] Add new API services to `src/services/api.js`
- [ ] Update existing dashboard components to use real API data
- [ ] Replace all static/dummy data with API calls
- [ ] Add error handling for all API calls

### **✅ New Pages:**
- [ ] Create SystemSettings page with tabbed interface
- [ ] Create SubCategories page with CRUD operations
- [ ] Create PerformanceReports page with filtering
- [ ] Add routes for new pages in App.js

### **✅ Components:**
- [ ] Create TaskAssignment modal component
- [ ] Create PerformanceReports component
- [ ] Update existing tables to show real data
- [ ] Add loading states and error handling

### **✅ Navigation:**
- [ ] Add new pages to sidebar navigation
- [ ] Update role-based navigation permissions
- [ ] Add breadcrumbs for new pages

**🎉 Once implemented, your frontend will have complete integration with the enhanced backend, providing real-time, persistent data across all dashboards!**
