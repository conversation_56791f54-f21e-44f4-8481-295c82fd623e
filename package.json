{"name": "morya-insurance-management-system", "version": "1.0.0", "description": "A comprehensive insurance management system with RBAC built with React.js and Node.js", "main": "index.js", "scripts": {"install-all": "npm install && cd server && npm install && cd ../moryaInsure && npm install", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd moryaInsure && npm start", "build": "cd moryaInsure && npm run build", "test": "cd moryaInsure && npm test", "start": "cd server && npm start"}, "keywords": ["insurance", "management", "system", "react", "nodejs", "mongodb", "rbac", "authentication", "dashboard"], "author": "Morya Insurance Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/morya-insurance-system.git"}, "bugs": {"url": "https://github.com/your-username/morya-insurance-system/issues"}, "homepage": "https://github.com/your-username/morya-insurance-system#readme"}