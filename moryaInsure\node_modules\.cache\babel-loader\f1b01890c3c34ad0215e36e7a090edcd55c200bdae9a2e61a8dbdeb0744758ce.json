{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\ConnectionStatus.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FaWifi, FaExclamationTriangle } from 'react-icons/fa';\nimport { useRealtime } from '../contexts/RealtimeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ConnectionStatus = () => {\n  _s();\n  const {\n    isConnected\n  } = useRealtime();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `connection-status ${isConnected ? 'connected' : 'disconnected'}`,\n    style: {\n      position: 'fixed',\n      bottom: '20px',\n      right: '20px',\n      zIndex: 1040,\n      padding: '8px 12px',\n      borderRadius: '20px',\n      fontSize: '0.875rem',\n      fontWeight: '500',\n      backgroundColor: 'var(--card-bg)',\n      border: `1px solid ${isConnected ? 'var(--success-text)' : 'var(--danger-text)'}`,\n      color: isConnected ? 'var(--success-text)' : 'var(--danger-text)',\n      boxShadow: '0 2px 8px var(--card-shadow)',\n      transition: 'all 0.3s ease'\n    },\n    children: isConnected ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(FaWifi, {\n        className: \"me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), \"Real-time Connected\"]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n        className: \"me-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this), \"Disconnected\"]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(ConnectionStatus, \"FrcVwiLF2SCdpzsBBlSsSDVXa5I=\", false, function () {\n  return [useRealtime];\n});\n_c = ConnectionStatus;\nexport default ConnectionStatus;\nvar _c;\n$RefreshReg$(_c, \"ConnectionStatus\");", "map": {"version": 3, "names": ["React", "FaWifi", "FaExclamationTriangle", "useRealtime", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ConnectionStatus", "_s", "isConnected", "className", "style", "position", "bottom", "right", "zIndex", "padding", "borderRadius", "fontSize", "fontWeight", "backgroundColor", "border", "color", "boxShadow", "transition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/ConnectionStatus.js"], "sourcesContent": ["import React from 'react';\nimport { FaWifi, FaExclamationTriangle } from 'react-icons/fa';\nimport { useRealtime } from '../contexts/RealtimeContext';\n\nconst ConnectionStatus = () => {\n  const { isConnected } = useRealtime();\n\n  return (\n    <div\n      className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}\n      style={{\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        zIndex: 1040,\n        padding: '8px 12px',\n        borderRadius: '20px',\n        fontSize: '0.875rem',\n        fontWeight: '500',\n        backgroundColor: 'var(--card-bg)',\n        border: `1px solid ${isConnected ? 'var(--success-text)' : 'var(--danger-text)'}`,\n        color: isConnected ? 'var(--success-text)' : 'var(--danger-text)',\n        boxShadow: '0 2px 8px var(--card-shadow)',\n        transition: 'all 0.3s ease'\n      }}\n    >\n      {isConnected ? (\n        <>\n          <FaWifi className=\"me-2\" />\n          Real-time Connected\n        </>\n      ) : (\n        <>\n          <FaExclamationTriangle className=\"me-2\" />\n          Disconnected\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default ConnectionStatus;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,qBAAqB,QAAQ,gBAAgB;AAC9D,SAASC,WAAW,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAY,CAAC,GAAGP,WAAW,CAAC,CAAC;EAErC,oBACEE,OAAA;IACEM,SAAS,EAAE,qBAAqBD,WAAW,GAAG,WAAW,GAAG,cAAc,EAAG;IAC7EE,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,UAAU;MACnBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,KAAK;MACjBC,eAAe,EAAE,gBAAgB;MACjCC,MAAM,EAAE,aAAaZ,WAAW,GAAG,qBAAqB,GAAG,oBAAoB,EAAE;MACjFa,KAAK,EAAEb,WAAW,GAAG,qBAAqB,GAAG,oBAAoB;MACjEc,SAAS,EAAE,8BAA8B;MACzCC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,EAEDhB,WAAW,gBACVL,OAAA,CAAAE,SAAA;MAAAmB,QAAA,gBACErB,OAAA,CAACJ,MAAM;QAACU,SAAS,EAAC;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,uBAE7B;IAAA,eAAE,CAAC,gBAEHzB,OAAA,CAAAE,SAAA;MAAAmB,QAAA,gBACErB,OAAA,CAACH,qBAAqB;QAACS,SAAS,EAAC;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE5C;IAAA,eAAE;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrB,EAAA,CAnCID,gBAAgB;EAAA,QACIL,WAAW;AAAA;AAAA4B,EAAA,GAD/BvB,gBAAgB;AAqCtB,eAAeA,gBAAgB;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}