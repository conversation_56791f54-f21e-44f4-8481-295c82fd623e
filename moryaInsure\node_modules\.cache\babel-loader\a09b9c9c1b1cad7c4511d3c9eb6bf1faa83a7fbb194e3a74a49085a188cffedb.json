{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\PolicyHolder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Form, Modal, Row, Col, Container, Card, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PolicyHolder = () => {\n  _s();\n  const [policyHolders, setPolicyHolders] = useState([]);\n  const [policies, setPolicies] = useState([]);\n  const [search, setSearch] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedHolder, setSelectedHolder] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form state\n  const [form, setForm] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    gender: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'India'\n    },\n    emergencyContact: {\n      name: '',\n      relationship: '',\n      phone: ''\n    },\n    occupation: '',\n    annualIncome: '',\n    status: 'active'\n  });\n  useEffect(() => {\n    fetchPolicyHolders();\n    fetchPolicies();\n  }, []);\n  const fetchPolicyHolders = async () => {\n    try {\n      setLoading(true);\n      const response = await policyHoldersAPI.getPolicyHolders();\n      if (response.success) {\n        setPolicyHolders(response.data.policyHolders || []);\n      } else {\n        setError('Failed to fetch policy holders');\n      }\n    } catch (error) {\n      console.error('Error fetching policy holders:', error);\n      setError('Failed to fetch policy holders');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchPolicies = async () => {\n    try {\n      const response = await policiesAPI.getPolicies();\n      if (response.success) {\n        setPolicies(response.data.policies || []);\n      }\n    } catch (error) {\n      console.error('Error fetching policies:', error);\n    }\n  };\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSave = () => {\n    const newHolder = {\n      ...form,\n      id: holders.length + 1,\n      status: 'Active'\n    };\n    setHolders([...holders, newHolder]);\n    setShowModal(false);\n    setForm({\n      holderName: '',\n      contactNumber: '',\n      policyName: '',\n      category: '',\n      subCategory: '',\n      sumAssured: '',\n      premium: '',\n      tenure: ''\n    });\n  };\n  const filteredHolders = holders.filter(h => h.holderName.toLowerCase().includes(search.toLowerCase()) || h.policyName.toLowerCase().includes(search.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Policy Holders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search policy holders...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Holder Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Contact Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sum Assured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Premium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tenure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredHolders.map(h => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.holderName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.contactNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.policyName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.subCategory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.sumAssured, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.premium, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.tenure, \" months\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge bg-${h.status === 'Active' ? 'success' : 'warning'}`,\n              children: h.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, h.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"New Policy Holder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Holder Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"holderName\",\n              value: form.holderName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Contact Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"contactNumber\",\n              value: form.contactNumber,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"policyName\",\n              value: form.policyName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"category\",\n                value: form.category,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sub Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"subCategory\",\n                value: form.subCategory,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sum Assured\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"sumAssured\",\n                value: form.sumAssured,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"premium\",\n                value: form.premium,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tenure (Months)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"tenure\",\n              value: form.tenure,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(PolicyHolder, \"jySNE3FZ0lBwq294NSYCFHi4cCY=\");\n_c = PolicyHolder;\nexport default PolicyHolder;\nvar _c;\n$RefreshReg$(_c, \"PolicyHolder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Form", "Modal", "Row", "Col", "Container", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaSearch", "FaFileImport", "FaUser", "FaPhone", "FaEnvelope", "policyHoldersAPI", "policiesAPI", "jsxDEV", "_jsxDEV", "PolicyHolder", "_s", "policyHolders", "setPolicyHolders", "policies", "setPolicies", "search", "setSearch", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "showEditModal", "setShowEditModal", "showViewModal", "setShowViewModal", "selectedHolder", "setSelectedHolder", "submitting", "setSubmitting", "form", "setForm", "firstName", "lastName", "email", "phone", "dateOfBirth", "gender", "address", "street", "city", "state", "zipCode", "country", "emergencyContact", "name", "relationship", "occupation", "annualIncome", "status", "fetchPolicyHolders", "fetchPolicies", "response", "getPolicyHolders", "data", "console", "getPolicies", "handleChange", "e", "target", "value", "handleSave", "newHolder", "id", "holders", "length", "setHolders", "<PERSON><PERSON><PERSON>", "contactNumber", "policyName", "category", "subCategory", "sumAssured", "premium", "tenure", "filteredHolders", "filter", "h", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/PolicyHolder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>, Form, Modal, <PERSON>, Col, Con<PERSON>er, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\r\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\r\n\r\nconst PolicyHolder = () => {\r\n  const [policyHolders, setPolicyHolders] = useState([]);\r\n  const [policies, setPolicies] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedHolder, setSelectedHolder] = useState(null);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  // Form state\r\n  const [form, setForm] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    phone: '',\r\n    dateOfBirth: '',\r\n    gender: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: 'India'\r\n    },\r\n    emergencyContact: {\r\n      name: '',\r\n      relationship: '',\r\n      phone: ''\r\n    },\r\n    occupation: '',\r\n    annualIncome: '',\r\n    status: 'active'\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchPolicyHolders();\r\n    fetchPolicies();\r\n  }, []);\r\n\r\n  const fetchPolicyHolders = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await policyHoldersAPI.getPolicyHolders();\r\n      if (response.success) {\r\n        setPolicyHolders(response.data.policyHolders || []);\r\n      } else {\r\n        setError('Failed to fetch policy holders');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policy holders:', error);\r\n      setError('Failed to fetch policy holders');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchPolicies = async () => {\r\n    try {\r\n      const response = await policiesAPI.getPolicies();\r\n      if (response.success) {\r\n        setPolicies(response.data.policies || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policies:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSave = () => {\r\n    const newHolder = { ...form, id: holders.length + 1, status: 'Active' };\r\n    setHolders([...holders, newHolder]);\r\n    setShowModal(false);\r\n    setForm({\r\n      holderName: '', contactNumber: '', policyName: '', category: '',\r\n      subCategory: '', sumAssured: '', premium: '', tenure: ''\r\n    });\r\n  };\r\n\r\n  const filteredHolders = holders.filter(h =>\r\n    h.holderName.toLowerCase().includes(search.toLowerCase()) ||\r\n    h.policyName.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h4 className=\"fw-bold text-uppercase\">Policy Holders</h4>\r\n        <Button variant=\"primary\" onClick={() => setShowModal(true)}>+ New</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search policy holders...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Policy Holder Name</th>\r\n            <th>Contact Number</th>\r\n            <th>Policy Name</th>\r\n            <th>Category</th>\r\n            <th>Sub Category</th>\r\n            <th>Sum Assured</th>\r\n            <th>Premium</th>\r\n            <th>Tenure</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredHolders.map((h) => (\r\n            <tr key={h.id}>\r\n              <td>{h.holderName}</td>\r\n              <td>{h.contactNumber}</td>\r\n              <td>{h.policyName}</td>\r\n              <td>{h.category}</td>\r\n              <td>{h.subCategory}</td>\r\n              <td>{h.sumAssured} PHP</td>\r\n              <td>{h.premium} PHP</td>\r\n              <td>{h.tenure} months</td>\r\n              <td><span className={`badge bg-${h.status === 'Active' ? 'success' : 'warning'}`}>{h.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>New Policy Holder</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Holder Name</Form.Label>\r\n              <Form.Control name=\"holderName\" value={form.holderName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Contact Number</Form.Label>\r\n              <Form.Control name=\"contactNumber\" value={form.contactNumber} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Name</Form.Label>\r\n              <Form.Control name=\"policyName\" value={form.policyName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Category</Form.Label>\r\n                <Form.Select name=\"category\" value={form.category} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Auto Insurance</option>\r\n                  <option>Life Insurance</option>\r\n                  <option>Travel Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Sub Category</Form.Label>\r\n                <Form.Select name=\"subCategory\" value={form.subCategory} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Comprehensive Coverage</option>\r\n                  <option>Term Life Insurance</option>\r\n                  <option>Travel Cancellation Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Sum Assured</Form.Label>\r\n                <Form.Control name=\"sumAssured\" value={form.sumAssured} onChange={handleChange} />\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Premium</Form.Label>\r\n                <Form.Control name=\"premium\" value={form.premium} onChange={handleChange} />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Tenure (Months)</Form.Label>\r\n              <Form.Control name=\"tenure\" value={form.tenure} onChange={handleChange} />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={handleSave}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PolicyHolder;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC9G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AACpH,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAGnD,QAAQ,CAAC;IAC/BoD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBX,KAAK,EAAE;IACT,CAAC;IACDY,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEFpE,SAAS,CAAC,MAAM;IACdqE,kBAAkB,CAAC,CAAC;IACpBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,QAAQ,GAAG,MAAMlD,gBAAgB,CAACmD,gBAAgB,CAAC,CAAC;MAC1D,IAAID,QAAQ,CAAClC,OAAO,EAAE;QACpBT,gBAAgB,CAAC2C,QAAQ,CAACE,IAAI,CAAC9C,aAAa,IAAI,EAAE,CAAC;MACrD,CAAC,MAAM;QACLS,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjD,WAAW,CAACqD,WAAW,CAAC,CAAC;MAChD,IAAIJ,QAAQ,CAAClC,OAAO,EAAE;QACpBP,WAAW,CAACyC,QAAQ,CAACE,IAAI,CAAC5C,QAAQ,IAAI,EAAE,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIC,CAAC,IAAK;IAC1B3B,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAAC4B,CAAC,CAACC,MAAM,CAACd,IAAI,GAAGa,CAAC,CAACC,MAAM,CAACC;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAG;MAAE,GAAGhC,IAAI;MAAEiC,EAAE,EAAEC,OAAO,CAACC,MAAM,GAAG,CAAC;MAAEhB,MAAM,EAAE;IAAS,CAAC;IACvEiB,UAAU,CAAC,CAAC,GAAGF,OAAO,EAAEF,SAAS,CAAC,CAAC;IACnCzC,YAAY,CAAC,KAAK,CAAC;IACnBU,OAAO,CAAC;MACNoC,UAAU,EAAE,EAAE;MAAEC,aAAa,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAC/DC,WAAW,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IACxD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,eAAe,GAAGX,OAAO,CAACY,MAAM,CAACC,CAAC,IACtCA,CAAC,CAACV,UAAU,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnE,MAAM,CAACkE,WAAW,CAAC,CAAC,CAAC,IACzDD,CAAC,CAACR,UAAU,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnE,MAAM,CAACkE,WAAW,CAAC,CAAC,CAC1D,CAAC;EAED,oBACEzE,OAAA;IAAK2E,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC5E,OAAA;MAAK2E,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrE5E,OAAA;QAAI2E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DhF,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC,IAAI,CAAE;QAAA4D,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C5E,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DhF,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DhF,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DhF,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DhF,OAAA,CAACtB,MAAM;QAACuG,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB5E,OAAA,CAACrB,IAAI,CAACyG,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,0BAA0B;QACtC/B,KAAK,EAAEhD,MAAO;QACdgF,QAAQ,EAAGlC,CAAC,IAAK7C,SAAS,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhF,OAAA,CAACvB,KAAK;MAAC+G,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACf,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpD5E,OAAA;QAAO2E,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9B5E,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAA4E,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhF,OAAA;YAAA4E,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBhF,OAAA;YAAA4E,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBhF,OAAA;YAAA4E,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBhF,OAAA;YAAA4E,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBhF,OAAA;YAAA4E,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBhF,OAAA;YAAA4E,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBhF,OAAA;YAAA4E,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfhF,OAAA;YAAA4E,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfhF,OAAA;YAAA4E,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRhF,OAAA;QAAA4E,QAAA,EACGN,eAAe,CAACqB,GAAG,CAAEnB,CAAC,iBACrBxE,OAAA;UAAA4E,QAAA,gBACE5E,OAAA;YAAA4E,QAAA,EAAKJ,CAAC,CAACV;UAAU;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBhF,OAAA;YAAA4E,QAAA,EAAKJ,CAAC,CAACT;UAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BhF,OAAA;YAAA4E,QAAA,EAAKJ,CAAC,CAACR;UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBhF,OAAA;YAAA4E,QAAA,EAAKJ,CAAC,CAACP;UAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBhF,OAAA;YAAA4E,QAAA,EAAKJ,CAAC,CAACN;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBhF,OAAA;YAAA4E,QAAA,GAAKJ,CAAC,CAACL,UAAU,EAAC,MAAI;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhF,OAAA;YAAA4E,QAAA,GAAKJ,CAAC,CAACJ,OAAO,EAAC,MAAI;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBhF,OAAA;YAAA4E,QAAA,GAAKJ,CAAC,CAACH,MAAM,EAAC,SAAO;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BhF,OAAA;YAAA4E,QAAA,eAAI5E,OAAA;cAAM2E,SAAS,EAAE,YAAYH,CAAC,CAAC5B,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAG;cAAAgC,QAAA,EAAEJ,CAAC,CAAC5B;YAAM;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxGhF,OAAA;YAAA4E,QAAA,gBACE5E,OAAA,CAACtB,MAAM;cAACuG,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAAC5E,OAAA,CAACX,MAAM;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxEhF,OAAA,CAACtB,MAAM;cAACuG,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAAC5E,OAAA,CAACV,OAAO;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAbER,CAAC,CAACd,EAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcT,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRhF,OAAA,CAACpB,KAAK;MAACgH,IAAI,EAAE7E,SAAU;MAAC8E,MAAM,EAAEA,CAAA,KAAM7E,YAAY,CAAC,KAAK,CAAE;MAAC8E,QAAQ;MAAAlB,QAAA,gBACjE5E,OAAA,CAACpB,KAAK,CAACmH,MAAM;QAACC,WAAW;QAAApB,QAAA,eACvB5E,OAAA,CAACpB,KAAK,CAACqH,KAAK;UAAArB,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACfhF,OAAA,CAACpB,KAAK,CAACsH,IAAI;QAAAtB,QAAA,eACT5E,OAAA,CAACrB,IAAI;UAAAiG,QAAA,gBACH5E,OAAA,CAACrB,IAAI,CAACwH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;cAAAxB,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3ChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;cAAC5C,IAAI,EAAC,YAAY;cAACe,KAAK,EAAE9B,IAAI,CAACqC,UAAW;cAACyB,QAAQ,EAAEnC;YAAa;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbhF,OAAA,CAACrB,IAAI,CAACwH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;cAAAxB,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;cAAC5C,IAAI,EAAC,eAAe;cAACe,KAAK,EAAE9B,IAAI,CAACsC,aAAc;cAACwB,QAAQ,EAAEnC;YAAa;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEbhF,OAAA,CAACrB,IAAI,CAACwH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;cAAAxB,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;cAAC5C,IAAI,EAAC,YAAY;cAACe,KAAK,EAAE9B,IAAI,CAACuC,UAAW;cAACuB,QAAQ,EAAEnC;YAAa;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbhF,OAAA,CAACnB,GAAG;YAAC8F,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5E,OAAA,CAAClB,GAAG;cAAA8F,QAAA,gBACF5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;gBAAAxB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjChF,OAAA,CAACrB,IAAI,CAAC0H,MAAM;gBAAC7D,IAAI,EAAC,UAAU;gBAACe,KAAK,EAAE9B,IAAI,CAACwC,QAAS;gBAACsB,QAAQ,EAAEnC,YAAa;gBAAAwB,QAAA,eACxE5E,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAqB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNhF,OAAA,CAAClB,GAAG;cAAA8F,QAAA,gBACF5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;gBAAAxB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrChF,OAAA,CAACrB,IAAI,CAAC0H,MAAM;gBAAC7D,IAAI,EAAC,aAAa;gBAACe,KAAK,EAAE9B,IAAI,CAACyC,WAAY;gBAACqB,QAAQ,EAAEnC,YAAa;gBAAAwB,QAAA,eAC9E5E,OAAA;kBAAQuD,KAAK,EAAC,EAAE;kBAAAqB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA,CAACnB,GAAG;YAAC8F,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB5E,OAAA,CAAClB,GAAG;cAAA8F,QAAA,gBACF5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;gBAAAxB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;gBAAC5C,IAAI,EAAC,YAAY;gBAACe,KAAK,EAAE9B,IAAI,CAAC0C,UAAW;gBAACoB,QAAQ,EAAEnC;cAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNhF,OAAA,CAAClB,GAAG;cAAA8F,QAAA,gBACF5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;gBAAAxB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;gBAAC5C,IAAI,EAAC,SAAS;gBAACe,KAAK,EAAE9B,IAAI,CAAC2C,OAAQ;gBAACmB,QAAQ,EAAEnC;cAAa;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhF,OAAA,CAACrB,IAAI,CAACwH,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1B5E,OAAA,CAACrB,IAAI,CAACyH,KAAK;cAAAxB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxChF,OAAA,CAACrB,IAAI,CAACyG,OAAO;cAAC5C,IAAI,EAAC,QAAQ;cAACe,KAAK,EAAE9B,IAAI,CAAC4C,MAAO;cAACkB,QAAQ,EAAEnC;YAAa;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbhF,OAAA,CAACpB,KAAK,CAAC0H,MAAM;QAAA1B,QAAA,gBACX5E,OAAA,CAACtB,MAAM;UAACuG,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC,KAAK,CAAE;UAAA4D,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EhF,OAAA,CAACtB,MAAM;UAACuG,OAAO,EAAC,SAAS;UAACC,OAAO,EAAE1B,UAAW;UAAAoB,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA3NID,YAAY;AAAAsG,EAAA,GAAZtG,YAAY;AA6NlB,eAAeA,YAAY;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}