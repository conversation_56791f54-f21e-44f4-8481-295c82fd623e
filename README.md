# Morya Insurance Management System

A comprehensive insurance management system with role-based access control (RBAC) built with React.js frontend and Node.js backend.

## Features

### 🔐 Authentication & Authorization
- JWT-based authentication
- Role-based access control (Admin, Employee, Customer)
- Secure password hashing with bcrypt
- Account lockout after failed attempts
- Session management

### 🎨 Frontend Features
- **Responsive Design**: Mobile-first approach with Bootstrap
- **Expandable Sidebar**: Collapsible navigation with tooltips
- **Dynamic Navbar**: User-specific navigation with role indicators
- **Role-based Dashboards**: Customized views for each user role
- **Modern UI**: Clean, professional interface with smooth animations

### 🏢 Admin Features
- User management (Create, Read, Update, Delete)
- System statistics and analytics
- Category and subcategory management
- Policy approval workflows
- Comprehensive reporting tools
- System health monitoring

### 👨‍💼 Employee Features
- Policy management and processing
- Customer support ticket handling
- Policy holder information management
- Performance tracking and reports
- Task management dashboard

### 👤 Customer Features
- Personal dashboard with policy overview
- Premium payment tracking
- Support ticket creation and tracking
- Policy document access
- Claims management

### 📊 Business Logic
- **Policy Management**: Life, Health, Auto, Home, Business, Travel insurance
- **Support Tickets**: Multi-level priority system with SLA tracking
- **Categories**: Hierarchical organization system
- **Reports**: Comprehensive analytics and insights
- **Notifications**: Real-time updates and alerts

## Technology Stack

### Frontend
- **React 19.1.0** - Modern UI library
- **React Router DOM 7.6.2** - Client-side routing
- **React Bootstrap 2.10.10** - UI components
- **Bootstrap 5.3.7** - CSS framework
- **React Icons 5.5.0** - Icon library
- **Axios** - HTTP client for API calls

### Backend
- **Node.js** - Runtime environment
- **Express.js 5.1.0** - Web framework
- **MongoDB** - NoSQL database
- **Mongoose 8.16.0** - ODM for MongoDB
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing
- **Helmet** - Security middleware
- **CORS** - Cross-origin resource sharing
- **Express Rate Limit** - API rate limiting
- **Express Validator** - Input validation

## Project Structure

```
├── moryaInsure/                 # Frontend React application
│   ├── public/                  # Static assets
│   ├── src/
│   │   ├── Component/           # Reusable components
│   │   │   ├── Navbar.js
│   │   │   ├── Sidebar.js
│   │   │   └── Footer.js
│   │   ├── components/          # Feature-specific components
│   │   │   ├── dashboards/      # Role-based dashboards
│   │   │   └── ProtectedRoute.js
│   │   ├── Pages/               # Page components
│   │   ├── context/             # React context providers
│   │   ├── services/            # API service layer
│   │   └── layout/              # Layout components
│   └── package.json
├── server/                      # Backend Node.js application
│   ├── models/                  # Database models
│   │   ├── User.js
│   │   ├── Policy.js
│   │   ├── Ticket.js
│   │   └── Category.js
│   ├── routes/                  # API routes
│   │   ├── auth.js
│   │   ├── users.js
│   │   ├── policies.js
│   │   ├── tickets.js
│   │   ├── categories.js
│   │   └── reports.js
│   ├── middleware/              # Custom middleware
│   │   └── auth.js
│   ├── server.js               # Main server file
│   ├── .env                    # Environment variables
│   └── package.json
└── README.md
```

## Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- npm or yarn package manager

### Backend Setup

1. **Navigate to server directory**
   ```bash
   cd server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   # Copy and configure environment variables
   cp .env.example .env
   ```
   
   Update `.env` with your configuration:
   ```env
   NODE_ENV=development
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/moryainsurance
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRE=7d
   CLIENT_URL=http://localhost:3000
   ```

4. **Start MongoDB**
   ```bash
   # Using MongoDB service
   sudo systemctl start mongod
   
   # Or using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

5. **Start the server**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd moryaInsure
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create `.env` file in the frontend directory:
   ```env
   REACT_APP_API_URL=http://localhost:5000/api
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## Demo Credentials

### Admin Access
- **Email**: <EMAIL>
- **Password**: admin123
- **Permissions**: Full system access

### Employee Access
- **Email**: <EMAIL>
- **Password**: emp123
- **Permissions**: Policy and ticket management

### Customer Access
- **Email**: <EMAIL>
- **Password**: cust123
- **Permissions**: Personal dashboard and policies

## API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/logout` - Logout

### User Management (Admin Only)
- `GET /api/users` - Get all users
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `PUT /api/users/:id/status` - Toggle user status

### Policy Management
- `GET /api/policies` - Get policies (role-filtered)
- `POST /api/policies` - Create policy
- `PUT /api/policies/:id` - Update policy
- `PUT /api/policies/:id/status` - Update policy status

### Support Tickets
- `GET /api/tickets` - Get tickets (role-filtered)
- `POST /api/tickets` - Create ticket
- `PUT /api/tickets/:id/assign` - Assign ticket
- `POST /api/tickets/:id/comments` - Add comment

### Reports & Analytics
- `GET /api/reports/dashboard` - Dashboard statistics
- `GET /api/reports/policies` - Policy reports
- `GET /api/reports/tickets` - Ticket reports

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Rate Limiting**: API endpoint protection
- **Input Validation**: Comprehensive data validation
- **CORS Protection**: Cross-origin request security
- **Helmet Security**: HTTP header security
- **Account Lockout**: Brute force protection
- **Role-based Access**: Granular permission system

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please contact:
- Email: <EMAIL>
- Documentation: [Project Wiki](link-to-wiki)
- Issues: [GitHub Issues](link-to-issues)

---

**Morya Insurance Management System** - Built with ❤️ for modern insurance management.
