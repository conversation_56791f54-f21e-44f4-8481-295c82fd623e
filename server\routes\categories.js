const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Category = require('../models/Category');
const { authenticate, adminOnly } = require('../middleware/auth');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/temp/' });

// @route   GET /api/categories
// @desc    Get all categories with search and pagination
// @access  Private
router.get('/', authenticate, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('status').optional().isIn(['active', 'inactive', 'all']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { type, parent, search, status } = req.query;

    let query = {};

    // Status filter
    if (status === 'active') {
      query.isActive = true;
    } else if (status === 'inactive') {
      query.isActive = false;
    }
    // If status is 'all' or not provided, don't filter by status

    if (type) {
      query.type = type;
    }

    if (parent === 'null' || parent === '') {
      query.parentCategory = null;
    } else if (parent) {
      query.parentCategory = parent;
    }

    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const categories = await Category.find(query)
      .populate('parentCategory', 'name')
      .populate('createdBy', 'firstName lastName')
      .sort({ sortOrder: 1, name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Category.countDocuments(query);

    res.json({
      success: true,
      data: {
        categories,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching categories'
    });
  }
});

// @route   POST /api/categories
// @desc    Create new category
// @access  Private (Admin only)
router.post('/', authenticate, adminOnly, [
  body('name').trim().isLength({ min: 1, max: 100 }).withMessage('Name must be between 1 and 100 characters'),
  body('type').isIn(['insurance', 'ticket', 'general']).withMessage('Invalid category type'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, description, type, parentCategory, sortOrder, metadata } = req.body;

    const category = new Category({
      name,
      description,
      type,
      parentCategory: parentCategory || null,
      sortOrder: sortOrder || 0,
      metadata,
      createdBy: req.user._id
    });

    await category.save();

    const populatedCategory = await Category.findById(category._id)
      .populate('parentCategory', 'name')
      .populate('createdBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: {
        category: populatedCategory
      }
    });
  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating category'
    });
  }
});

// @route   PUT /api/categories/:id
// @desc    Update category
// @access  Private (Admin only)
router.put('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const allowedUpdates = ['name', 'description', 'sortOrder', 'metadata', 'isActive'];
    const updates = {};
    
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    updates.updatedBy = req.user._id;

    const category = await Category.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate('parentCategory createdBy updatedBy');

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: {
        category
      }
    });
  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating category'
    });
  }
});

// @route   DELETE /api/categories/:id
// @desc    Delete category
// @access  Private (Admin only)
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if category has subcategories
    const subcategories = await Category.find({ parentCategory: req.params.id });
    if (subcategories.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete category with subcategories. Please delete subcategories first.'
      });
    }

    await Category.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting category'
    });
  }
});

// @route   POST /api/categories/import
// @desc    Import categories from CSV/Excel
// @access  Private (Admin only)
router.post('/import', authenticate, adminOnly, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const results = [];
    const errors = [];

    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          let imported = 0;

          for (let i = 0; i < results.length; i++) {
            const row = results[i];

            try {
              // Validate required fields
              if (!row.name) {
                errors.push(`Row ${i + 1}: Category name is required`);
                continue;
              }

              // Check for duplicate
              const existing = await Category.findOne({
                name: row.name.trim(),
                type: row.type || 'insurance'
              });

              if (existing) {
                errors.push(`Row ${i + 1}: Category '${row.name}' already exists`);
                continue;
              }

              // Create category
              const category = new Category({
                name: row.name.trim(),
                description: row.description?.trim(),
                type: row.type || 'insurance',
                isActive: row.status !== 'inactive',
                sortOrder: parseInt(row.sortOrder) || 0,
                createdBy: req.user._id
              });

              await category.save();
              imported++;
            } catch (error) {
              errors.push(`Row ${i + 1}: ${error.message}`);
            }
          }

          // Clean up uploaded file
          fs.unlinkSync(req.file.path);

          res.json({
            success: true,
            message: `Import completed. ${imported} categories imported.`,
            data: {
              imported,
              errors: errors.length > 0 ? errors : undefined
            }
          });
        } catch (error) {
          console.error('Import processing error:', error);
          res.status(500).json({
            success: false,
            message: 'Error processing import file'
          });
        }
      });
  } catch (error) {
    console.error('Import categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during import'
    });
  }
});

// @route   GET /api/categories/search
// @desc    Search categories
// @access  Private
router.get('/search', authenticate, [
  query('q').notEmpty().withMessage('Search query is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { q, type, limit = 10 } = req.query;

    let query = {
      $or: [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } }
      ]
    };

    if (type) {
      query.type = type;
    }

    const categories = await Category.find(query)
      .populate('parentCategory', 'name')
      .sort({ name: 1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Search categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while searching categories'
    });
  }
});

module.exports = router;
