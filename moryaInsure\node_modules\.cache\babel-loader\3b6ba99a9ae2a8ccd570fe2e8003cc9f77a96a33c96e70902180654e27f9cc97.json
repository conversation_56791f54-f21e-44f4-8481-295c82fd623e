{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\Staff.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Table, Button, Modal, Form, Badge, InputGroup } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaUserTie } from 'react-icons/fa';\nimport { userAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Staff = () => {\n  _s();\n  // State for staff data\n  const [staff, setStaff] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showModal, setShowModal] = useState(false);\n  const [selectedStaff, setSelectedStaff] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Form state\n  const [staffForm, setStaffForm] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    role: 'employee',\n    phone: ''\n  });\n\n  // Fetch staff on component mount\n  useEffect(() => {\n    fetchStaff();\n  }, []);\n\n  // Fetch all staff (employees and admins)\n  const fetchStaff = async () => {\n    try {\n      setLoading(true);\n      const response = await userAPI.getUsers({\n        role: 'employee',\n        limit: 100\n      });\n      if (response.success) {\n        setStaff(response.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching staff:', error);\n      alert('Error fetching staff. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle form input changes\n  const handleFormChange = e => {\n    setStaffForm({\n      ...staffForm,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      if (selectedStaff) {\n        // Update existing staff\n        await userAPI.updateUser(selectedStaff._id, staffForm);\n        alert('Staff member updated successfully!');\n      } else {\n        // Create new staff\n        await userAPI.createUser(staffForm);\n        alert('Staff member created successfully!');\n      }\n      setShowModal(false);\n      resetForm();\n      fetchStaff();\n    } catch (error) {\n      console.error('Error saving staff:', error);\n      alert('Error saving staff member. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setStaffForm({\n      firstName: '',\n      lastName: '',\n      email: '',\n      password: '',\n      role: 'employee',\n      phone: ''\n    });\n    setSelectedStaff(null);\n  };\n\n  // Handle edit staff\n  const handleEdit = staffMember => {\n    setSelectedStaff(staffMember);\n    setStaffForm({\n      firstName: staffMember.firstName,\n      lastName: staffMember.lastName,\n      email: staffMember.email,\n      password: '',\n      role: staffMember.role,\n      phone: staffMember.phone || ''\n    });\n    setShowModal(true);\n  };\n\n  // Handle delete staff\n  const handleDelete = async staffId => {\n    if (window.confirm('Are you sure you want to delete this staff member?')) {\n      try {\n        await userAPI.deleteUser(staffId);\n        alert('Staff member deleted successfully!');\n        fetchStaff();\n      } catch (error) {\n        console.error('Error deleting staff:', error);\n        alert('Error deleting staff member. Please try again.');\n      }\n    }\n  };\n\n  // Handle add new staff\n  const handleAddNew = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  // Filter staff based on search term\n  const filteredStaff = staff.filter(member => member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) || member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) || member.email.toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-info mb-0\",\n          children: \"Staff Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Manage employees and administrators\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n            children: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Search staff by name or email...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        className: \"text-end\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"info\",\n          onClick: handleAddNew,\n          children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), \"Add Staff Member\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"bg-info text-white\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Total Staff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: staff.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaUserTie, {\n                className: \"fs-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"bg-success text-white\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Active Staff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: staff.filter(s => s.isActive).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaEye, {\n                className: \"fs-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"bg-warning text-white\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"Admins\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: staff.filter(s => s.role === 'admin').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FaEye, {\n                className: \"fs-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [\"All Staff Members (\", filteredStaff.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner-border\",\n                role: \"status\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"visually-hidden\",\n                  children: \"Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              striped: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Joined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: filteredStaff.length > 0 ? filteredStaff.map(member => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [member.firstName, \" \", member.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 29\n                      }, this), member.isEmailVerified && /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"bi bi-patch-check-fill text-success ms-1\",\n                        title: \"Email Verified\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: member.role === 'admin' ? 'Administrator' : 'Employee'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: member.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: member.role === 'admin' ? 'danger' : 'info',\n                      children: member.role.charAt(0).toUpperCase() + member.role.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: member.phone || /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Not provided\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 46\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: member.isActive ? 'success' : 'danger',\n                      children: member.isActive ? 'Active' : 'Inactive'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [new Date(member.createdAt).toLocaleDateString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: new Date(member.createdAt).toLocaleTimeString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        size: \"sm\",\n                        variant: \"outline-primary\",\n                        onClick: () => handleEdit(member),\n                        title: \"Edit Staff\",\n                        children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 281,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"sm\",\n                        variant: \"outline-danger\",\n                        onClick: () => handleDelete(member._id),\n                        title: \"Delete Staff\",\n                        children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this)]\n                }, member._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"7\",\n                    className: \"text-center py-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bi bi-person-workspace text-muted fs-1 mb-2 d-block\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-muted mb-0\",\n                      children: \"No staff members found\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: searchTerm ? 'Try adjusting your search terms' : 'Add your first staff member to get started'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: selectedStaff ? 'Edit Staff Member' : 'Add New Staff Member'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"First Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"firstName\",\n                  value: staffForm.firstName,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Last Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"lastName\",\n                  value: staffForm.lastName,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  name: \"email\",\n                  value: staffForm.email,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"tel\",\n                  name: \"phone\",\n                  value: staffForm.phone,\n                  onChange: handleFormChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"role\",\n                  value: staffForm.role,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"employee\",\n                    children: \"Employee\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"admin\",\n                    children: \"Administrator\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: [staffForm.role === 'employee' && 'Employee will have limited access to assigned work', staffForm.role === 'admin' && 'Administrator will have full system access']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: [\"Password \", selectedStaff && '(leave blank to keep current)']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  name: \"password\",\n                  value: staffForm.password,\n                  onChange: handleFormChange,\n                  required: !selectedStaff\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Note:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), \" Staff members will be able to access the employee dashboard and manage assigned work. Administrators will have full system access including user management.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"info\",\n            type: \"submit\",\n            disabled: loading,\n            children: loading ? 'Saving...' : selectedStaff ? 'Update Staff' : 'Create Staff'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(Staff, \"/nrRuyistnijOfw0LYNdAM6/Ib8=\");\n_c = Staff;\nexport default Staff;\nvar _c;\n$RefreshReg$(_c, \"Staff\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Badge", "InputGroup", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaSearch", "FaUserTie", "userAPI", "jsxDEV", "_jsxDEV", "Staff", "_s", "staff", "set<PERSON>taff", "loading", "setLoading", "showModal", "setShowModal", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedStaff", "searchTerm", "setSearchTerm", "staffForm", "setStaffForm", "firstName", "lastName", "email", "password", "role", "phone", "fetchStaff", "response", "getUsers", "limit", "success", "data", "users", "error", "console", "alert", "handleFormChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "updateUser", "_id", "createUser", "resetForm", "handleEdit", "staffMember", "handleDelete", "staffId", "window", "confirm", "deleteUser", "handleAddNew", "filteredStaff", "filter", "member", "toLowerCase", "includes", "fluid", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "md", "Text", "Control", "type", "placeholder", "onChange", "variant", "onClick", "Body", "length", "s", "isActive", "Header", "responsive", "striped", "hover", "map", "isEmailVerified", "title", "bg", "char<PERSON>t", "toUpperCase", "slice", "Date", "createdAt", "toLocaleDateString", "toLocaleTimeString", "size", "colSpan", "show", "onHide", "closeButton", "Title", "onSubmit", "Group", "Label", "required", "Select", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/Staff.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Container, Row, Col, Card, Table, Button, Modal, Form, Badge, InputGroup } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaUserTie } from 'react-icons/fa';\r\nimport { userAPI } from '../services/api';\r\n\r\nconst Staff = () => {\r\n  // State for staff data\r\n  const [staff, setStaff] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [selectedStaff, setSelectedStaff] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // Form state\r\n  const [staffForm, setStaffForm] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    password: '',\r\n    role: 'employee',\r\n    phone: ''\r\n  });\r\n\r\n  // Fetch staff on component mount\r\n  useEffect(() => {\r\n    fetchStaff();\r\n  }, []);\r\n\r\n  // Fetch all staff (employees and admins)\r\n  const fetchStaff = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await userAPI.getUsers({ role: 'employee', limit: 100 });\r\n      if (response.success) {\r\n        setStaff(response.data.users);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching staff:', error);\r\n      alert('Error fetching staff. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle form input changes\r\n  const handleFormChange = (e) => {\r\n    setStaffForm({\r\n      ...staffForm,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      setLoading(true);\r\n      if (selectedStaff) {\r\n        // Update existing staff\r\n        await userAPI.updateUser(selectedStaff._id, staffForm);\r\n        alert('Staff member updated successfully!');\r\n      } else {\r\n        // Create new staff\r\n        await userAPI.createUser(staffForm);\r\n        alert('Staff member created successfully!');\r\n      }\r\n      setShowModal(false);\r\n      resetForm();\r\n      fetchStaff();\r\n    } catch (error) {\r\n      console.error('Error saving staff:', error);\r\n      alert('Error saving staff member. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Reset form\r\n  const resetForm = () => {\r\n    setStaffForm({\r\n      firstName: '',\r\n      lastName: '',\r\n      email: '',\r\n      password: '',\r\n      role: 'employee',\r\n      phone: ''\r\n    });\r\n    setSelectedStaff(null);\r\n  };\r\n\r\n  // Handle edit staff\r\n  const handleEdit = (staffMember) => {\r\n    setSelectedStaff(staffMember);\r\n    setStaffForm({\r\n      firstName: staffMember.firstName,\r\n      lastName: staffMember.lastName,\r\n      email: staffMember.email,\r\n      password: '',\r\n      role: staffMember.role,\r\n      phone: staffMember.phone || ''\r\n    });\r\n    setShowModal(true);\r\n  };\r\n\r\n  // Handle delete staff\r\n  const handleDelete = async (staffId) => {\r\n    if (window.confirm('Are you sure you want to delete this staff member?')) {\r\n      try {\r\n        await userAPI.deleteUser(staffId);\r\n        alert('Staff member deleted successfully!');\r\n        fetchStaff();\r\n      } catch (error) {\r\n        console.error('Error deleting staff:', error);\r\n        alert('Error deleting staff member. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle add new staff\r\n  const handleAddNew = () => {\r\n    resetForm();\r\n    setShowModal(true);\r\n  };\r\n\r\n  // Filter staff based on search term\r\n  const filteredStaff = staff.filter(member =>\r\n    member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    member.email.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <Container fluid className=\"py-4\">\r\n      <Row className=\"mb-4\">\r\n        <Col>\r\n          <h2 className=\"text-info mb-0\">Staff Management</h2>\r\n          <p className=\"text-muted\">Manage employees and administrators</p>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Action Bar */}\r\n      <Row className=\"mb-4\">\r\n        <Col md={6}>\r\n          <InputGroup>\r\n            <InputGroup.Text>\r\n              <FaSearch />\r\n            </InputGroup.Text>\r\n            <Form.Control\r\n              type=\"text\"\r\n              placeholder=\"Search staff by name or email...\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n            />\r\n          </InputGroup>\r\n        </Col>\r\n        <Col md={6} className=\"text-end\">\r\n          <Button\r\n            variant=\"info\"\r\n            onClick={handleAddNew}\r\n          >\r\n            <FaPlus className=\"me-2\" />\r\n            Add Staff Member\r\n          </Button>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Statistics Cards */}\r\n      <Row className=\"mb-4\">\r\n        <Col md={4}>\r\n          <Card className=\"bg-info text-white\">\r\n            <Card.Body>\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6>Total Staff</h6>\r\n                  <h4>{staff.length}</h4>\r\n                </div>\r\n                <FaUserTie className=\"fs-2\" />\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        <Col md={4}>\r\n          <Card className=\"bg-success text-white\">\r\n            <Card.Body>\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6>Active Staff</h6>\r\n                  <h4>{staff.filter(s => s.isActive).length}</h4>\r\n                </div>\r\n                <FaEye className=\"fs-2\" />\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n        <Col md={4}>\r\n          <Card className=\"bg-warning text-white\">\r\n            <Card.Body>\r\n              <div className=\"d-flex justify-content-between\">\r\n                <div>\r\n                  <h6>Admins</h6>\r\n                  <h4>{staff.filter(s => s.role === 'admin').length}</h4>\r\n                </div>\r\n                <FaEye className=\"fs-2\" />\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Staff Table */}\r\n      <Row>\r\n        <Col>\r\n          <Card>\r\n            <Card.Header>\r\n              <h5 className=\"mb-0\">All Staff Members ({filteredStaff.length})</h5>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {loading ? (\r\n                <div className=\"text-center py-4\">\r\n                  <div className=\"spinner-border\" role=\"status\">\r\n                    <span className=\"visually-hidden\">Loading...</span>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <Table responsive striped hover>\r\n                  <thead>\r\n                    <tr>\r\n                      <th>Name</th>\r\n                      <th>Email</th>\r\n                      <th>Role</th>\r\n                      <th>Phone</th>\r\n                      <th>Status</th>\r\n                      <th>Joined</th>\r\n                      <th>Actions</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {filteredStaff.length > 0 ? filteredStaff.map((member) => (\r\n                      <tr key={member._id}>\r\n                        <td>\r\n                          <div>\r\n                            <strong>{member.firstName} {member.lastName}</strong>\r\n                            {member.isEmailVerified && (\r\n                              <i className=\"bi bi-patch-check-fill text-success ms-1\" title=\"Email Verified\"></i>\r\n                            )}\r\n                            <br />\r\n                            <small className=\"text-muted\">\r\n                              {member.role === 'admin' ? 'Administrator' : 'Employee'}\r\n                            </small>\r\n                          </div>\r\n                        </td>\r\n                        <td>{member.email}</td>\r\n                        <td>\r\n                          <Badge bg={member.role === 'admin' ? 'danger' : 'info'}>\r\n                            {member.role.charAt(0).toUpperCase() + member.role.slice(1)}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>{member.phone || <span className=\"text-muted\">Not provided</span>}</td>\r\n                        <td>\r\n                          <Badge bg={member.isActive ? 'success' : 'danger'}>\r\n                            {member.isActive ? 'Active' : 'Inactive'}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>\r\n                          <div>\r\n                            {new Date(member.createdAt).toLocaleDateString()}\r\n                            <br />\r\n                            <small className=\"text-muted\">\r\n                              {new Date(member.createdAt).toLocaleTimeString()}\r\n                            </small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex gap-1\">\r\n                            <Button\r\n                              size=\"sm\"\r\n                              variant=\"outline-primary\"\r\n                              onClick={() => handleEdit(member)}\r\n                              title=\"Edit Staff\"\r\n                            >\r\n                              <FaEdit />\r\n                            </Button>\r\n                            <Button\r\n                              size=\"sm\"\r\n                              variant=\"outline-danger\"\r\n                              onClick={() => handleDelete(member._id)}\r\n                              title=\"Delete Staff\"\r\n                            >\r\n                              <FaTrash />\r\n                            </Button>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    )) : (\r\n                      <tr>\r\n                        <td colSpan=\"7\" className=\"text-center py-4\">\r\n                          <i className=\"bi bi-person-workspace text-muted fs-1 mb-2 d-block\"></i>\r\n                          <p className=\"text-muted mb-0\">No staff members found</p>\r\n                          <small className=\"text-muted\">\r\n                            {searchTerm ? 'Try adjusting your search terms' : 'Add your first staff member to get started'}\r\n                          </small>\r\n                        </td>\r\n                      </tr>\r\n                    )}\r\n                  </tbody>\r\n                </Table>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Staff Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {selectedStaff ? 'Edit Staff Member' : 'Add New Staff Member'}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Form onSubmit={handleSubmit}>\r\n          <Modal.Body>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>First Name</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"firstName\"\r\n                    value={staffForm.firstName}\r\n                    onChange={handleFormChange}\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Last Name</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"lastName\"\r\n                    value={staffForm.lastName}\r\n                    onChange={handleFormChange}\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Email</Form.Label>\r\n                  <Form.Control\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={staffForm.email}\r\n                    onChange={handleFormChange}\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Phone</Form.Label>\r\n                  <Form.Control\r\n                    type=\"tel\"\r\n                    name=\"phone\"\r\n                    value={staffForm.phone}\r\n                    onChange={handleFormChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Role</Form.Label>\r\n                  <Form.Select\r\n                    name=\"role\"\r\n                    value={staffForm.role}\r\n                    onChange={handleFormChange}\r\n                    required\r\n                  >\r\n                    <option value=\"employee\">Employee</option>\r\n                    <option value=\"admin\">Administrator</option>\r\n                  </Form.Select>\r\n                  <Form.Text className=\"text-muted\">\r\n                    {staffForm.role === 'employee' && 'Employee will have limited access to assigned work'}\r\n                    {staffForm.role === 'admin' && 'Administrator will have full system access'}\r\n                  </Form.Text>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Password {selectedStaff && '(leave blank to keep current)'}</Form.Label>\r\n                  <Form.Control\r\n                    type=\"password\"\r\n                    name=\"password\"\r\n                    value={staffForm.password}\r\n                    onChange={handleFormChange}\r\n                    required={!selectedStaff}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n            <div className=\"alert alert-info\">\r\n              <small>\r\n                <strong>Note:</strong> Staff members will be able to access the employee dashboard and manage assigned work.\r\n                Administrators will have full system access including user management.\r\n              </small>\r\n            </div>\r\n          </Modal.Body>\r\n          <Modal.Footer>\r\n            <Button variant=\"secondary\" onClick={() => setShowModal(false)}>\r\n              Cancel\r\n            </Button>\r\n            <Button variant=\"info\" type=\"submit\" disabled={loading}>\r\n              {loading ? 'Saving...' : selectedStaff ? 'Update Staff' : 'Create Staff'}\r\n            </Button>\r\n          </Modal.Footer>\r\n        </Form>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Staff;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AAC1G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC;IACzCmC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACAvC,SAAS,CAAC,MAAM;IACdwC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,QAAQ,GAAG,MAAMxB,OAAO,CAACyB,QAAQ,CAAC;QAAEJ,IAAI,EAAE,UAAU;QAAEK,KAAK,EAAE;MAAI,CAAC,CAAC;MACzE,IAAIF,QAAQ,CAACG,OAAO,EAAE;QACpBrB,QAAQ,CAACkB,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CE,KAAK,CAAC,yCAAyC,CAAC;IAClD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyB,gBAAgB,GAAIC,CAAC,IAAK;IAC9BlB,YAAY,CAAC;MACX,GAAGD,SAAS;MACZ,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIG,aAAa,EAAE;QACjB;QACA,MAAMX,OAAO,CAACwC,UAAU,CAAC7B,aAAa,CAAC8B,GAAG,EAAE1B,SAAS,CAAC;QACtDiB,KAAK,CAAC,oCAAoC,CAAC;MAC7C,CAAC,MAAM;QACL;QACA,MAAMhC,OAAO,CAAC0C,UAAU,CAAC3B,SAAS,CAAC;QACnCiB,KAAK,CAAC,oCAAoC,CAAC;MAC7C;MACAtB,YAAY,CAAC,KAAK,CAAC;MACnBiC,SAAS,CAAC,CAAC;MACXpB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CE,KAAK,CAAC,8CAA8C,CAAC;IACvD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,SAAS,GAAGA,CAAA,KAAM;IACtB3B,YAAY,CAAC;MACXC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFV,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAIC,WAAW,IAAK;IAClCjC,gBAAgB,CAACiC,WAAW,CAAC;IAC7B7B,YAAY,CAAC;MACXC,SAAS,EAAE4B,WAAW,CAAC5B,SAAS;MAChCC,QAAQ,EAAE2B,WAAW,CAAC3B,QAAQ;MAC9BC,KAAK,EAAE0B,WAAW,CAAC1B,KAAK;MACxBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAEwB,WAAW,CAACxB,IAAI;MACtBC,KAAK,EAAEuB,WAAW,CAACvB,KAAK,IAAI;IAC9B,CAAC,CAAC;IACFZ,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoC,YAAY,GAAG,MAAOC,OAAO,IAAK;IACtC,IAAIC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACxE,IAAI;QACF,MAAMjD,OAAO,CAACkD,UAAU,CAACH,OAAO,CAAC;QACjCf,KAAK,CAAC,oCAAoC,CAAC;QAC3CT,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CE,KAAK,CAAC,gDAAgD,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzBR,SAAS,CAAC,CAAC;IACXjC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAM0C,aAAa,GAAG/C,KAAK,CAACgD,MAAM,CAACC,MAAM,IACvCA,MAAM,CAACrC,SAAS,CAACsC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC,IACjED,MAAM,CAACpC,QAAQ,CAACqC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC,IAChED,MAAM,CAACnC,KAAK,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAC9D,CAAC;EAED,oBACErD,OAAA,CAAClB,SAAS;IAACyE,KAAK;IAACC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAC/BzD,OAAA,CAACjB,GAAG;MAACyE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBzD,OAAA,CAAChB,GAAG;QAAAyE,QAAA,gBACFzD,OAAA;UAAIwD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD7D,OAAA;UAAGwD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACjB,GAAG;MAACyE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzD,OAAA,CAAChB,GAAG;QAAC8E,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTzD,OAAA,CAACT,UAAU;UAAAkE,QAAA,gBACTzD,OAAA,CAACT,UAAU,CAACwE,IAAI;YAAAN,QAAA,eACdzD,OAAA,CAACJ,QAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAClB7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;YACXC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kCAAkC;YAC9C/B,KAAK,EAAExB,UAAW;YAClBwD,QAAQ,EAAGnC,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAACC,MAAM,CAACE,KAAK;UAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN7D,OAAA,CAAChB,GAAG;QAAC8E,EAAE,EAAE,CAAE;QAACN,SAAS,EAAC,UAAU;QAAAC,QAAA,eAC9BzD,OAAA,CAACb,MAAM;UACLiF,OAAO,EAAC,MAAM;UACdC,OAAO,EAAEpB,YAAa;UAAAQ,QAAA,gBAEtBzD,OAAA,CAACR,MAAM;YAACgE,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACjB,GAAG;MAACyE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzD,OAAA,CAAChB,GAAG;QAAC8E,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTzD,OAAA,CAACf,IAAI;UAACuE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eAClCzD,OAAA,CAACf,IAAI,CAACqF,IAAI;YAAAb,QAAA,eACRzD,OAAA;cAAKwD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzD,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB7D,OAAA;kBAAAyD,QAAA,EAAKtD,KAAK,CAACoE;gBAAM;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACN7D,OAAA,CAACH,SAAS;gBAAC2D,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAAChB,GAAG;QAAC8E,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTzD,OAAA,CAACf,IAAI;UAACuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACrCzD,OAAA,CAACf,IAAI,CAACqF,IAAI;YAAAb,QAAA,eACRzD,OAAA;cAAKwD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzD,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrB7D,OAAA;kBAAAyD,QAAA,EAAKtD,KAAK,CAACgD,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAACF;gBAAM;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN7D,OAAA,CAACL,KAAK;gBAAC6D,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAAChB,GAAG;QAAC8E,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTzD,OAAA,CAACf,IAAI;UAACuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACrCzD,OAAA,CAACf,IAAI,CAACqF,IAAI;YAAAb,QAAA,eACRzD,OAAA;cAAKwD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CzD,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAAyD,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACf7D,OAAA;kBAAAyD,QAAA,EAAKtD,KAAK,CAACgD,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACrD,IAAI,KAAK,OAAO,CAAC,CAACoD;gBAAM;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACN7D,OAAA,CAACL,KAAK;gBAAC6D,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACjB,GAAG;MAAA0E,QAAA,eACFzD,OAAA,CAAChB,GAAG;QAAAyE,QAAA,eACFzD,OAAA,CAACf,IAAI;UAAAwE,QAAA,gBACHzD,OAAA,CAACf,IAAI,CAACyF,MAAM;YAAAjB,QAAA,eACVzD,OAAA;cAAIwD,SAAS,EAAC,MAAM;cAAAC,QAAA,GAAC,qBAAmB,EAACP,aAAa,CAACqB,MAAM,EAAC,GAAC;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACd7D,OAAA,CAACf,IAAI,CAACqF,IAAI;YAAAb,QAAA,EACPpD,OAAO,gBACNL,OAAA;cAAKwD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BzD,OAAA;gBAAKwD,SAAS,EAAC,gBAAgB;gBAACrC,IAAI,EAAC,QAAQ;gBAAAsC,QAAA,eAC3CzD,OAAA;kBAAMwD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN7D,OAAA,CAACd,KAAK;cAACyF,UAAU;cAACC,OAAO;cAACC,KAAK;cAAApB,QAAA,gBAC7BzD,OAAA;gBAAAyD,QAAA,eACEzD,OAAA;kBAAAyD,QAAA,gBACEzD,OAAA;oBAAAyD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf7D,OAAA;oBAAAyD,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7D,OAAA;gBAAAyD,QAAA,EACGP,aAAa,CAACqB,MAAM,GAAG,CAAC,GAAGrB,aAAa,CAAC4B,GAAG,CAAE1B,MAAM,iBACnDpD,OAAA;kBAAAyD,QAAA,gBACEzD,OAAA;oBAAAyD,QAAA,eACEzD,OAAA;sBAAAyD,QAAA,gBACEzD,OAAA;wBAAAyD,QAAA,GAASL,MAAM,CAACrC,SAAS,EAAC,GAAC,EAACqC,MAAM,CAACpC,QAAQ;sBAAA;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,EACpDT,MAAM,CAAC2B,eAAe,iBACrB/E,OAAA;wBAAGwD,SAAS,EAAC,0CAA0C;wBAACwB,KAAK,EAAC;sBAAgB;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CACnF,eACD7D,OAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN7D,OAAA;wBAAOwD,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAC1BL,MAAM,CAACjC,IAAI,KAAK,OAAO,GAAG,eAAe,GAAG;sBAAU;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,EAAKL,MAAM,CAACnC;kBAAK;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvB7D,OAAA;oBAAAyD,QAAA,eACEzD,OAAA,CAACV,KAAK;sBAAC2F,EAAE,EAAE7B,MAAM,CAACjC,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,MAAO;sBAAAsC,QAAA,EACpDL,MAAM,CAACjC,IAAI,CAAC+D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/B,MAAM,CAACjC,IAAI,CAACiE,KAAK,CAAC,CAAC;oBAAC;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,EAAKL,MAAM,CAAChC,KAAK,iBAAIpB,OAAA;sBAAMwD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3E7D,OAAA;oBAAAyD,QAAA,eACEzD,OAAA,CAACV,KAAK;sBAAC2F,EAAE,EAAE7B,MAAM,CAACqB,QAAQ,GAAG,SAAS,GAAG,QAAS;sBAAAhB,QAAA,EAC/CL,MAAM,CAACqB,QAAQ,GAAG,QAAQ,GAAG;oBAAU;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,eACEzD,OAAA;sBAAAyD,QAAA,GACG,IAAI4B,IAAI,CAACjC,MAAM,CAACkC,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,eAChDvF,OAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN7D,OAAA;wBAAOwD,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAC1B,IAAI4B,IAAI,CAACjC,MAAM,CAACkC,SAAS,CAAC,CAACE,kBAAkB,CAAC;sBAAC;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL7D,OAAA;oBAAAyD,QAAA,eACEzD,OAAA;sBAAKwD,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BzD,OAAA,CAACb,MAAM;wBACLsG,IAAI,EAAC,IAAI;wBACTrB,OAAO,EAAC,iBAAiB;wBACzBC,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAACU,MAAM,CAAE;wBAClC4B,KAAK,EAAC,YAAY;wBAAAvB,QAAA,eAElBzD,OAAA,CAACP,MAAM;0BAAAiE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACT7D,OAAA,CAACb,MAAM;wBACLsG,IAAI,EAAC,IAAI;wBACTrB,OAAO,EAAC,gBAAgB;wBACxBC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAACQ,MAAM,CAACb,GAAG,CAAE;wBACxCyC,KAAK,EAAC,cAAc;wBAAAvB,QAAA,eAEpBzD,OAAA,CAACN,OAAO;0BAAAgE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GArDET,MAAM,CAACb,GAAG;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsDf,CACL,CAAC,gBACA7D,OAAA;kBAAAyD,QAAA,eACEzD,OAAA;oBAAI0F,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC1CzD,OAAA;sBAAGwD,SAAS,EAAC;oBAAqD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE7D,OAAA;sBAAGwD,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzD7D,OAAA;sBAAOwD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAC1B9C,UAAU,GAAG,iCAAiC,GAAG;oBAA4C;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACL;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACZ,KAAK;MAACuG,IAAI,EAAEpF,SAAU;MAACqF,MAAM,EAAEA,CAAA,KAAMpF,YAAY,CAAC,KAAK,CAAE;MAACiF,IAAI,EAAC,IAAI;MAAAhC,QAAA,gBAClEzD,OAAA,CAACZ,KAAK,CAACsF,MAAM;QAACmB,WAAW;QAAApC,QAAA,eACvBzD,OAAA,CAACZ,KAAK,CAAC0G,KAAK;UAAArC,QAAA,EACThD,aAAa,GAAG,mBAAmB,GAAG;QAAsB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf7D,OAAA,CAACX,IAAI;QAAC0G,QAAQ,EAAE3D,YAAa;QAAAqB,QAAA,gBAC3BzD,OAAA,CAACZ,KAAK,CAACkF,IAAI;UAAAb,QAAA,gBACTzD,OAAA,CAACjB,GAAG;YAAA0E,QAAA,gBACFzD,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEtB,SAAS,CAACE,SAAU;kBAC3BoD,QAAQ,EAAEpC,gBAAiB;kBAC3BmE,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClC7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEtB,SAAS,CAACG,QAAS;kBAC1BmD,QAAQ,EAAEpC,gBAAiB;kBAC3BmE,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA,CAACjB,GAAG;YAAA0E,QAAA,gBACFzD,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9B7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZ/B,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEtB,SAAS,CAACI,KAAM;kBACvBkD,QAAQ,EAAEpC,gBAAiB;kBAC3BmE,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9B7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;kBACXC,IAAI,EAAC,KAAK;kBACV/B,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEtB,SAAS,CAACO,KAAM;kBACvB+C,QAAQ,EAAEpC;gBAAiB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA,CAACjB,GAAG;YAAA0E,QAAA,gBACFzD,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B7D,OAAA,CAACX,IAAI,CAAC8G,MAAM;kBACVjE,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEtB,SAAS,CAACM,IAAK;kBACtBgD,QAAQ,EAAEpC,gBAAiB;kBAC3BmE,QAAQ;kBAAAzC,QAAA,gBAERzD,OAAA;oBAAQmC,KAAK,EAAC,UAAU;oBAAAsB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1C7D,OAAA;oBAAQmC,KAAK,EAAC,OAAO;oBAAAsB,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACd7D,OAAA,CAACX,IAAI,CAAC0E,IAAI;kBAACP,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAC9B5C,SAAS,CAACM,IAAI,KAAK,UAAU,IAAI,oDAAoD,EACrFN,SAAS,CAACM,IAAI,KAAK,OAAO,IAAI,4CAA4C;gBAAA;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAAChB,GAAG;cAAC8E,EAAE,EAAE,CAAE;cAAAL,QAAA,eACTzD,OAAA,CAACX,IAAI,CAAC2G,KAAK;gBAACxC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BzD,OAAA,CAACX,IAAI,CAAC4G,KAAK;kBAAAxC,QAAA,GAAC,WAAS,EAAChD,aAAa,IAAI,+BAA+B;gBAAA;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpF7D,OAAA,CAACX,IAAI,CAAC2E,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACf/B,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEtB,SAAS,CAACK,QAAS;kBAC1BiD,QAAQ,EAAEpC,gBAAiB;kBAC3BmE,QAAQ,EAAE,CAACzF;gBAAc;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7D,OAAA;YAAKwD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BzD,OAAA;cAAAyD,QAAA,gBACEzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iKAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACb7D,OAAA,CAACZ,KAAK,CAACgH,MAAM;UAAA3C,QAAA,gBACXzD,OAAA,CAACb,MAAM;YAACiF,OAAO,EAAC,WAAW;YAACC,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,KAAK,CAAE;YAAAiD,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAACb,MAAM;YAACiF,OAAO,EAAC,MAAM;YAACH,IAAI,EAAC,QAAQ;YAACoC,QAAQ,EAAEhG,OAAQ;YAAAoD,QAAA,EACpDpD,OAAO,GAAG,WAAW,GAAGI,aAAa,GAAG,cAAc,GAAG;UAAc;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC3D,EAAA,CAlaID,KAAK;AAAAqG,EAAA,GAALrG,KAAK;AAoaX,eAAeA,KAAK;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}