{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\Categories.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Table, Form, Modal, Alert, Spinner, Container, Row, Col, Card } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\nimport { categoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesPage = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [search, setSearch] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // New category modal states\n  const [showNewModal, setShowNewModal] = useState(false);\n  const [newCategory, setNewCategory] = useState({\n    name: '',\n    description: '',\n    type: 'insurance',\n    isActive: true\n  });\n\n  // Edit modal states\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [editCategory, setEditCategory] = useState(null);\n\n  // Import modal states\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n  useEffect(() => {\n    fetchCategories();\n  }, []);\n  const fetchCategories = async () => {\n    try {\n      setLoading(true);\n      const response = await categoriesAPI.getCategories();\n      if (response.success) {\n        setCategories(response.data.categories || []);\n      } else {\n        setError('Failed to fetch categories');\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      setError('Failed to fetch categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredCategories = categories.filter(cat => cat.name.toLowerCase().includes(search.toLowerCase()));\n\n  // Handlers for new category modal\n  const handleNewInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setNewCategory(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleAddCategory = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await categoriesAPI.createCategory(newCategory);\n      if (response.success) {\n        setSuccess('Category created successfully!');\n        setNewCategory({\n          name: '',\n          description: '',\n          type: 'insurance',\n          isActive: true\n        });\n        setShowNewModal(false);\n        fetchCategories(); // Refresh the list\n      } else {\n        setError(response.message || 'Failed to create category');\n      }\n    } catch (error) {\n      console.error('Error creating category:', error);\n      setError('Failed to create category');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEditCategory = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await categoriesAPI.updateCategory(editCategory._id, editCategory);\n      if (response.success) {\n        setSuccess('Category updated successfully!');\n        setShowEditModal(false);\n        setEditCategory(null);\n        fetchCategories(); // Refresh the list\n      } else {\n        setError(response.message || 'Failed to update category');\n      }\n    } catch (error) {\n      console.error('Error updating category:', error);\n      setError('Failed to update category');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDeleteCategory = async categoryId => {\n    if (window.confirm('Are you sure you want to delete this category?')) {\n      try {\n        const response = await categoriesAPI.deleteCategory(categoryId);\n        if (response.success) {\n          setSuccess('Category deleted successfully!');\n          fetchCategories(); // Refresh the list\n        } else {\n          setError(response.message || 'Failed to delete category');\n        }\n      } catch (error) {\n        console.error('Error deleting category:', error);\n        setError('Failed to delete category');\n      }\n    }\n  };\n  const openEditModal = category => {\n    setEditCategory({\n      ...category\n    });\n    setShowEditModal(true);\n  };\n\n  // Handlers for import modal\n  const handleFileChange = e => {\n    setSelectedFile(e.target.files[0]);\n  };\n  const handleFileUpload = () => {\n    if (selectedFile) {\n      alert(`Uploaded file: ${selectedFile.name}`);\n      setSelectedFile(null);\n      setShowImportModal(false);\n    } else {\n      alert('Please select a file first.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Insurance Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowNewModal(true),\n        children: \"New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowImportModal(true),\n        children: \"Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 w-100 w-md-50\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search categories...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredCategories.map(cat => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: cat.categoryName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge bg-success\",\n              children: cat.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, cat.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showNewModal,\n      onHide: () => setShowNewModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add New Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Category Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              name: \"categoryName\",\n              placeholder: \"Enter category name\",\n              value: newCategory.categoryName,\n              onChange: handleNewInputChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"status\",\n              value: newCategory.status,\n              onChange: handleNewInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowNewModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleAddCategory,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImportModal,\n      onHide: () => setShowImportModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Import Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select file (CSV, Excel)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel\",\n              onChange: handleFileChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImportModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesPage, \"hLFw/Sr1EASnmhnvJZFOTazZNmM=\");\n_c = CategoriesPage;\nexport default CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Table", "Form", "Modal", "<PERSON><PERSON>", "Spinner", "Container", "Row", "Col", "Card", "FaPlus", "FaEdit", "FaTrash", "FaFileImport", "FaSearch", "categoriesAPI", "jsxDEV", "_jsxDEV", "CategoriesPage", "_s", "categories", "setCategories", "search", "setSearch", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showNewModal", "setShowNewModal", "newCategory", "setNewCategory", "name", "description", "type", "isActive", "showEditModal", "setShowEditModal", "editCategory", "setEditCategory", "showImportModal", "setShowImportModal", "selectedFile", "setSelectedFile", "submitting", "setSubmitting", "fetchCategories", "response", "getCategories", "data", "console", "filteredCategories", "filter", "cat", "toLowerCase", "includes", "handleNewInputChange", "e", "value", "checked", "target", "prev", "handleAddCategory", "createCategory", "message", "handleEditCategory", "updateCategory", "_id", "handleDeleteCategory", "categoryId", "window", "confirm", "deleteCategory", "openEditModal", "category", "handleFileChange", "files", "handleFileUpload", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "categoryName", "status", "id", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "accept", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/Categories.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { But<PERSON>, Table, Form, Modal, <PERSON><PERSON>, Spinner, Container, Row, Col, Card } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';\r\nimport { categoriesAPI } from '../services/api';\r\n\r\nconst CategoriesPage = () => {\r\n  const [categories, setCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n\r\n  // New category modal states\r\n  const [showNewModal, setShowNewModal] = useState(false);\r\n  const [newCategory, setNewCategory] = useState({\r\n    name: '',\r\n    description: '',\r\n    type: 'insurance',\r\n    isActive: true,\r\n  });\r\n\r\n  // Edit modal states\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [editCategory, setEditCategory] = useState(null);\r\n\r\n  // Import modal states\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  const fetchCategories = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await categoriesAPI.getCategories();\r\n      if (response.success) {\r\n        setCategories(response.data.categories || []);\r\n      } else {\r\n        setError('Failed to fetch categories');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching categories:', error);\r\n      setError('Failed to fetch categories');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const filteredCategories = categories.filter((cat) =>\r\n    cat.name.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  // Handlers for new category modal\r\n  const handleNewInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setNewCategory((prev) => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleAddCategory = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await categoriesAPI.createCategory(newCategory);\r\n      if (response.success) {\r\n        setSuccess('Category created successfully!');\r\n        setNewCategory({ name: '', description: '', type: 'insurance', isActive: true });\r\n        setShowNewModal(false);\r\n        fetchCategories(); // Refresh the list\r\n      } else {\r\n        setError(response.message || 'Failed to create category');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating category:', error);\r\n      setError('Failed to create category');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleEditCategory = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await categoriesAPI.updateCategory(editCategory._id, editCategory);\r\n      if (response.success) {\r\n        setSuccess('Category updated successfully!');\r\n        setShowEditModal(false);\r\n        setEditCategory(null);\r\n        fetchCategories(); // Refresh the list\r\n      } else {\r\n        setError(response.message || 'Failed to update category');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating category:', error);\r\n      setError('Failed to update category');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteCategory = async (categoryId) => {\r\n    if (window.confirm('Are you sure you want to delete this category?')) {\r\n      try {\r\n        const response = await categoriesAPI.deleteCategory(categoryId);\r\n        if (response.success) {\r\n          setSuccess('Category deleted successfully!');\r\n          fetchCategories(); // Refresh the list\r\n        } else {\r\n          setError(response.message || 'Failed to delete category');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error deleting category:', error);\r\n        setError('Failed to delete category');\r\n      }\r\n    }\r\n  };\r\n\r\n  const openEditModal = (category) => {\r\n    setEditCategory({ ...category });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  // Handlers for import modal\r\n  const handleFileChange = (e) => {\r\n    setSelectedFile(e.target.files[0]);\r\n  };\r\n\r\n  const handleFileUpload = () => {\r\n    if (selectedFile) {\r\n      alert(`Uploaded file: ${selectedFile.name}`);\r\n      setSelectedFile(null);\r\n      setShowImportModal(false);\r\n    } else {\r\n      alert('Please select a file first.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      {/* Header */}\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h3 className=\"fw-bold text-uppercase\">Insurance Category</h3>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"mb-3 d-flex gap-2\">\r\n        <Button variant=\"primary\" onClick={() => setShowNewModal(true)}>New</Button>\r\n        <Button variant=\"secondary\" onClick={() => setShowImportModal(true)}>Import</Button>\r\n      </div>\r\n\r\n      {/* Export Buttons */}\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-3 w-100 w-md-50\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search categories...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Category Name</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredCategories.map((cat) => (\r\n            <tr key={cat.id}>\r\n              <td>{cat.categoryName}</td>\r\n              <td><span className=\"badge bg-success\">{cat.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\">\r\n                  <FaEdit />\r\n                </Button>\r\n                <Button variant=\"danger\" size=\"sm\">\r\n                  <FaTrash />\r\n                </Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal - New Category */}\r\n      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add New Category</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Category Name</Form.Label>\r\n              <Form.Control\r\n                type=\"text\"\r\n                name=\"categoryName\"\r\n                placeholder=\"Enter category name\"\r\n                value={newCategory.categoryName}\r\n                onChange={handleNewInputChange}\r\n              />\r\n            </Form.Group>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Status</Form.Label>\r\n              <Form.Select\r\n                name=\"status\"\r\n                value={newCategory.status}\r\n                onChange={handleNewInputChange}\r\n              >\r\n                <option value=\"Active\">Active</option>\r\n                <option value=\"Inactive\">Inactive</option>\r\n              </Form.Select>\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowNewModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleAddCategory}>\r\n            Save Changes\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Modal - Import File */}\r\n      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Import Categories</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group>\r\n              <Form.Label>Select file (CSV, Excel)</Form.Label>\r\n              <Form.Control type=\"file\" accept=\".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel\" onChange={handleFileChange} />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowImportModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={handleFileUpload}>\r\n            Upload\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CategoriesPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAQ,iBAAiB;AACvG,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,gBAAgB;AAChF,SAASC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC;IAC7CmC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAEnDD,SAAS,CAAC,MAAM;IACdkD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAMlC,aAAa,CAACmC,aAAa,CAAC,CAAC;MACpD,IAAID,QAAQ,CAACrB,OAAO,EAAE;QACpBP,aAAa,CAAC4B,QAAQ,CAACE,IAAI,CAAC/B,UAAU,IAAI,EAAE,CAAC;MAC/C,CAAC,MAAM;QACLO,QAAQ,CAAC,4BAA4B,CAAC;MACxC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,kBAAkB,GAAGjC,UAAU,CAACkC,MAAM,CAAEC,GAAG,IAC/CA,GAAG,CAACrB,IAAI,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,MAAM,CAACkC,WAAW,CAAC,CAAC,CACtD,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAM;MAAEzB,IAAI;MAAE0B,KAAK;MAAExB,IAAI;MAAEyB;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/C7B,cAAc,CAAE8B,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,CAAC7B,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGyB,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,aAAa,CAAC,IAAI,CAAC;MACnBpB,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMsB,QAAQ,GAAG,MAAMlC,aAAa,CAACkD,cAAc,CAACjC,WAAW,CAAC;MAChE,IAAIiB,QAAQ,CAACrB,OAAO,EAAE;QACpBC,UAAU,CAAC,gCAAgC,CAAC;QAC5CI,cAAc,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,IAAI,EAAE,WAAW;UAAEC,QAAQ,EAAE;QAAK,CAAC,CAAC;QAChFN,eAAe,CAAC,KAAK,CAAC;QACtBiB,eAAe,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACLrB,QAAQ,CAACsB,QAAQ,CAACiB,OAAO,IAAI,2BAA2B,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRoB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFpB,aAAa,CAAC,IAAI,CAAC;MACnBpB,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMsB,QAAQ,GAAG,MAAMlC,aAAa,CAACqD,cAAc,CAAC5B,YAAY,CAAC6B,GAAG,EAAE7B,YAAY,CAAC;MACnF,IAAIS,QAAQ,CAACrB,OAAO,EAAE;QACpBC,UAAU,CAAC,gCAAgC,CAAC;QAC5CU,gBAAgB,CAAC,KAAK,CAAC;QACvBE,eAAe,CAAC,IAAI,CAAC;QACrBO,eAAe,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACLrB,QAAQ,CAACsB,QAAQ,CAACiB,OAAO,IAAI,2BAA2B,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRoB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMuB,oBAAoB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAIC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACF,MAAMxB,QAAQ,GAAG,MAAMlC,aAAa,CAAC2D,cAAc,CAACH,UAAU,CAAC;QAC/D,IAAItB,QAAQ,CAACrB,OAAO,EAAE;UACpBC,UAAU,CAAC,gCAAgC,CAAC;UAC5CmB,eAAe,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,MAAM;UACLrB,QAAQ,CAACsB,QAAQ,CAACiB,OAAO,IAAI,2BAA2B,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACd0B,OAAO,CAAC1B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDC,QAAQ,CAAC,2BAA2B,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMgD,aAAa,GAAIC,QAAQ,IAAK;IAClCnC,eAAe,CAAC;MAAE,GAAGmC;IAAS,CAAC,CAAC;IAChCrC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIlB,CAAC,IAAK;IAC9Bd,eAAe,CAACc,CAAC,CAACG,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAInC,YAAY,EAAE;MAChBoC,KAAK,CAAC,kBAAkBpC,YAAY,CAACV,IAAI,EAAE,CAAC;MAC5CW,eAAe,CAAC,IAAI,CAAC;MACrBF,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLqC,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EAED,oBACE/D,OAAA;IAAKgE,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCjE,OAAA;MAAKgE,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEjE,OAAA;QAAIgE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGNrE,OAAA;MAAKgE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,IAAI,CAAE;QAAAmD,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5ErE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC,IAAI,CAAE;QAAAuC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAGNrE,OAAA;MAAKgE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CjE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DrE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DrE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DrE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DrE,OAAA,CAACjB,MAAM;QAACuF,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGNrE,OAAA;MAAKgE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCjE,OAAA,CAACf,IAAI,CAACwF,OAAO;QACXtD,IAAI,EAAC,MAAM;QACXuD,WAAW,EAAC,sBAAsB;QAClC/B,KAAK,EAAEtC,MAAO;QACdsE,QAAQ,EAAGjC,CAAC,IAAKpC,SAAS,CAACoC,CAAC,CAACG,MAAM,CAACF,KAAK;MAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNrE,OAAA,CAAChB,KAAK;MAAC4F,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACd,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDjE,OAAA;QAAOgE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BjE,OAAA;UAAAiE,QAAA,gBACEjE,OAAA;YAAAiE,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBrE,OAAA;YAAAiE,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfrE,OAAA;YAAAiE,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRrE,OAAA;QAAAiE,QAAA,EACG7B,kBAAkB,CAAC2C,GAAG,CAAEzC,GAAG,iBAC1BtC,OAAA;UAAAiE,QAAA,gBACEjE,OAAA;YAAAiE,QAAA,EAAK3B,GAAG,CAAC0C;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3BrE,OAAA;YAAAiE,QAAA,eAAIjE,OAAA;cAAMgE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAE3B,GAAG,CAAC2C;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA,CAACjB,MAAM;cAACuF,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAClDjE,OAAA,CAACN,MAAM;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACTrE,OAAA,CAACjB,MAAM;cAACuF,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAChCjE,OAAA,CAACL,OAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GAVE/B,GAAG,CAAC4C,EAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRrE,OAAA,CAACd,KAAK;MAACiG,IAAI,EAAEtE,YAAa;MAACuE,MAAM,EAAEA,CAAA,KAAMtE,eAAe,CAAC,KAAK,CAAE;MAACuE,QAAQ;MAAApB,QAAA,gBACvEjE,OAAA,CAACd,KAAK,CAACoG,MAAM;QAACC,WAAW;QAAAtB,QAAA,eACvBjE,OAAA,CAACd,KAAK,CAACsG,KAAK;UAAAvB,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACfrE,OAAA,CAACd,KAAK,CAACuG,IAAI;QAAAxB,QAAA,eACTjE,OAAA,CAACf,IAAI;UAAAgF,QAAA,gBACHjE,OAAA,CAACf,IAAI,CAACyG,KAAK;YAAC1B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjE,OAAA,CAACf,IAAI,CAAC0G,KAAK;cAAA1B,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtCrE,OAAA,CAACf,IAAI,CAACwF,OAAO;cACXtD,IAAI,EAAC,MAAM;cACXF,IAAI,EAAC,cAAc;cACnByD,WAAW,EAAC,qBAAqB;cACjC/B,KAAK,EAAE5B,WAAW,CAACiE,YAAa;cAChCL,QAAQ,EAAElC;YAAqB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACbrE,OAAA,CAACf,IAAI,CAACyG,KAAK;YAAC1B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BjE,OAAA,CAACf,IAAI,CAAC0G,KAAK;cAAA1B,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BrE,OAAA,CAACf,IAAI,CAAC2G,MAAM;cACV3E,IAAI,EAAC,QAAQ;cACb0B,KAAK,EAAE5B,WAAW,CAACkE,MAAO;cAC1BN,QAAQ,EAAElC,oBAAqB;cAAAwB,QAAA,gBAE/BjE,OAAA;gBAAQ2C,KAAK,EAAC,QAAQ;gBAAAsB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrE,OAAA;gBAAQ2C,KAAK,EAAC,UAAU;gBAAAsB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbrE,OAAA,CAACd,KAAK,CAAC2G,MAAM;QAAA5B,QAAA,gBACXjE,OAAA,CAACjB,MAAM;UAACuF,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAAC,KAAK,CAAE;UAAAmD,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA,CAACjB,MAAM;UAACuF,OAAO,EAAC,SAAS;UAACC,OAAO,EAAExB,iBAAkB;UAAAkB,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRrE,OAAA,CAACd,KAAK;MAACiG,IAAI,EAAE1D,eAAgB;MAAC2D,MAAM,EAAEA,CAAA,KAAM1D,kBAAkB,CAAC,KAAK,CAAE;MAAC2D,QAAQ;MAAApB,QAAA,gBAC7EjE,OAAA,CAACd,KAAK,CAACoG,MAAM;QAACC,WAAW;QAAAtB,QAAA,eACvBjE,OAAA,CAACd,KAAK,CAACsG,KAAK;UAAAvB,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACfrE,OAAA,CAACd,KAAK,CAACuG,IAAI;QAAAxB,QAAA,eACTjE,OAAA,CAACf,IAAI;UAAAgF,QAAA,eACHjE,OAAA,CAACf,IAAI,CAACyG,KAAK;YAAAzB,QAAA,gBACTjE,OAAA,CAACf,IAAI,CAAC0G,KAAK;cAAA1B,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjDrE,OAAA,CAACf,IAAI,CAACwF,OAAO;cAACtD,IAAI,EAAC,MAAM;cAAC2E,MAAM,EAAC,mGAAmG;cAACnB,QAAQ,EAAEf;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbrE,OAAA,CAACd,KAAK,CAAC2G,MAAM;QAAA5B,QAAA,gBACXjE,OAAA,CAACjB,MAAM;UAACuF,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC,KAAK,CAAE;UAAAuC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrE,OAAA,CAACjB,MAAM;UAACuF,OAAO,EAAC,SAAS;UAACC,OAAO,EAAET,gBAAiB;UAAAG,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnE,EAAA,CAvQID,cAAc;AAAA8F,EAAA,GAAd9F,cAAc;AAyQpB,eAAeA,cAAc;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}