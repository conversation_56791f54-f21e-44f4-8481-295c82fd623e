import React from 'react';
import { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { FaBars, FaUser, FaSignOutAlt, FaCog } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import NotificationCenter from '../components/NotificationCenter';

const Navbar = ({ toggleSidebar }) => {
  const navigate = useNavigate();
  const { user, logout, isAuthenticated } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleRegister = () => {
    navigate('/register');
  };

  return (
    <BootstrapNavbar bg="dark" variant="dark" expand="lg" fixed="top" className="shadow-sm">
      <Container fluid>
        {/* Sidebar Toggle Button */}
        <Button
          variant="outline-light"
          onClick={toggleSidebar}
          className="me-3"
          style={{ border: 'none' }}
        >
          <FaBars />
        </Button>

        {/* Brand */}
        <BootstrapNavbar.Brand as={Link} to="/dashboard" className="fw-bold">
          Morya Insurance
        </BootstrapNavbar.Brand>

        {/* Toggle for mobile */}
        <BootstrapNavbar.Toggle aria-controls="basic-navbar-nav" />

        <BootstrapNavbar.Collapse id="basic-navbar-nav">
          {/* Main Navigation Links */}
          <Nav className="me-auto">
            <Nav.Link as={Link} to="/dashboard">Home</Nav.Link>
            <Nav.Link as={Link} to="/about">About</Nav.Link>
            <Nav.Link as={Link} to="/services">Services</Nav.Link>
            <Nav.Link as={Link} to="/contact">Contact</Nav.Link>
          </Nav>

          {/* User Authentication Section */}
          <Nav className="ms-auto">
            {isAuthenticated() ? (
              <>
                {/* Notification Center */}
                <NotificationCenter />

                {/* User Dropdown */}
                <NavDropdown
                  title={
                    <span>
                      <FaUser className="me-1" />
                      {user.firstName.toUpperCase()} ({user.role})
                    </span>
                  }
                  id="user-dropdown"
                  align="end"
                >
                  <NavDropdown.Item as={Link} to="/profile">
                    <FaUser className="me-2" />
                    Profile
                  </NavDropdown.Item>
                  <NavDropdown.Item as={Link} to="/settings">
                    <FaCog className="me-2" />
                    Settings
                  </NavDropdown.Item>
                  <NavDropdown.Divider />
                  <NavDropdown.Item onClick={handleLogout}>
                    <FaSignOutAlt className="me-2" />
                    Logout
                  </NavDropdown.Item>
                </NavDropdown>
              </>
            ) : (
              <>
                <Button
                  variant="outline-light"
                  onClick={handleLogin}
                  className="me-2"
                >
                  Login
                </Button>
                <Button
                  variant="light"
                  onClick={handleRegister}
                >
                  Register
                </Button>
              </>
            )}
          </Nav>
        </BootstrapNavbar.Collapse>
      </Container>
    </BootstrapNavbar>
  );
};

export default Navbar;
