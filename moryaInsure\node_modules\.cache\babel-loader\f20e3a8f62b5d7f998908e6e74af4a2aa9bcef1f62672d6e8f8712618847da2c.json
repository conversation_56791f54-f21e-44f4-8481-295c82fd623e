{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\VerifyOTP.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { FaShieldAlt, FaEnvelope, FaRedo } from 'react-icons/fa';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VerifyOTP = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [loading, setLoading] = useState(false);\n  const [resendLoading, setResendLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds\n  const [canResend, setCanResend] = useState(false);\n\n  // Get user data from registration\n  const userData = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.userData;\n  const userEmail = (userData === null || userData === void 0 ? void 0 : userData.email) || '';\n  const userId = (userData === null || userData === void 0 ? void 0 : userData.userId) || '';\n  useEffect(() => {\n    // Redirect if no user data\n    if (!userData || !userId) {\n      navigate('/register');\n      return;\n    }\n\n    // Start countdown timer\n    const timer = setInterval(() => {\n      setTimeLeft(prevTime => {\n        if (prevTime <= 1) {\n          setCanResend(true);\n          clearInterval(timer);\n          return 0;\n        }\n        return prevTime - 1;\n      });\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [userData, userId, navigate]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const handleOtpChange = (index, value) => {\n    if (value.length > 1) return; // Prevent multiple characters\n\n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError('');\n    setSuccess('');\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      const nextInput = document.getElementById(`otp-${index + 1}`);\n      if (nextInput) nextInput.focus();\n    }\n  };\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      const prevInput = document.getElementById(`otp-${index - 1}`);\n      if (prevInput) prevInput.focus();\n    }\n  };\n  const handlePaste = e => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData('text').slice(0, 6);\n    const newOtp = [...otp];\n    for (let i = 0; i < pastedData.length && i < 6; i++) {\n      if (/^\\d$/.test(pastedData[i])) {\n        newOtp[i] = pastedData[i];\n      }\n    }\n    setOtp(newOtp);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const otpString = otp.join('');\n    if (otpString.length !== 6) {\n      setError('Please enter the complete 6-digit OTP');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const response = await axios.post('/api/auth/verify-otp', {\n        userId: userId,\n        otp: otpString\n      });\n      if (response.data.success) {\n        setSuccess('Email verified successfully! Redirecting to login...');\n\n        // Store token if provided\n        if (response.data.data.token) {\n          localStorage.setItem('token', response.data.data.token);\n        }\n\n        // Redirect to login after 2 seconds\n        setTimeout(() => {\n          navigate('/login', {\n            state: {\n              message: 'Registration completed successfully! Please log in.',\n              email: userEmail\n            }\n          });\n        }, 2000);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('OTP verification error:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to verify OTP. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleResendOTP = async () => {\n    setResendLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const response = await axios.post('/api/auth/resend-otp', {\n        userId: userId\n      });\n      if (response.data.success) {\n        setSuccess('New OTP sent to your email!');\n        setTimeLeft(600); // Reset timer to 10 minutes\n        setCanResend(false);\n        setOtp(['', '', '', '', '', '']); // Clear current OTP\n\n        // Restart timer\n        const timer = setInterval(() => {\n          setTimeLeft(prevTime => {\n            if (prevTime <= 1) {\n              setCanResend(true);\n              clearInterval(timer);\n              return 0;\n            }\n            return prevTime - 1;\n          });\n        }, 1000);\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Resend OTP error:', error);\n      setError(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to resend OTP. Please try again.');\n    } finally {\n      setResendLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"min-vh-100 d-flex align-items-center justify-content-center bg-light\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"w-100 justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 10,\n        md: 8,\n        lg: 6,\n        xl: 5,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-lg border-0\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n              size: 48,\n              className: \"mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: \"Verify Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-0 mt-2\",\n              children: \"Enter the 6-digit code sent to your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-5\",\n            children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-4\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 25\n            }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              className: \"mb-4\",\n              children: success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 27\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                size: 24,\n                className: \"text-primary mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted mb-0\",\n                children: \"We've sent a verification code to:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"fw-bold text-primary\",\n                children: userEmail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center mb-4\",\n                children: otp.map((digit, index) => /*#__PURE__*/_jsxDEV(Form.Control, {\n                  id: `otp-${index}`,\n                  type: \"text\",\n                  value: digit,\n                  onChange: e => handleOtpChange(index, e.target.value),\n                  onKeyDown: e => handleKeyDown(index, e),\n                  onPaste: handlePaste,\n                  className: \"text-center mx-1 otp-input\",\n                  style: {\n                    width: '50px',\n                    height: '50px',\n                    fontSize: '20px',\n                    fontWeight: 'bold'\n                  },\n                  maxLength: 1,\n                  pattern: \"[0-9]\",\n                  inputMode: \"numeric\",\n                  autoComplete: \"off\"\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: timeLeft > 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: [\"Code expires in: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-warning\",\n                    children: formatTime(timeLeft)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-danger fw-bold\",\n                  children: \"Code has expired\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                className: \"w-100 mb-3\",\n                disabled: loading || otp.join('').length !== 6,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    animation: \"border\",\n                    size: \"sm\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), \"Verifying...\"]\n                }, void 0, true) : 'Verify Email'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted mb-2\",\n                  children: \"Didn't receive the code?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  onClick: handleResendOTP,\n                  disabled: !canResend || resendLoading,\n                  className: \"me-2\",\n                  children: resendLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this), \"Sending...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(FaRedo, {\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this), \"Resend Code\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted mb-0\",\n                children: [\"Remember your password?\", /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"text-primary text-decoration-none ms-1\",\n                  children: \"Back to Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .otp-input:focus {\n          border-color: #0d6efd;\n          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(VerifyOTP, \"68F0OA3VOClWp7k4fKj0ZdaCYFs=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = VerifyOTP;\nexport default VerifyOTP;\nvar _c;\n$RefreshReg$(_c, \"VerifyOTP\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "useLocation", "FaShieldAlt", "FaEnvelope", "FaRedo", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VerifyOTP", "_s", "_location$state", "navigate", "location", "otp", "setOtp", "loading", "setLoading", "resendLoading", "setResendLoading", "error", "setError", "success", "setSuccess", "timeLeft", "setTimeLeft", "canResend", "setCanResend", "userData", "state", "userEmail", "email", "userId", "timer", "setInterval", "prevTime", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleOtpChange", "index", "value", "length", "newOtp", "nextInput", "document", "getElementById", "focus", "handleKeyDown", "e", "key", "prevInput", "handlePaste", "preventDefault", "pastedData", "clipboardData", "getData", "slice", "i", "test", "handleSubmit", "otpString", "join", "response", "post", "data", "token", "localStorage", "setItem", "setTimeout", "message", "_error$response", "_error$response$data", "console", "handleResendOTP", "_error$response2", "_error$response2$data", "fluid", "className", "children", "xs", "sm", "md", "lg", "xl", "Header", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "onSubmit", "map", "digit", "Control", "id", "type", "onChange", "target", "onKeyDown", "onPaste", "style", "width", "height", "fontSize", "fontWeight", "max<PERSON><PERSON><PERSON>", "pattern", "inputMode", "autoComplete", "disabled", "animation", "onClick", "to", "jsx", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/VerifyOTP.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { FaShieldAlt, FaEnvelope, FaRedo } from 'react-icons/fa';\nimport axios from 'axios';\n\nconst VerifyOTP = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [loading, setLoading] = useState(false);\n  const [resendLoading, setResendLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds\n  const [canResend, setCanResend] = useState(false);\n\n  // Get user data from registration\n  const userData = location.state?.userData;\n  const userEmail = userData?.email || '';\n  const userId = userData?.userId || '';\n\n  useEffect(() => {\n    // Redirect if no user data\n    if (!userData || !userId) {\n      navigate('/register');\n      return;\n    }\n\n    // Start countdown timer\n    const timer = setInterval(() => {\n      setTimeLeft((prevTime) => {\n        if (prevTime <= 1) {\n          setCanResend(true);\n          clearInterval(timer);\n          return 0;\n        }\n        return prevTime - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [userData, userId, navigate]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleOtpChange = (index, value) => {\n    if (value.length > 1) return; // Prevent multiple characters\n    \n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError('');\n    setSuccess('');\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      const nextInput = document.getElementById(`otp-${index + 1}`);\n      if (nextInput) nextInput.focus();\n    }\n  };\n\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      const prevInput = document.getElementById(`otp-${index - 1}`);\n      if (prevInput) prevInput.focus();\n    }\n  };\n\n  const handlePaste = (e) => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData('text').slice(0, 6);\n    const newOtp = [...otp];\n    \n    for (let i = 0; i < pastedData.length && i < 6; i++) {\n      if (/^\\d$/.test(pastedData[i])) {\n        newOtp[i] = pastedData[i];\n      }\n    }\n    \n    setOtp(newOtp);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const otpString = otp.join('');\n    if (otpString.length !== 6) {\n      setError('Please enter the complete 6-digit OTP');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await axios.post('/api/auth/verify-otp', {\n        userId: userId,\n        otp: otpString\n      });\n\n      if (response.data.success) {\n        setSuccess('Email verified successfully! Redirecting to login...');\n        \n        // Store token if provided\n        if (response.data.data.token) {\n          localStorage.setItem('token', response.data.data.token);\n        }\n\n        // Redirect to login after 2 seconds\n        setTimeout(() => {\n          navigate('/login', { \n            state: { \n              message: 'Registration completed successfully! Please log in.',\n              email: userEmail \n            } \n          });\n        }, 2000);\n      }\n    } catch (error) {\n      console.error('OTP verification error:', error);\n      setError(\n        error.response?.data?.message || \n        'Failed to verify OTP. Please try again.'\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResendOTP = async () => {\n    setResendLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await axios.post('/api/auth/resend-otp', {\n        userId: userId\n      });\n\n      if (response.data.success) {\n        setSuccess('New OTP sent to your email!');\n        setTimeLeft(600); // Reset timer to 10 minutes\n        setCanResend(false);\n        setOtp(['', '', '', '', '', '']); // Clear current OTP\n        \n        // Restart timer\n        const timer = setInterval(() => {\n          setTimeLeft((prevTime) => {\n            if (prevTime <= 1) {\n              setCanResend(true);\n              clearInterval(timer);\n              return 0;\n            }\n            return prevTime - 1;\n          });\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Resend OTP error:', error);\n      setError(\n        error.response?.data?.message || \n        'Failed to resend OTP. Please try again.'\n      );\n    } finally {\n      setResendLoading(false);\n    }\n  };\n\n  return (\n    <Container fluid className=\"min-vh-100 d-flex align-items-center justify-content-center bg-light\">\n      <Row className=\"w-100 justify-content-center\">\n        <Col xs={12} sm={10} md={8} lg={6} xl={5}>\n          <Card className=\"shadow-lg border-0\">\n            <Card.Header className=\"bg-primary text-white text-center py-4\">\n              <FaShieldAlt size={48} className=\"mb-3\" />\n              <h3 className=\"mb-0\">Verify Your Email</h3>\n              <p className=\"mb-0 mt-2\">Enter the 6-digit code sent to your email</p>\n            </Card.Header>\n            \n            <Card.Body className=\"p-5\">\n              {error && <Alert variant=\"danger\" className=\"mb-4\">{error}</Alert>}\n              {success && <Alert variant=\"success\" className=\"mb-4\">{success}</Alert>}\n              \n              <div className=\"text-center mb-4\">\n                <FaEnvelope size={24} className=\"text-primary mb-2\" />\n                <p className=\"text-muted mb-0\">\n                  We've sent a verification code to:\n                </p>\n                <p className=\"fw-bold text-primary\">{userEmail}</p>\n              </div>\n\n              <Form onSubmit={handleSubmit}>\n                <div className=\"d-flex justify-content-center mb-4\">\n                  {otp.map((digit, index) => (\n                    <Form.Control\n                      key={index}\n                      id={`otp-${index}`}\n                      type=\"text\"\n                      value={digit}\n                      onChange={(e) => handleOtpChange(index, e.target.value)}\n                      onKeyDown={(e) => handleKeyDown(index, e)}\n                      onPaste={handlePaste}\n                      className=\"text-center mx-1 otp-input\"\n                      style={{\n                        width: '50px',\n                        height: '50px',\n                        fontSize: '20px',\n                        fontWeight: 'bold'\n                      }}\n                      maxLength={1}\n                      pattern=\"[0-9]\"\n                      inputMode=\"numeric\"\n                      autoComplete=\"off\"\n                    />\n                  ))}\n                </div>\n\n                <div className=\"text-center mb-4\">\n                  {timeLeft > 0 ? (\n                    <p className=\"text-muted\">\n                      Code expires in: <span className=\"fw-bold text-warning\">{formatTime(timeLeft)}</span>\n                    </p>\n                  ) : (\n                    <p className=\"text-danger fw-bold\">Code has expired</p>\n                  )}\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  variant=\"primary\"\n                  size=\"lg\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading || otp.join('').length !== 6}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                      Verifying...\n                    </>\n                  ) : (\n                    'Verify Email'\n                  )}\n                </Button>\n\n                <div className=\"text-center\">\n                  <p className=\"text-muted mb-2\">Didn't receive the code?</p>\n                  <Button\n                    variant=\"outline-primary\"\n                    onClick={handleResendOTP}\n                    disabled={!canResend || resendLoading}\n                    className=\"me-2\"\n                  >\n                    {resendLoading ? (\n                      <>\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        <FaRedo className=\"me-2\" />\n                        Resend Code\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </Form>\n\n              <hr className=\"my-4\" />\n              \n              <div className=\"text-center\">\n                <p className=\"text-muted mb-0\">\n                  Remember your password? \n                  <Link to=\"/login\" className=\"text-primary text-decoration-none ms-1\">\n                    Back to Login\n                  </Link>\n                </p>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <style jsx>{`\n        .otp-input:focus {\n          border-color: #0d6efd;\n          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);\n        }\n      `}</style>\n    </Container>\n  );\n};\n\nexport default VerifyOTP;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACtB,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,GAAG,EAAEC,MAAM,CAAC,GAAG3B,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACxD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMwC,QAAQ,IAAAjB,eAAA,GAAGE,QAAQ,CAACgB,KAAK,cAAAlB,eAAA,uBAAdA,eAAA,CAAgBiB,QAAQ;EACzC,MAAME,SAAS,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,KAAK,KAAI,EAAE;EACvC,MAAMC,MAAM,GAAG,CAAAJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM,KAAI,EAAE;EAErC3C,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACuC,QAAQ,IAAI,CAACI,MAAM,EAAE;MACxBpB,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;;IAEA;IACA,MAAMqB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAAEU,QAAQ,IAAK;QACxB,IAAIA,QAAQ,IAAI,CAAC,EAAE;UACjBR,YAAY,CAAC,IAAI,CAAC;UAClBS,aAAa,CAACH,KAAK,CAAC;UACpB,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ,GAAG,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACL,QAAQ,EAAEI,MAAM,EAAEpB,QAAQ,CAAC,CAAC;EAEhC,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACxC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9B,MAAMC,MAAM,GAAG,CAAC,GAAGnC,GAAG,CAAC;IACvBmC,MAAM,CAACH,KAAK,CAAC,GAAGC,KAAK;IACrBhC,MAAM,CAACkC,MAAM,CAAC;IACd5B,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAIwB,KAAK,IAAID,KAAK,GAAG,CAAC,EAAE;MACtB,MAAMI,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,OAAON,KAAK,GAAG,CAAC,EAAE,CAAC;MAC7D,IAAII,SAAS,EAAEA,SAAS,CAACG,KAAK,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACR,KAAK,EAAES,CAAC,KAAK;IAClC;IACA,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,IAAI,CAAC1C,GAAG,CAACgC,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACrD,MAAMW,SAAS,GAAGN,QAAQ,CAACC,cAAc,CAAC,OAAON,KAAK,GAAG,CAAC,EAAE,CAAC;MAC7D,IAAIW,SAAS,EAAEA,SAAS,CAACJ,KAAK,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMK,WAAW,GAAIH,CAAC,IAAK;IACzBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAGL,CAAC,CAACM,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9D,MAAMd,MAAM,GAAG,CAAC,GAAGnC,GAAG,CAAC;IAEvB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACZ,MAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACnD,IAAI,MAAM,CAACC,IAAI,CAACL,UAAU,CAACI,CAAC,CAAC,CAAC,EAAE;QAC9Bf,MAAM,CAACe,CAAC,CAAC,GAAGJ,UAAU,CAACI,CAAC,CAAC;MAC3B;IACF;IAEAjD,MAAM,CAACkC,MAAM,CAAC;EAChB,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAElB,MAAMQ,SAAS,GAAGrD,GAAG,CAACsD,IAAI,CAAC,EAAE,CAAC;IAC9B,IAAID,SAAS,CAACnB,MAAM,KAAK,CAAC,EAAE;MAC1B3B,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,IAAI,CAAC,sBAAsB,EAAE;QACxDtC,MAAM,EAAEA,MAAM;QACdlB,GAAG,EAAEqD;MACP,CAAC,CAAC;MAEF,IAAIE,QAAQ,CAACE,IAAI,CAACjD,OAAO,EAAE;QACzBC,UAAU,CAAC,sDAAsD,CAAC;;QAElE;QACA,IAAI8C,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC,KAAK,EAAE;UAC5BC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC;QACzD;;QAEA;QACAG,UAAU,CAAC,MAAM;UACf/D,QAAQ,CAAC,QAAQ,EAAE;YACjBiB,KAAK,EAAE;cACL+C,OAAO,EAAE,qDAAqD;cAC9D7C,KAAK,EAAED;YACT;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAyD,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAAC3D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CACN,EAAAwD,eAAA,GAAAzD,KAAK,CAACiD,QAAQ,cAAAQ,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAC7B,yCACF,CAAC;IACH,CAAC,SAAS;MACR3D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC7D,gBAAgB,CAAC,IAAI,CAAC;IACtBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,IAAI,CAAC,sBAAsB,EAAE;QACxDtC,MAAM,EAAEA;MACV,CAAC,CAAC;MAEF,IAAIqC,QAAQ,CAACE,IAAI,CAACjD,OAAO,EAAE;QACzBC,UAAU,CAAC,6BAA6B,CAAC;QACzCE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAClBE,YAAY,CAAC,KAAK,CAAC;QACnBZ,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElC;QACA,MAAMkB,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9BT,WAAW,CAAEU,QAAQ,IAAK;YACxB,IAAIA,QAAQ,IAAI,CAAC,EAAE;cACjBR,YAAY,CAAC,IAAI,CAAC;cAClBS,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,QAAQ,GAAG,CAAC;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOf,KAAK,EAAE;MAAA,IAAA6D,gBAAA,EAAAC,qBAAA;MACdH,OAAO,CAAC3D,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CACN,EAAA4D,gBAAA,GAAA7D,KAAK,CAACiD,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAC7B,yCACF,CAAC;IACH,CAAC,SAAS;MACRzD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,oBACEb,OAAA,CAAChB,SAAS;IAAC6F,KAAK;IAACC,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBAC/F/E,OAAA,CAACf,GAAG;MAAC6F,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3C/E,OAAA,CAACd,GAAG;QAAC8F,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvC/E,OAAA,CAACb,IAAI;UAAC2F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAClC/E,OAAA,CAACb,IAAI,CAACkG,MAAM;YAACP,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAC7D/E,OAAA,CAACL,WAAW;cAAC2F,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C1F,OAAA;cAAI8E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3C1F,OAAA;cAAG8E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAyC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEd1F,OAAA,CAACb,IAAI,CAACwG,IAAI;YAACb,SAAS,EAAC,KAAK;YAAAC,QAAA,GACvBjE,KAAK,iBAAId,OAAA,CAACV,KAAK;cAACsG,OAAO,EAAC,QAAQ;cAACd,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEjE;YAAK;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACjE1E,OAAO,iBAAIhB,OAAA,CAACV,KAAK;cAACsG,OAAO,EAAC,SAAS;cAACd,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAE/D;YAAO;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEvE1F,OAAA;cAAK8E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/E,OAAA,CAACJ,UAAU;gBAAC0F,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAmB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD1F,OAAA;gBAAG8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAE/B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ1F,OAAA;gBAAG8E,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAEvD;cAAS;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAEN1F,OAAA,CAACZ,IAAI;cAACyG,QAAQ,EAAEjC,YAAa;cAAAmB,QAAA,gBAC3B/E,OAAA;gBAAK8E,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChDvE,GAAG,CAACsF,GAAG,CAAC,CAACC,KAAK,EAAEvD,KAAK,kBACpBxC,OAAA,CAACZ,IAAI,CAAC4G,OAAO;kBAEXC,EAAE,EAAE,OAAOzD,KAAK,EAAG;kBACnB0D,IAAI,EAAC,MAAM;kBACXzD,KAAK,EAAEsD,KAAM;kBACbI,QAAQ,EAAGlD,CAAC,IAAKV,eAAe,CAACC,KAAK,EAAES,CAAC,CAACmD,MAAM,CAAC3D,KAAK,CAAE;kBACxD4D,SAAS,EAAGpD,CAAC,IAAKD,aAAa,CAACR,KAAK,EAAES,CAAC,CAAE;kBAC1CqD,OAAO,EAAElD,WAAY;kBACrB0B,SAAS,EAAC,4BAA4B;kBACtCyB,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdC,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE;kBACd,CAAE;kBACFC,SAAS,EAAE,CAAE;kBACbC,OAAO,EAAC,OAAO;kBACfC,SAAS,EAAC,SAAS;kBACnBC,YAAY,EAAC;gBAAK,GAjBbvE,KAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBX,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1F,OAAA;gBAAK8E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9B7D,QAAQ,GAAG,CAAC,gBACXlB,OAAA;kBAAG8E,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,mBACP,eAAA/E,OAAA;oBAAM8E,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAEhD,UAAU,CAACb,QAAQ;kBAAC;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,gBAEJ1F,OAAA;kBAAG8E,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACvD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA,CAACX,MAAM;gBACL6G,IAAI,EAAC,QAAQ;gBACbN,OAAO,EAAC,SAAS;gBACjBN,IAAI,EAAC,IAAI;gBACTR,SAAS,EAAC,YAAY;gBACtBkC,QAAQ,EAAEtG,OAAO,IAAIF,GAAG,CAACsD,IAAI,CAAC,EAAE,CAAC,CAACpB,MAAM,KAAK,CAAE;gBAAAqC,QAAA,EAE9CrE,OAAO,gBACNV,OAAA,CAAAE,SAAA;kBAAA6E,QAAA,gBACE/E,OAAA,CAACT,OAAO;oBAAC0H,SAAS,EAAC,QAAQ;oBAAC3B,IAAI,EAAC,IAAI;oBAACR,SAAS,EAAC;kBAAM;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE3D;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAET1F,OAAA;gBAAK8E,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B/E,OAAA;kBAAG8E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3D1F,OAAA,CAACX,MAAM;kBACLuG,OAAO,EAAC,iBAAiB;kBACzBsB,OAAO,EAAExC,eAAgB;kBACzBsC,QAAQ,EAAE,CAAC5F,SAAS,IAAIR,aAAc;kBACtCkE,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAEfnE,aAAa,gBACZZ,OAAA,CAAAE,SAAA;oBAAA6E,QAAA,gBACE/E,OAAA,CAACT,OAAO;sBAAC0H,SAAS,EAAC,QAAQ;sBAAC3B,IAAI,EAAC,IAAI;sBAACR,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAE3D;kBAAA,eAAE,CAAC,gBAEH1F,OAAA,CAAAE,SAAA;oBAAA6E,QAAA,gBACE/E,OAAA,CAACH,MAAM;sBAACiF,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAE7B;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEP1F,OAAA;cAAI8E,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEvB1F,OAAA;cAAK8E,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B/E,OAAA;gBAAG8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,yBAE7B,eAAA/E,OAAA,CAACR,IAAI;kBAAC2H,EAAE,EAAC,QAAQ;kBAACrC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAErE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1F,OAAA;MAAOoH,GAAG;MAAArC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEhB,CAAC;AAACtF,EAAA,CAnSID,SAAS;EAAA,QACIV,WAAW,EACXC,WAAW;AAAA;AAAA2H,EAAA,GAFxBlH,SAAS;AAqSf,eAAeA,SAAS;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}