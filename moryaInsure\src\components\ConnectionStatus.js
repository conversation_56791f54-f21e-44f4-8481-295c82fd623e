import React from 'react';
import { FaWifi, FaExclamationTriangle } from 'react-icons/fa';
import { useRealtime } from '../contexts/RealtimeContext';

const ConnectionStatus = () => {
  const { isConnected } = useRealtime();

  return (
    <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
      {isConnected ? (
        <>
          <FaWifi className="me-2" />
          Real-time Connected
        </>
      ) : (
        <>
          <FaExclamationTriangle className="me-2" />
          Disconnected
        </>
      )}
    </div>
  );
};

export default ConnectionStatus;
