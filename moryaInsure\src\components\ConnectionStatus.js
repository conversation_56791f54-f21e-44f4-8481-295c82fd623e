import React from 'react';
import { FaWifi, FaExclamationTriangle } from 'react-icons/fa';
import { useRealtime } from '../contexts/RealtimeContext';

const ConnectionStatus = () => {
  const { isConnected } = useRealtime();

  return (
    <div
      className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 1040,
        padding: '8px 12px',
        borderRadius: '20px',
        fontSize: '0.875rem',
        fontWeight: '500',
        backgroundColor: 'var(--card-bg)',
        border: `1px solid ${isConnected ? 'var(--success-text)' : 'var(--danger-text)'}`,
        color: isConnected ? 'var(--success-text)' : 'var(--danger-text)',
        boxShadow: '0 2px 8px var(--card-shadow)',
        transition: 'all 0.3s ease'
      }}
    >
      {isConnected ? (
        <>
          <FaWifi className="me-2" />
          Real-time Connected
        </>
      ) : (
        <>
          <FaExclamationTriangle className="me-2" />
          Disconnected
        </>
      )}
    </div>
  );
};

export default ConnectionStatus;
