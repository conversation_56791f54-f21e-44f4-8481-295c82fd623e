{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  if (error.response) {\n    // Server responded with error status\n    const {\n      status,\n      data\n    } = error.response;\n    if (status === 401) {\n      // Unauthorized - clear token and redirect to login\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject({\n      message: data.message || 'An error occurred',\n      status,\n      errors: data.errors || []\n    });\n  } else if (error.request) {\n    // Network error\n    return Promise.reject({\n      message: 'Network error. Please check your connection.',\n      status: 0\n    });\n  } else {\n    // Other error\n    return Promise.reject({\n      message: error.message || 'An unexpected error occurred',\n      status: 0\n    });\n  }\n});\n\n// Auth API\nexport const authAPI = {\n  login: credentials => api.post('/auth/login', credentials),\n  register: userData => api.post('/auth/register', userData),\n  getProfile: () => api.get('/auth/me'),\n  updateProfile: userData => api.put('/auth/profile', userData),\n  changePassword: passwordData => api.post('/auth/change-password', passwordData),\n  logout: () => api.post('/auth/logout')\n};\n\n// Users API\nexport const usersAPI = {\n  getUsers: params => api.get('/users', {\n    params\n  }),\n  getUser: id => api.get(`/users/${id}`),\n  createUser: userData => api.post('/users', userData),\n  updateUser: (id, userData) => api.put(`/users/${id}`, userData),\n  deleteUser: id => api.delete(`/users/${id}`),\n  toggleUserStatus: id => api.put(`/users/${id}/status`),\n  getUserStats: () => api.get('/users/stats/overview')\n};\n\n// Policies API\nexport const policiesAPI = {\n  getPolicies: params => api.get('/policies', {\n    params\n  }),\n  getPolicy: id => api.get(`/policies/${id}`),\n  createPolicy: policyData => api.post('/policies', policyData),\n  updatePolicy: (id, policyData) => api.put(`/policies/${id}`, policyData),\n  updatePolicyStatus: (id, statusData) => api.put(`/policies/${id}/status`, statusData)\n};\n\n// Categories API\nexport const categoriesAPI = {\n  getCategories: params => api.get('/categories', {\n    params\n  }),\n  createCategory: categoryData => api.post('/categories', categoryData),\n  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),\n  deleteCategory: id => api.delete(`/categories/${id}`)\n};\n\n// Tickets API\nexport const ticketsAPI = {\n  getTickets: params => api.get('/tickets', {\n    params\n  }),\n  getTicket: id => api.get(`/tickets/${id}`),\n  createTicket: ticketData => api.post('/tickets', ticketData),\n  assignTicket: (id, assignData) => api.put(`/tickets/${id}/assign`, assignData),\n  addComment: (id, commentData) => api.post(`/tickets/${id}/comments`, commentData)\n};\n\n// Reports API\nexport const reportsAPI = {\n  getDashboardStats: () => api.get('/reports/dashboard'),\n  getPolicyReports: params => api.get('/reports/policies', {\n    params\n  }),\n  getTicketReports: params => api.get('/reports/tickets', {\n    params\n  })\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => api.get('/health')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "status", "removeItem", "window", "location", "href", "message", "errors", "authAPI", "login", "credentials", "post", "register", "userData", "getProfile", "get", "updateProfile", "put", "changePassword", "passwordData", "logout", "usersAPI", "getUsers", "params", "getUser", "id", "createUser", "updateUser", "deleteUser", "delete", "toggleUserStatus", "getUserStats", "policiesAPI", "getPolicies", "getPolicy", "createPolicy", "policyData", "updatePolicy", "updatePolicyStatus", "statusData", "categoriesAPI", "getCategories", "createCategory", "categoryData", "updateCategory", "deleteCategory", "ticketsAPI", "getTickets", "getTicket", "createTicket", "ticketData", "assignTicket", "assignData", "addComment", "commentData", "reportsAPI", "getDashboardStats", "getPolicyReports", "getTicketReports", "healthAPI", "check"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // Server responded with error status\n      const { status, data } = error.response;\n      \n      if (status === 401) {\n        // Unauthorized - clear token and redirect to login\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n      \n      return Promise.reject({\n        message: data.message || 'An error occurred',\n        status,\n        errors: data.errors || []\n      });\n    } else if (error.request) {\n      // Network error\n      return Promise.reject({\n        message: 'Network error. Please check your connection.',\n        status: 0\n      });\n    } else {\n      // Other error\n      return Promise.reject({\n        message: error.message || 'An unexpected error occurred',\n        status: 0\n      });\n    }\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: (credentials) => api.post('/auth/login', credentials),\n  register: (userData) => api.post('/auth/register', userData),\n  getProfile: () => api.get('/auth/me'),\n  updateProfile: (userData) => api.put('/auth/profile', userData),\n  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),\n  logout: () => api.post('/auth/logout'),\n};\n\n// Users API\nexport const usersAPI = {\n  getUsers: (params) => api.get('/users', { params }),\n  getUser: (id) => api.get(`/users/${id}`),\n  createUser: (userData) => api.post('/users', userData),\n  updateUser: (id, userData) => api.put(`/users/${id}`, userData),\n  deleteUser: (id) => api.delete(`/users/${id}`),\n  toggleUserStatus: (id) => api.put(`/users/${id}/status`),\n  getUserStats: () => api.get('/users/stats/overview'),\n};\n\n// Policies API\nexport const policiesAPI = {\n  getPolicies: (params) => api.get('/policies', { params }),\n  getPolicy: (id) => api.get(`/policies/${id}`),\n  createPolicy: (policyData) => api.post('/policies', policyData),\n  updatePolicy: (id, policyData) => api.put(`/policies/${id}`, policyData),\n  updatePolicyStatus: (id, statusData) => api.put(`/policies/${id}/status`, statusData),\n};\n\n// Categories API\nexport const categoriesAPI = {\n  getCategories: (params) => api.get('/categories', { params }),\n  createCategory: (categoryData) => api.post('/categories', categoryData),\n  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),\n  deleteCategory: (id) => api.delete(`/categories/${id}`),\n};\n\n// Tickets API\nexport const ticketsAPI = {\n  getTickets: (params) => api.get('/tickets', { params }),\n  getTicket: (id) => api.get(`/tickets/${id}`),\n  createTicket: (ticketData) => api.post('/tickets', ticketData),\n  assignTicket: (id, assignData) => api.put(`/tickets/${id}/assign`, assignData),\n  addComment: (id, commentData) => api.post(`/tickets/${id}/comments`, commentData),\n};\n\n// Reports API\nexport const reportsAPI = {\n  getDashboardStats: () => api.get('/reports/dashboard'),\n  getPolicyReports: (params) => api.get('/reports/policies', { params }),\n  getTicketReports: (params) => api.get('/reports/tickets', { params }),\n};\n\n// Health check\nexport const healthAPI = {\n  check: () => api.get('/health'),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EACT,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB;IACA,MAAM;MAAEE,MAAM;MAAED;IAAK,CAAC,GAAGJ,KAAK,CAACG,QAAQ;IAEvC,IAAIE,MAAM,KAAK,GAAG,EAAE;MAClB;MACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;MAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IAEA,OAAOR,OAAO,CAACC,MAAM,CAAC;MACpBQ,OAAO,EAAEN,IAAI,CAACM,OAAO,IAAI,mBAAmB;MAC5CL,MAAM;MACNM,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAI;IACzB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIX,KAAK,CAACP,OAAO,EAAE;IACxB;IACA,OAAOQ,OAAO,CAACC,MAAM,CAAC;MACpBQ,OAAO,EAAE,8CAA8C;MACvDL,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA,OAAOJ,OAAO,CAACC,MAAM,CAAC;MACpBQ,OAAO,EAAEV,KAAK,CAACU,OAAO,IAAI,8BAA8B;MACxDL,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF,CACF,CAAC;;AAED;AACA,OAAO,MAAMO,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAW,IAAK9B,GAAG,CAAC+B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EAC5DE,QAAQ,EAAGC,QAAQ,IAAKjC,GAAG,CAAC+B,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EAC5DC,UAAU,EAAEA,CAAA,KAAMlC,GAAG,CAACmC,GAAG,CAAC,UAAU,CAAC;EACrCC,aAAa,EAAGH,QAAQ,IAAKjC,GAAG,CAACqC,GAAG,CAAC,eAAe,EAAEJ,QAAQ,CAAC;EAC/DK,cAAc,EAAGC,YAAY,IAAKvC,GAAG,CAAC+B,IAAI,CAAC,uBAAuB,EAAEQ,YAAY,CAAC;EACjFC,MAAM,EAAEA,CAAA,KAAMxC,GAAG,CAAC+B,IAAI,CAAC,cAAc;AACvC,CAAC;;AAED;AACA,OAAO,MAAMU,QAAQ,GAAG;EACtBC,QAAQ,EAAGC,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,QAAQ,EAAE;IAAEQ;EAAO,CAAC,CAAC;EACnDC,OAAO,EAAGC,EAAE,IAAK7C,GAAG,CAACmC,GAAG,CAAC,UAAUU,EAAE,EAAE,CAAC;EACxCC,UAAU,EAAGb,QAAQ,IAAKjC,GAAG,CAAC+B,IAAI,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EACtDc,UAAU,EAAEA,CAACF,EAAE,EAAEZ,QAAQ,KAAKjC,GAAG,CAACqC,GAAG,CAAC,UAAUQ,EAAE,EAAE,EAAEZ,QAAQ,CAAC;EAC/De,UAAU,EAAGH,EAAE,IAAK7C,GAAG,CAACiD,MAAM,CAAC,UAAUJ,EAAE,EAAE,CAAC;EAC9CK,gBAAgB,EAAGL,EAAE,IAAK7C,GAAG,CAACqC,GAAG,CAAC,UAAUQ,EAAE,SAAS,CAAC;EACxDM,YAAY,EAAEA,CAAA,KAAMnD,GAAG,CAACmC,GAAG,CAAC,uBAAuB;AACrD,CAAC;;AAED;AACA,OAAO,MAAMiB,WAAW,GAAG;EACzBC,WAAW,EAAGV,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,WAAW,EAAE;IAAEQ;EAAO,CAAC,CAAC;EACzDW,SAAS,EAAGT,EAAE,IAAK7C,GAAG,CAACmC,GAAG,CAAC,aAAaU,EAAE,EAAE,CAAC;EAC7CU,YAAY,EAAGC,UAAU,IAAKxD,GAAG,CAAC+B,IAAI,CAAC,WAAW,EAAEyB,UAAU,CAAC;EAC/DC,YAAY,EAAEA,CAACZ,EAAE,EAAEW,UAAU,KAAKxD,GAAG,CAACqC,GAAG,CAAC,aAAaQ,EAAE,EAAE,EAAEW,UAAU,CAAC;EACxEE,kBAAkB,EAAEA,CAACb,EAAE,EAAEc,UAAU,KAAK3D,GAAG,CAACqC,GAAG,CAAC,aAAaQ,EAAE,SAAS,EAAEc,UAAU;AACtF,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,aAAa,EAAGlB,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,aAAa,EAAE;IAAEQ;EAAO,CAAC,CAAC;EAC7DmB,cAAc,EAAGC,YAAY,IAAK/D,GAAG,CAAC+B,IAAI,CAAC,aAAa,EAAEgC,YAAY,CAAC;EACvEC,cAAc,EAAEA,CAACnB,EAAE,EAAEkB,YAAY,KAAK/D,GAAG,CAACqC,GAAG,CAAC,eAAeQ,EAAE,EAAE,EAAEkB,YAAY,CAAC;EAChFE,cAAc,EAAGpB,EAAE,IAAK7C,GAAG,CAACiD,MAAM,CAAC,eAAeJ,EAAE,EAAE;AACxD,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxBC,UAAU,EAAGxB,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,UAAU,EAAE;IAAEQ;EAAO,CAAC,CAAC;EACvDyB,SAAS,EAAGvB,EAAE,IAAK7C,GAAG,CAACmC,GAAG,CAAC,YAAYU,EAAE,EAAE,CAAC;EAC5CwB,YAAY,EAAGC,UAAU,IAAKtE,GAAG,CAAC+B,IAAI,CAAC,UAAU,EAAEuC,UAAU,CAAC;EAC9DC,YAAY,EAAEA,CAAC1B,EAAE,EAAE2B,UAAU,KAAKxE,GAAG,CAACqC,GAAG,CAAC,YAAYQ,EAAE,SAAS,EAAE2B,UAAU,CAAC;EAC9EC,UAAU,EAAEA,CAAC5B,EAAE,EAAE6B,WAAW,KAAK1E,GAAG,CAAC+B,IAAI,CAAC,YAAYc,EAAE,WAAW,EAAE6B,WAAW;AAClF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,iBAAiB,EAAEA,CAAA,KAAM5E,GAAG,CAACmC,GAAG,CAAC,oBAAoB,CAAC;EACtD0C,gBAAgB,EAAGlC,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,mBAAmB,EAAE;IAAEQ;EAAO,CAAC,CAAC;EACtEmC,gBAAgB,EAAGnC,MAAM,IAAK3C,GAAG,CAACmC,GAAG,CAAC,kBAAkB,EAAE;IAAEQ;EAAO,CAAC;AACtE,CAAC;;AAED;AACA,OAAO,MAAMoC,SAAS,GAAG;EACvBC,KAAK,EAAEA,CAAA,KAAMhF,GAAG,CAACmC,GAAG,CAAC,SAAS;AAChC,CAAC;AAED,eAAenC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}