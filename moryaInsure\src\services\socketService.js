import io from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
  }

  connect(token) {
    if (this.socket && this.isConnected) {
      return this.socket;
    }

    const serverUrl = process.env.REACT_APP_SERVER_URL || 'http://localhost:5002';
    
    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    });

    this.socket.on('connect', () => {
      console.log('✅ Connected to server via WebSocket');
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('❌ Disconnected from server');
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.isConnected = false;
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.listeners.clear();
    }
  }

  // Real-time event listeners
  on(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback);
      
      // Store listener for cleanup
      if (!this.listeners.has(event)) {
        this.listeners.set(event, []);
      }
      this.listeners.get(event).push(callback);
    }
  }

  off(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback);
      
      // Remove from stored listeners
      if (this.listeners.has(event)) {
        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    }
  }

  emit(event, data) {
    if (this.socket && this.isConnected) {
      this.socket.emit(event, data);
    }
  }

  // Join specific rooms for targeted updates
  joinRoom(room) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_room', room);
    }
  }

  leaveRoom(room) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_room', room);
    }
  }

  // Specific real-time event handlers
  onTaskUpdate(callback) {
    this.on('task_updated', callback);
    this.on('task_created', callback);
    this.on('task_deleted', callback);
    this.on('task_assigned', callback);
  }

  onPolicyUpdate(callback) {
    this.on('policy_updated', callback);
    this.on('policy_created', callback);
    this.on('policy_deleted', callback);
  }

  onUserUpdate(callback) {
    this.on('user_updated', callback);
    this.on('user_created', callback);
    this.on('user_deleted', callback);
  }

  onCategoryUpdate(callback) {
    this.on('category_updated', callback);
    this.on('category_created', callback);
    this.on('category_deleted', callback);
  }

  onSubCategoryUpdate(callback) {
    this.on('subcategory_updated', callback);
    this.on('subcategory_created', callback);
    this.on('subcategory_deleted', callback);
  }

  onPolicyHolderUpdate(callback) {
    this.on('policyholder_updated', callback);
    this.on('policyholder_created', callback);
    this.on('policyholder_deleted', callback);
  }

  onTicketUpdate(callback) {
    this.on('ticket_updated', callback);
    this.on('ticket_created', callback);
    this.on('ticket_deleted', callback);
  }

  onClaimUpdate(callback) {
    this.on('claim_updated', callback);
    this.on('claim_created', callback);
    this.on('claim_deleted', callback);
  }

  onNotification(callback) {
    this.on('notification', callback);
  }

  onSystemUpdate(callback) {
    this.on('system_update', callback);
  }

  // Cleanup all listeners
  removeAllListeners() {
    if (this.socket) {
      this.listeners.forEach((callbacks, event) => {
        callbacks.forEach(callback => {
          this.socket.off(event, callback);
        });
      });
      this.listeners.clear();
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      socketId: this.socket?.id || null
    };
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
