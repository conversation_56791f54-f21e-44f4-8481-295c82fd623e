{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\VerifyOTP.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { FaShieldAlt, FaEnvelope, FaRedo } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VerifyOTP = () => {\n  _s();\n  var _location$state;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    verifyOTP,\n    resendOTP\n  } = useAuth();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [loading, setLoading] = useState(false);\n  const [resendLoading, setResendLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds\n  const [canResend, setCanResend] = useState(false);\n\n  // Get user data from registration\n  const userData = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.userData;\n  const userEmail = (userData === null || userData === void 0 ? void 0 : userData.email) || '';\n  const userId = (userData === null || userData === void 0 ? void 0 : userData.userId) || '';\n  useEffect(() => {\n    // Redirect if no user data\n    if (!userData || !userId) {\n      navigate('/register');\n      return;\n    }\n\n    // Start countdown timer\n    const timer = setInterval(() => {\n      setTimeLeft(prevTime => {\n        if (prevTime <= 1) {\n          setCanResend(true);\n          clearInterval(timer);\n          return 0;\n        }\n        return prevTime - 1;\n      });\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [userData, userId, navigate]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const handleOtpChange = (index, value) => {\n    if (value.length > 1) return; // Prevent multiple characters\n\n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError('');\n    setSuccess('');\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      const nextInput = document.getElementById(`otp-${index + 1}`);\n      if (nextInput) nextInput.focus();\n    }\n  };\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      const prevInput = document.getElementById(`otp-${index - 1}`);\n      if (prevInput) prevInput.focus();\n    }\n  };\n  const handlePaste = e => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData('text').slice(0, 6);\n    const newOtp = [...otp];\n    for (let i = 0; i < pastedData.length && i < 6; i++) {\n      if (/^\\d$/.test(pastedData[i])) {\n        newOtp[i] = pastedData[i];\n      }\n    }\n    setOtp(newOtp);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const otpString = otp.join('');\n    if (otpString.length !== 6) {\n      setError('Please enter the complete 6-digit OTP');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const result = await verifyOTP(userId, otpString);\n      if (result.success) {\n        setSuccess('Email verified successfully! Redirecting to login...');\n\n        // Redirect to login after 2 seconds\n        setTimeout(() => {\n          navigate('/login', {\n            state: {\n              message: 'Registration completed successfully! Please log in.',\n              email: userEmail\n            }\n          });\n        }, 2000);\n      } else {\n        setError(result.error || 'Failed to verify OTP. Please try again.');\n      }\n    } catch (error) {\n      console.error('OTP verification error:', error);\n      setError('Failed to verify OTP. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleResendOTP = async () => {\n    setResendLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const result = await resendOTP(userId);\n      if (result.success) {\n        setSuccess('New OTP sent to your email!');\n        setTimeLeft(600); // Reset timer to 10 minutes\n        setCanResend(false);\n        setOtp(['', '', '', '', '', '']); // Clear current OTP\n\n        // Restart timer\n        const timer = setInterval(() => {\n          setTimeLeft(prevTime => {\n            if (prevTime <= 1) {\n              setCanResend(true);\n              clearInterval(timer);\n              return 0;\n            }\n            return prevTime - 1;\n          });\n        }, 1000);\n      } else {\n        setError(result.error || 'Failed to resend OTP. Please try again.');\n      }\n    } catch (error) {\n      console.error('Resend OTP error:', error);\n      setError('Failed to resend OTP. Please try again.');\n    } finally {\n      setResendLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"min-vh-100 d-flex align-items-center justify-content-center bg-light\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"w-100 justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 10,\n        md: 8,\n        lg: 6,\n        xl: 5,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-lg border-0\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n              size: 48,\n              className: \"mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mb-0\",\n              children: \"Verify Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-0 mt-2\",\n              children: \"Enter the 6-digit code sent to your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-5\",\n            children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-4\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 25\n            }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              className: \"mb-4\",\n              children: success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 27\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                size: 24,\n                className: \"text-primary mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted mb-0\",\n                children: \"We've sent a verification code to:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"fw-bold text-primary\",\n                children: userEmail\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-center mb-4\",\n                children: otp.map((digit, index) => /*#__PURE__*/_jsxDEV(Form.Control, {\n                  id: `otp-${index}`,\n                  type: \"text\",\n                  value: digit,\n                  onChange: e => handleOtpChange(index, e.target.value),\n                  onKeyDown: e => handleKeyDown(index, e),\n                  onPaste: handlePaste,\n                  className: \"text-center mx-1 otp-input\",\n                  style: {\n                    width: '50px',\n                    height: '50px',\n                    fontSize: '20px',\n                    fontWeight: 'bold'\n                  },\n                  maxLength: 1,\n                  pattern: \"[0-9]\",\n                  inputMode: \"numeric\",\n                  autoComplete: \"off\"\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: timeLeft > 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: [\"Code expires in: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-warning\",\n                    children: formatTime(timeLeft)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-danger fw-bold\",\n                  children: \"Code has expired\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                className: \"w-100 mb-3\",\n                disabled: loading || otp.join('').length !== 6,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    animation: \"border\",\n                    size: \"sm\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), \"Verifying...\"]\n                }, void 0, true) : 'Verify Email'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted mb-2\",\n                  children: \"Didn't receive the code?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  onClick: handleResendOTP,\n                  disabled: !canResend || resendLoading,\n                  className: \"me-2\",\n                  children: resendLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this), \"Sending...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(FaRedo, {\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this), \"Resend Code\"]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"my-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted mb-0\",\n                children: [\"Remember your password?\", /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"text-primary text-decoration-none ms-1\",\n                  children: \"Back to Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .otp-input:focus {\n          border-color: #0d6efd;\n          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(VerifyOTP, \"4/9hoH29t48gmRILwY4FpxgnNnE=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = VerifyOTP;\nexport default VerifyOTP;\nvar _c;\n$RefreshReg$(_c, \"VerifyOTP\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "useLocation", "FaShieldAlt", "FaEnvelope", "FaRedo", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VerifyOTP", "_s", "_location$state", "navigate", "location", "verifyOTP", "resendOTP", "otp", "setOtp", "loading", "setLoading", "resendLoading", "setResendLoading", "error", "setError", "success", "setSuccess", "timeLeft", "setTimeLeft", "canResend", "setCanResend", "userData", "state", "userEmail", "email", "userId", "timer", "setInterval", "prevTime", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleOtpChange", "index", "value", "length", "newOtp", "nextInput", "document", "getElementById", "focus", "handleKeyDown", "e", "key", "prevInput", "handlePaste", "preventDefault", "pastedData", "clipboardData", "getData", "slice", "i", "test", "handleSubmit", "otpString", "join", "result", "setTimeout", "message", "console", "handleResendOTP", "fluid", "className", "children", "xs", "sm", "md", "lg", "xl", "Header", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "onSubmit", "map", "digit", "Control", "id", "type", "onChange", "target", "onKeyDown", "onPaste", "style", "width", "height", "fontSize", "fontWeight", "max<PERSON><PERSON><PERSON>", "pattern", "inputMode", "autoComplete", "disabled", "animation", "onClick", "to", "jsx", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/VerifyOTP.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Contain<PERSON>, <PERSON>, Col, Card, <PERSON>, Button, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { FaShieldAlt, FaEnvelope, FaRedo } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst VerifyOTP = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { verifyOTP, resendOTP } = useAuth();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [loading, setLoading] = useState(false);\n  const [resendLoading, setResendLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds\n  const [canResend, setCanResend] = useState(false);\n\n  // Get user data from registration\n  const userData = location.state?.userData;\n  const userEmail = userData?.email || '';\n  const userId = userData?.userId || '';\n\n  useEffect(() => {\n    // Redirect if no user data\n    if (!userData || !userId) {\n      navigate('/register');\n      return;\n    }\n\n    // Start countdown timer\n    const timer = setInterval(() => {\n      setTimeLeft((prevTime) => {\n        if (prevTime <= 1) {\n          setCanResend(true);\n          clearInterval(timer);\n          return 0;\n        }\n        return prevTime - 1;\n      });\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [userData, userId, navigate]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleOtpChange = (index, value) => {\n    if (value.length > 1) return; // Prevent multiple characters\n    \n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError('');\n    setSuccess('');\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      const nextInput = document.getElementById(`otp-${index + 1}`);\n      if (nextInput) nextInput.focus();\n    }\n  };\n\n  const handleKeyDown = (index, e) => {\n    // Handle backspace\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      const prevInput = document.getElementById(`otp-${index - 1}`);\n      if (prevInput) prevInput.focus();\n    }\n  };\n\n  const handlePaste = (e) => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData('text').slice(0, 6);\n    const newOtp = [...otp];\n    \n    for (let i = 0; i < pastedData.length && i < 6; i++) {\n      if (/^\\d$/.test(pastedData[i])) {\n        newOtp[i] = pastedData[i];\n      }\n    }\n    \n    setOtp(newOtp);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const otpString = otp.join('');\n    if (otpString.length !== 6) {\n      setError('Please enter the complete 6-digit OTP');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const result = await verifyOTP(userId, otpString);\n\n      if (result.success) {\n        setSuccess('Email verified successfully! Redirecting to login...');\n\n        // Redirect to login after 2 seconds\n        setTimeout(() => {\n          navigate('/login', {\n            state: {\n              message: 'Registration completed successfully! Please log in.',\n              email: userEmail\n            }\n          });\n        }, 2000);\n      } else {\n        setError(result.error || 'Failed to verify OTP. Please try again.');\n      }\n    } catch (error) {\n      console.error('OTP verification error:', error);\n      setError('Failed to verify OTP. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResendOTP = async () => {\n    setResendLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const result = await resendOTP(userId);\n\n      if (result.success) {\n        setSuccess('New OTP sent to your email!');\n        setTimeLeft(600); // Reset timer to 10 minutes\n        setCanResend(false);\n        setOtp(['', '', '', '', '', '']); // Clear current OTP\n\n        // Restart timer\n        const timer = setInterval(() => {\n          setTimeLeft((prevTime) => {\n            if (prevTime <= 1) {\n              setCanResend(true);\n              clearInterval(timer);\n              return 0;\n            }\n            return prevTime - 1;\n          });\n        }, 1000);\n      } else {\n        setError(result.error || 'Failed to resend OTP. Please try again.');\n      }\n    } catch (error) {\n      console.error('Resend OTP error:', error);\n      setError('Failed to resend OTP. Please try again.');\n    } finally {\n      setResendLoading(false);\n    }\n  };\n\n  return (\n    <Container fluid className=\"min-vh-100 d-flex align-items-center justify-content-center bg-light\">\n      <Row className=\"w-100 justify-content-center\">\n        <Col xs={12} sm={10} md={8} lg={6} xl={5}>\n          <Card className=\"shadow-lg border-0\">\n            <Card.Header className=\"bg-primary text-white text-center py-4\">\n              <FaShieldAlt size={48} className=\"mb-3\" />\n              <h3 className=\"mb-0\">Verify Your Email</h3>\n              <p className=\"mb-0 mt-2\">Enter the 6-digit code sent to your email</p>\n            </Card.Header>\n            \n            <Card.Body className=\"p-5\">\n              {error && <Alert variant=\"danger\" className=\"mb-4\">{error}</Alert>}\n              {success && <Alert variant=\"success\" className=\"mb-4\">{success}</Alert>}\n              \n              <div className=\"text-center mb-4\">\n                <FaEnvelope size={24} className=\"text-primary mb-2\" />\n                <p className=\"text-muted mb-0\">\n                  We've sent a verification code to:\n                </p>\n                <p className=\"fw-bold text-primary\">{userEmail}</p>\n              </div>\n\n              <Form onSubmit={handleSubmit}>\n                <div className=\"d-flex justify-content-center mb-4\">\n                  {otp.map((digit, index) => (\n                    <Form.Control\n                      key={index}\n                      id={`otp-${index}`}\n                      type=\"text\"\n                      value={digit}\n                      onChange={(e) => handleOtpChange(index, e.target.value)}\n                      onKeyDown={(e) => handleKeyDown(index, e)}\n                      onPaste={handlePaste}\n                      className=\"text-center mx-1 otp-input\"\n                      style={{\n                        width: '50px',\n                        height: '50px',\n                        fontSize: '20px',\n                        fontWeight: 'bold'\n                      }}\n                      maxLength={1}\n                      pattern=\"[0-9]\"\n                      inputMode=\"numeric\"\n                      autoComplete=\"off\"\n                    />\n                  ))}\n                </div>\n\n                <div className=\"text-center mb-4\">\n                  {timeLeft > 0 ? (\n                    <p className=\"text-muted\">\n                      Code expires in: <span className=\"fw-bold text-warning\">{formatTime(timeLeft)}</span>\n                    </p>\n                  ) : (\n                    <p className=\"text-danger fw-bold\">Code has expired</p>\n                  )}\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  variant=\"primary\"\n                  size=\"lg\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading || otp.join('').length !== 6}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                      Verifying...\n                    </>\n                  ) : (\n                    'Verify Email'\n                  )}\n                </Button>\n\n                <div className=\"text-center\">\n                  <p className=\"text-muted mb-2\">Didn't receive the code?</p>\n                  <Button\n                    variant=\"outline-primary\"\n                    onClick={handleResendOTP}\n                    disabled={!canResend || resendLoading}\n                    className=\"me-2\"\n                  >\n                    {resendLoading ? (\n                      <>\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        <FaRedo className=\"me-2\" />\n                        Resend Code\n                      </>\n                    )}\n                  </Button>\n                </div>\n              </Form>\n\n              <hr className=\"my-4\" />\n              \n              <div className=\"text-center\">\n                <p className=\"text-muted mb-0\">\n                  Remember your password? \n                  <Link to=\"/login\" className=\"text-primary text-decoration-none ms-1\">\n                    Back to Login\n                  </Link>\n                </p>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <style jsx>{`\n        .otp-input:focus {\n          border-color: #0d6efd;\n          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);\n        }\n      `}</style>\n    </Container>\n  );\n};\n\nexport default VerifyOTP;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACtB,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,SAAS;IAAEC;EAAU,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACY,GAAG,EAAEC,MAAM,CAAC,GAAG7B,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACxD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM0C,QAAQ,IAAAnB,eAAA,GAAGE,QAAQ,CAACkB,KAAK,cAAApB,eAAA,uBAAdA,eAAA,CAAgBmB,QAAQ;EACzC,MAAME,SAAS,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,KAAK,KAAI,EAAE;EACvC,MAAMC,MAAM,GAAG,CAAAJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM,KAAI,EAAE;EAErC7C,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACyC,QAAQ,IAAI,CAACI,MAAM,EAAE;MACxBtB,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;;IAEA;IACA,MAAMuB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAAEU,QAAQ,IAAK;QACxB,IAAIA,QAAQ,IAAI,CAAC,EAAE;UACjBR,YAAY,CAAC,IAAI,CAAC;UAClBS,aAAa,CAACH,KAAK,CAAC;UACpB,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ,GAAG,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACL,QAAQ,EAAEI,MAAM,EAAEtB,QAAQ,CAAC,CAAC;EAEhC,MAAM2B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGC,OAAO,IAAIG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACxC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9B,MAAMC,MAAM,GAAG,CAAC,GAAGnC,GAAG,CAAC;IACvBmC,MAAM,CAACH,KAAK,CAAC,GAAGC,KAAK;IACrBhC,MAAM,CAACkC,MAAM,CAAC;IACd5B,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAIwB,KAAK,IAAID,KAAK,GAAG,CAAC,EAAE;MACtB,MAAMI,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,OAAON,KAAK,GAAG,CAAC,EAAE,CAAC;MAC7D,IAAII,SAAS,EAAEA,SAAS,CAACG,KAAK,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACR,KAAK,EAAES,CAAC,KAAK;IAClC;IACA,IAAIA,CAAC,CAACC,GAAG,KAAK,WAAW,IAAI,CAAC1C,GAAG,CAACgC,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACrD,MAAMW,SAAS,GAAGN,QAAQ,CAACC,cAAc,CAAC,OAAON,KAAK,GAAG,CAAC,EAAE,CAAC;MAC7D,IAAIW,SAAS,EAAEA,SAAS,CAACJ,KAAK,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMK,WAAW,GAAIH,CAAC,IAAK;IACzBA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAGL,CAAC,CAACM,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9D,MAAMd,MAAM,GAAG,CAAC,GAAGnC,GAAG,CAAC;IAEvB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACZ,MAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACnD,IAAI,MAAM,CAACC,IAAI,CAACL,UAAU,CAACI,CAAC,CAAC,CAAC,EAAE;QAC9Bf,MAAM,CAACe,CAAC,CAAC,GAAGJ,UAAU,CAACI,CAAC,CAAC;MAC3B;IACF;IAEAjD,MAAM,CAACkC,MAAM,CAAC;EAChB,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAElB,MAAMQ,SAAS,GAAGrD,GAAG,CAACsD,IAAI,CAAC,EAAE,CAAC;IAC9B,IAAID,SAAS,CAACnB,MAAM,KAAK,CAAC,EAAE;MAC1B3B,QAAQ,CAAC,uCAAuC,CAAC;MACjD;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM8C,MAAM,GAAG,MAAMzD,SAAS,CAACoB,MAAM,EAAEmC,SAAS,CAAC;MAEjD,IAAIE,MAAM,CAAC/C,OAAO,EAAE;QAClBC,UAAU,CAAC,sDAAsD,CAAC;;QAElE;QACA+C,UAAU,CAAC,MAAM;UACf5D,QAAQ,CAAC,QAAQ,EAAE;YACjBmB,KAAK,EAAE;cACL0C,OAAO,EAAE,qDAAqD;cAC9DxC,KAAK,EAAED;YACT;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLT,QAAQ,CAACgD,MAAM,CAACjD,KAAK,IAAI,yCAAyC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdoD,OAAO,CAACpD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,yCAAyC,CAAC;IACrD,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCtD,gBAAgB,CAAC,IAAI,CAAC;IACtBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAM8C,MAAM,GAAG,MAAMxD,SAAS,CAACmB,MAAM,CAAC;MAEtC,IAAIqC,MAAM,CAAC/C,OAAO,EAAE;QAClBC,UAAU,CAAC,6BAA6B,CAAC;QACzCE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QAClBE,YAAY,CAAC,KAAK,CAAC;QACnBZ,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElC;QACA,MAAMkB,KAAK,GAAGC,WAAW,CAAC,MAAM;UAC9BT,WAAW,CAAEU,QAAQ,IAAK;YACxB,IAAIA,QAAQ,IAAI,CAAC,EAAE;cACjBR,YAAY,CAAC,IAAI,CAAC;cAClBS,aAAa,CAACH,KAAK,CAAC;cACpB,OAAO,CAAC;YACV;YACA,OAAOE,QAAQ,GAAG,CAAC;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLd,QAAQ,CAACgD,MAAM,CAACjD,KAAK,IAAI,yCAAyC,CAAC;MACrE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdoD,OAAO,CAACpD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCC,QAAQ,CAAC,yCAAyC,CAAC;IACrD,CAAC,SAAS;MACRF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,oBACEf,OAAA,CAAChB,SAAS;IAACsF,KAAK;IAACC,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBAC/FxE,OAAA,CAACf,GAAG;MAACsF,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CxE,OAAA,CAACd,GAAG;QAACuF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvCxE,OAAA,CAACb,IAAI;UAACoF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAClCxE,OAAA,CAACb,IAAI,CAAC2F,MAAM;YAACP,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAC7DxE,OAAA,CAACL,WAAW;cAACoF,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CnF,OAAA;cAAIuE,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CnF,OAAA;cAAGuE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAyC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEdnF,OAAA,CAACb,IAAI,CAACiG,IAAI;YAACb,SAAS,EAAC,KAAK;YAAAC,QAAA,GACvBxD,KAAK,iBAAIhB,OAAA,CAACV,KAAK;cAAC+F,OAAO,EAAC,QAAQ;cAACd,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAExD;YAAK;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACjEjE,OAAO,iBAAIlB,OAAA,CAACV,KAAK;cAAC+F,OAAO,EAAC,SAAS;cAACd,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEtD;YAAO;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEvEnF,OAAA;cAAKuE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BxE,OAAA,CAACJ,UAAU;gBAACmF,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAmB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDnF,OAAA;gBAAGuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAE/B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnF,OAAA;gBAAGuE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAE9C;cAAS;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAENnF,OAAA,CAACZ,IAAI;cAACkG,QAAQ,EAAExB,YAAa;cAAAU,QAAA,gBAC3BxE,OAAA;gBAAKuE,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChD9D,GAAG,CAAC6E,GAAG,CAAC,CAACC,KAAK,EAAE9C,KAAK,kBACpB1C,OAAA,CAACZ,IAAI,CAACqG,OAAO;kBAEXC,EAAE,EAAE,OAAOhD,KAAK,EAAG;kBACnBiD,IAAI,EAAC,MAAM;kBACXhD,KAAK,EAAE6C,KAAM;kBACbI,QAAQ,EAAGzC,CAAC,IAAKV,eAAe,CAACC,KAAK,EAAES,CAAC,CAAC0C,MAAM,CAAClD,KAAK,CAAE;kBACxDmD,SAAS,EAAG3C,CAAC,IAAKD,aAAa,CAACR,KAAK,EAAES,CAAC,CAAE;kBAC1C4C,OAAO,EAAEzC,WAAY;kBACrBiB,SAAS,EAAC,4BAA4B;kBACtCyB,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdC,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE;kBACd,CAAE;kBACFC,SAAS,EAAE,CAAE;kBACbC,OAAO,EAAC,OAAO;kBACfC,SAAS,EAAC,SAAS;kBACnBC,YAAY,EAAC;gBAAK,GAjBb9D,KAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBX,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnF,OAAA;gBAAKuE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BpD,QAAQ,GAAG,CAAC,gBACXpB,OAAA;kBAAGuE,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,mBACP,eAAAxE,OAAA;oBAAMuE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAEvC,UAAU,CAACb,QAAQ;kBAAC;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,gBAEJnF,OAAA;kBAAGuE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACvD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENnF,OAAA,CAACX,MAAM;gBACLsG,IAAI,EAAC,QAAQ;gBACbN,OAAO,EAAC,SAAS;gBACjBN,IAAI,EAAC,IAAI;gBACTR,SAAS,EAAC,YAAY;gBACtBkC,QAAQ,EAAE7F,OAAO,IAAIF,GAAG,CAACsD,IAAI,CAAC,EAAE,CAAC,CAACpB,MAAM,KAAK,CAAE;gBAAA4B,QAAA,EAE9C5D,OAAO,gBACNZ,OAAA,CAAAE,SAAA;kBAAAsE,QAAA,gBACExE,OAAA,CAACT,OAAO;oBAACmH,SAAS,EAAC,QAAQ;oBAAC3B,IAAI,EAAC,IAAI;oBAACR,SAAS,EAAC;kBAAM;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE3D;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETnF,OAAA;gBAAKuE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxE,OAAA;kBAAGuE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC3DnF,OAAA,CAACX,MAAM;kBACLgG,OAAO,EAAC,iBAAiB;kBACzBsB,OAAO,EAAEtC,eAAgB;kBACzBoC,QAAQ,EAAE,CAACnF,SAAS,IAAIR,aAAc;kBACtCyD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAEf1D,aAAa,gBACZd,OAAA,CAAAE,SAAA;oBAAAsE,QAAA,gBACExE,OAAA,CAACT,OAAO;sBAACmH,SAAS,EAAC,QAAQ;sBAAC3B,IAAI,EAAC,IAAI;sBAACR,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,cAE3D;kBAAA,eAAE,CAAC,gBAEHnF,OAAA,CAAAE,SAAA;oBAAAsE,QAAA,gBACExE,OAAA,CAACH,MAAM;sBAAC0E,SAAS,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAE7B;kBAAA,eAAE;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEPnF,OAAA;cAAIuE,SAAS,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEvBnF,OAAA;cAAKuE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BxE,OAAA;gBAAGuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,yBAE7B,eAAAxE,OAAA,CAACR,IAAI;kBAACoH,EAAE,EAAC,QAAQ;kBAACrC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAErE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnF,OAAA;MAAO6G,GAAG;MAAArC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEhB,CAAC;AAAC/E,EAAA,CAxRID,SAAS;EAAA,QACIV,WAAW,EACXC,WAAW,EACKI,OAAO;AAAA;AAAAgH,EAAA,GAHpC3G,SAAS;AA0Rf,eAAeA,SAAS;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}