{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaUser, FaEnvelope, FaLock, FaEye, FaEyeSlash, FaUserTag } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    register\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'customer'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [validated, setValidated] = useState(false);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n    setSuccess('');\n  };\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n    if (!validateForm()) {\n      setValidated(true);\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const result = await register(formData);\n      if (result.success) {\n        setSuccess('Registration successful! Redirecting to login...');\n        setTimeout(() => {\n          navigate('/login');\n        }, 2000);\n      } else {\n        setError(result.error || 'Registration failed. Please try again.');\n      }\n    } catch (err) {\n      setError('Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center bg-light py-5\",\n    style: {\n      paddingTop: '56px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow-lg border-0\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-primary\",\n                  children: \"Create Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"Join Morya Insurance today\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-3\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"success\",\n                className: \"mb-3\",\n                children: success\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                noValidate: true,\n                validated: validated,\n                onSubmit: handleSubmit,\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"First Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 110,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"position-relative\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                          className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 112,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"text\",\n                          name: \"firstName\",\n                          value: formData.firstName,\n                          onChange: handleChange,\n                          required: true,\n                          placeholder: \"First name\",\n                          className: \"ps-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 113,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: \"Please provide your first name.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 122,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 111,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Last Name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 130,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"position-relative\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                          className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 132,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"text\",\n                          name: \"lastName\",\n                          value: formData.lastName,\n                          onChange: handleChange,\n                          required: true,\n                          placeholder: \"Last name\",\n                          className: \"ps-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 133,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: \"Please provide your last name.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 142,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 131,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"position-relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                      className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 153,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      name: \"email\",\n                      value: formData.email,\n                      onChange: handleChange,\n                      required: true,\n                      placeholder: \"Enter your email\",\n                      className: \"ps-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: \"Please provide a valid email.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Role\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"position-relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaUserTag, {\n                      className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      name: \"role\",\n                      value: formData.role,\n                      onChange: handleChange,\n                      required: true,\n                      className: \"ps-5\",\n                      children: /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"customer\",\n                        children: \"Customer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 180,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: \"Please select a role.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 192,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"position-relative\",\n                        children: [/*#__PURE__*/_jsxDEV(FaLock, {\n                          className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 194,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: showPassword ? 'text' : 'password',\n                          name: \"password\",\n                          value: formData.password,\n                          onChange: handleChange,\n                          required: true,\n                          placeholder: \"Password\",\n                          className: \"ps-5 pe-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 195,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"link\",\n                          className: \"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\",\n                          onClick: () => setShowPassword(!showPassword),\n                          type: \"button\",\n                          children: showPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 210,\n                            columnNumber: 45\n                          }, this) : /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 210,\n                            columnNumber: 62\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: \"Please provide a password.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 212,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 193,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                        children: \"Confirm Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"position-relative\",\n                        children: [/*#__PURE__*/_jsxDEV(FaLock, {\n                          className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 222,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: showConfirmPassword ? 'text' : 'password',\n                          name: \"confirmPassword\",\n                          value: formData.confirmPassword,\n                          onChange: handleChange,\n                          required: true,\n                          placeholder: \"Confirm password\",\n                          className: \"ps-5 pe-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 223,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"link\",\n                          className: \"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\",\n                          onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                          type: \"button\",\n                          children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 52\n                          }, this) : /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 69\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 232,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: \"Please confirm your password.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 240,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"primary\",\n                  className: \"w-100 mb-3\",\n                  disabled: loading,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this), \"Creating Account...\"]\n                  }, void 0, true) : 'Create Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    className: \"text-primary text-decoration-none\",\n                    children: \"Sign in here\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"lm9dypDvAqXENTzi4B+YoXxLnHA=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "FaUser", "FaEnvelope", "FaLock", "FaEye", "FaEyeSlash", "FaUserTag", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "navigate", "register", "formData", "setFormData", "firstName", "lastName", "email", "password", "confirmPassword", "role", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "validated", "setValidated", "handleChange", "e", "target", "name", "value", "validateForm", "length", "handleSubmit", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "result", "setTimeout", "err", "className", "style", "paddingTop", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "noValidate", "onSubmit", "Group", "Label", "Control", "type", "onChange", "required", "placeholder", "<PERSON><PERSON><PERSON>", "Select", "onClick", "disabled", "animation", "size", "to", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaU<PERSON>, FaEnvelope, FaLock, <PERSON>aEye, FaEyeSlash, FaUserTag } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst Register = () => {\n  const navigate = useNavigate();\n  const { register } = useAuth();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'customer'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [validated, setValidated] = useState(false);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n    setSuccess('');\n  };\n\n  const validateForm = () => {\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    \n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n\n    if (!validateForm()) {\n      setValidated(true);\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const result = await register(formData);\n\n      if (result.success) {\n        setSuccess('Registration successful! Redirecting to login...');\n        setTimeout(() => {\n          navigate('/login');\n        }, 2000);\n      } else {\n        setError(result.error || 'Registration failed. Please try again.');\n      }\n    } catch (err) {\n      setError('Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-vh-100 d-flex align-items-center bg-light py-5\" style={{ paddingTop: '56px' }}>\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col md={8} lg={6}>\n            <Card className=\"shadow-lg border-0\">\n              <Card.Body className=\"p-5\">\n                <div className=\"text-center mb-4\">\n                  <h2 className=\"fw-bold text-primary\">Create Account</h2>\n                  <p className=\"text-muted\">Join Morya Insurance today</p>\n                </div>\n\n                {error && (\n                  <Alert variant=\"danger\" className=\"mb-3\">\n                    {error}\n                  </Alert>\n                )}\n\n                {success && (\n                  <Alert variant=\"success\" className=\"mb-3\">\n                    {success}\n                  </Alert>\n                )}\n\n                <Form noValidate validated={validated} onSubmit={handleSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>First Name</Form.Label>\n                        <div className=\"position-relative\">\n                          <FaUser className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                          <Form.Control\n                            type=\"text\"\n                            name=\"firstName\"\n                            value={formData.firstName}\n                            onChange={handleChange}\n                            required\n                            placeholder=\"First name\"\n                            className=\"ps-5\"\n                          />\n                          <Form.Control.Feedback type=\"invalid\">\n                            Please provide your first name.\n                          </Form.Control.Feedback>\n                        </div>\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Last Name</Form.Label>\n                        <div className=\"position-relative\">\n                          <FaUser className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                          <Form.Control\n                            type=\"text\"\n                            name=\"lastName\"\n                            value={formData.lastName}\n                            onChange={handleChange}\n                            required\n                            placeholder=\"Last name\"\n                            className=\"ps-5\"\n                          />\n                          <Form.Control.Feedback type=\"invalid\">\n                            Please provide your last name.\n                          </Form.Control.Feedback>\n                        </div>\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <div className=\"position-relative\">\n                      <FaEnvelope className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                      <Form.Control\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        required\n                        placeholder=\"Enter your email\"\n                        className=\"ps-5\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        Please provide a valid email.\n                      </Form.Control.Feedback>\n                    </div>\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Role</Form.Label>\n                    <div className=\"position-relative\">\n                      <FaUserTag className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                      <Form.Select\n                        name=\"role\"\n                        value={formData.role}\n                        onChange={handleChange}\n                        required\n                        className=\"ps-5\"\n                      >\n                        <option value=\"customer\">Customer</option>\n                        {/* <option value=\"employee\">Employee</option> */}\n                      </Form.Select>\n                      <Form.Control.Feedback type=\"invalid\">\n                        Please select a role.\n                      </Form.Control.Feedback>\n                    </div>\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Password</Form.Label>\n                        <div className=\"position-relative\">\n                          <FaLock className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                          <Form.Control\n                            type={showPassword ? 'text' : 'password'}\n                            name=\"password\"\n                            value={formData.password}\n                            onChange={handleChange}\n                            required\n                            placeholder=\"Password\"\n                            className=\"ps-5 pe-5\"\n                          />\n                          <Button\n                            variant=\"link\"\n                            className=\"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\"\n                            onClick={() => setShowPassword(!showPassword)}\n                            type=\"button\"\n                          >\n                            {showPassword ? <FaEyeSlash /> : <FaEye />}\n                          </Button>\n                          <Form.Control.Feedback type=\"invalid\">\n                            Please provide a password.\n                          </Form.Control.Feedback>\n                        </div>\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-4\">\n                        <Form.Label>Confirm Password</Form.Label>\n                        <div className=\"position-relative\">\n                          <FaLock className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                          <Form.Control\n                            type={showConfirmPassword ? 'text' : 'password'}\n                            name=\"confirmPassword\"\n                            value={formData.confirmPassword}\n                            onChange={handleChange}\n                            required\n                            placeholder=\"Confirm password\"\n                            className=\"ps-5 pe-5\"\n                          />\n                          <Button\n                            variant=\"link\"\n                            className=\"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\"\n                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                            type=\"button\"\n                          >\n                            {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}\n                          </Button>\n                          <Form.Control.Feedback type=\"invalid\">\n                            Please confirm your password.\n                          </Form.Control.Feedback>\n                        </div>\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Button\n                    type=\"submit\"\n                    variant=\"primary\"\n                    className=\"w-100 mb-3\"\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                        Creating Account...\n                      </>\n                    ) : (\n                      'Create Account'\n                    )}\n                  </Button>\n                </Form>\n\n                <div className=\"text-center\">\n                  <p className=\"mb-0\">\n                    Already have an account?{' '}\n                    <Link to=\"/login\" className=\"text-primary text-decoration-none\">\n                      Sign in here\n                    </Link>\n                  </p>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACzF,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAS,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC;IACvC4B,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM8C,YAAY,GAAIC,CAAC,IAAK;IAC1BpB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACqB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFT,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIzB,QAAQ,CAACK,QAAQ,KAAKL,QAAQ,CAACM,eAAe,EAAE;MAClDS,QAAQ,CAAC,wBAAwB,CAAC;MAClC,OAAO,KAAK;IACd;IACA,IAAIf,QAAQ,CAACK,QAAQ,CAACqB,MAAM,GAAG,CAAC,EAAE;MAChCX,QAAQ,CAAC,6CAA6C,CAAC;MACvD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMY,YAAY,GAAG,MAAON,CAAC,IAAK;IAChCA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,MAAMC,IAAI,GAAGR,CAAC,CAACS,aAAa;IAE5B,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,KAAK,KAAK,EAAE;MAClCV,CAAC,CAACW,eAAe,CAAC,CAAC;MACnBb,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEA,IAAI,CAACM,YAAY,CAAC,CAAC,EAAE;MACnBN,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAN,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMkB,MAAM,GAAG,MAAMlC,QAAQ,CAACC,QAAQ,CAAC;MAEvC,IAAIiC,MAAM,CAACjB,OAAO,EAAE;QAClBC,UAAU,CAAC,kDAAkD,CAAC;QAC9DiB,UAAU,CAAC,MAAM;UACfpC,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLiB,QAAQ,CAACkB,MAAM,CAACnB,KAAK,IAAI,wCAAwC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZpB,QAAQ,CAAC,wCAAwC,CAAC;IACpD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEpB,OAAA;IAAK2C,SAAS,EAAC,oDAAoD;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAO,CAAE;IAAAC,QAAA,eAChG9C,OAAA,CAAClB,SAAS;MAAAgE,QAAA,eACR9C,OAAA,CAACjB,GAAG;QAAC4D,SAAS,EAAC,wBAAwB;QAAAG,QAAA,eACrC9C,OAAA,CAAChB,GAAG;UAAC+D,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAF,QAAA,eAChB9C,OAAA,CAACf,IAAI;YAAC0D,SAAS,EAAC,oBAAoB;YAAAG,QAAA,eAClC9C,OAAA,CAACf,IAAI,CAACgE,IAAI;cAACN,SAAS,EAAC,KAAK;cAAAG,QAAA,gBACxB9C,OAAA;gBAAK2C,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,gBAC/B9C,OAAA;kBAAI2C,SAAS,EAAC,sBAAsB;kBAAAG,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDrD,OAAA;kBAAG2C,SAAS,EAAC,YAAY;kBAAAG,QAAA,EAAC;gBAA0B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EAELhC,KAAK,iBACJrB,OAAA,CAACZ,KAAK;gBAACkE,OAAO,EAAC,QAAQ;gBAACX,SAAS,EAAC,MAAM;gBAAAG,QAAA,EACrCzB;cAAK;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,EAEA9B,OAAO,iBACNvB,OAAA,CAACZ,KAAK;gBAACkE,OAAO,EAAC,SAAS;gBAACX,SAAS,EAAC,MAAM;gBAAAG,QAAA,EACtCvB;cAAO;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR,eAEDrD,OAAA,CAACd,IAAI;gBAACqE,UAAU;gBAAC9B,SAAS,EAAEA,SAAU;gBAAC+B,QAAQ,EAAEtB,YAAa;gBAAAY,QAAA,gBAC5D9C,OAAA,CAACjB,GAAG;kBAAA+D,QAAA,gBACF9C,OAAA,CAAChB,GAAG;oBAAC+D,EAAE,EAAE,CAAE;oBAAAD,QAAA,eACT9C,OAAA,CAACd,IAAI,CAACuE,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;wBAAAZ,QAAA,EAAC;sBAAU;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACnCrD,OAAA;wBAAK2C,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,gBAChC9C,OAAA,CAACR,MAAM;0BAACmD,SAAS,EAAC;wBAAqE;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1FrD,OAAA,CAACd,IAAI,CAACyE,OAAO;0BACXC,IAAI,EAAC,MAAM;0BACX9B,IAAI,EAAC,WAAW;0BAChBC,KAAK,EAAExB,QAAQ,CAACE,SAAU;0BAC1BoD,QAAQ,EAAElC,YAAa;0BACvBmC,QAAQ;0BACRC,WAAW,EAAC,YAAY;0BACxBpB,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACFrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;0BAACJ,IAAI,EAAC,SAAS;0BAAAd,QAAA,EAAC;wBAEtC;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNrD,OAAA,CAAChB,GAAG;oBAAC+D,EAAE,EAAE,CAAE;oBAAAD,QAAA,eACT9C,OAAA,CAACd,IAAI,CAACuE,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;wBAAAZ,QAAA,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAClCrD,OAAA;wBAAK2C,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,gBAChC9C,OAAA,CAACR,MAAM;0BAACmD,SAAS,EAAC;wBAAqE;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1FrD,OAAA,CAACd,IAAI,CAACyE,OAAO;0BACXC,IAAI,EAAC,MAAM;0BACX9B,IAAI,EAAC,UAAU;0BACfC,KAAK,EAAExB,QAAQ,CAACG,QAAS;0BACzBmD,QAAQ,EAAElC,YAAa;0BACvBmC,QAAQ;0BACRC,WAAW,EAAC,WAAW;0BACvBpB,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACFrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;0BAACJ,IAAI,EAAC,SAAS;0BAAAd,QAAA,EAAC;wBAEtC;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA,CAACd,IAAI,CAACuE,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;oBAAAZ,QAAA,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtCrD,OAAA;oBAAK2C,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,gBAChC9C,OAAA,CAACP,UAAU;sBAACkD,SAAS,EAAC;oBAAqE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9FrD,OAAA,CAACd,IAAI,CAACyE,OAAO;sBACXC,IAAI,EAAC,OAAO;sBACZ9B,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAExB,QAAQ,CAACI,KAAM;sBACtBkD,QAAQ,EAAElC,YAAa;sBACvBmC,QAAQ;sBACRC,WAAW,EAAC,kBAAkB;sBAC9BpB,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;sBAACJ,IAAI,EAAC,SAAS;sBAAAd,QAAA,EAAC;oBAEtC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAuB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbrD,OAAA,CAACd,IAAI,CAACuE,KAAK;kBAACd,SAAS,EAAC,MAAM;kBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;oBAAAZ,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7BrD,OAAA;oBAAK2C,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,gBAChC9C,OAAA,CAACH,SAAS;sBAAC8C,SAAS,EAAC;oBAAqE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7FrD,OAAA,CAACd,IAAI,CAAC+E,MAAM;sBACVnC,IAAI,EAAC,MAAM;sBACXC,KAAK,EAAExB,QAAQ,CAACO,IAAK;sBACrB+C,QAAQ,EAAElC,YAAa;sBACvBmC,QAAQ;sBACRnB,SAAS,EAAC,MAAM;sBAAAG,QAAA,eAEhB9C,OAAA;wBAAQ+B,KAAK,EAAC,UAAU;wBAAAe,QAAA,EAAC;sBAAQ;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/B,CAAC,eACdrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;sBAACJ,IAAI,EAAC,SAAS;sBAAAd,QAAA,EAAC;oBAEtC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAuB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbrD,OAAA,CAACjB,GAAG;kBAAA+D,QAAA,gBACF9C,OAAA,CAAChB,GAAG;oBAAC+D,EAAE,EAAE,CAAE;oBAAAD,QAAA,eACT9C,OAAA,CAACd,IAAI,CAACuE,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;wBAAAZ,QAAA,EAAC;sBAAQ;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjCrD,OAAA;wBAAK2C,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,gBAChC9C,OAAA,CAACN,MAAM;0BAACiD,SAAS,EAAC;wBAAqE;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1FrD,OAAA,CAACd,IAAI,CAACyE,OAAO;0BACXC,IAAI,EAAE7C,YAAY,GAAG,MAAM,GAAG,UAAW;0BACzCe,IAAI,EAAC,UAAU;0BACfC,KAAK,EAAExB,QAAQ,CAACK,QAAS;0BACzBiD,QAAQ,EAAElC,YAAa;0BACvBmC,QAAQ;0BACRC,WAAW,EAAC,UAAU;0BACtBpB,SAAS,EAAC;wBAAW;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACFrD,OAAA,CAACb,MAAM;0BACLmE,OAAO,EAAC,MAAM;0BACdX,SAAS,EAAC,qEAAqE;0BAC/EuB,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC,CAACD,YAAY,CAAE;0BAC9C6C,IAAI,EAAC,QAAQ;0BAAAd,QAAA,EAEZ/B,YAAY,gBAAGf,OAAA,CAACJ,UAAU;4BAAAsD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACL,KAAK;4BAAAuD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CAAC,eACTrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;0BAACJ,IAAI,EAAC,SAAS;0BAAAd,QAAA,EAAC;wBAEtC;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNrD,OAAA,CAAChB,GAAG;oBAAC+D,EAAE,EAAE,CAAE;oBAAAD,QAAA,eACT9C,OAAA,CAACd,IAAI,CAACuE,KAAK;sBAACd,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAC1B9C,OAAA,CAACd,IAAI,CAACwE,KAAK;wBAAAZ,QAAA,EAAC;sBAAgB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACzCrD,OAAA;wBAAK2C,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,gBAChC9C,OAAA,CAACN,MAAM;0BAACiD,SAAS,EAAC;wBAAqE;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC1FrD,OAAA,CAACd,IAAI,CAACyE,OAAO;0BACXC,IAAI,EAAE3C,mBAAmB,GAAG,MAAM,GAAG,UAAW;0BAChDa,IAAI,EAAC,iBAAiB;0BACtBC,KAAK,EAAExB,QAAQ,CAACM,eAAgB;0BAChCgD,QAAQ,EAAElC,YAAa;0BACvBmC,QAAQ;0BACRC,WAAW,EAAC,kBAAkB;0BAC9BpB,SAAS,EAAC;wBAAW;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACFrD,OAAA,CAACb,MAAM;0BACLmE,OAAO,EAAC,MAAM;0BACdX,SAAS,EAAC,qEAAqE;0BAC/EuB,OAAO,EAAEA,CAAA,KAAMhD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;0BAC5D2C,IAAI,EAAC,QAAQ;0BAAAd,QAAA,EAEZ7B,mBAAmB,gBAAGjB,OAAA,CAACJ,UAAU;4BAAAsD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAGrD,OAAA,CAACL,KAAK;4BAAAuD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC,eACTrD,OAAA,CAACd,IAAI,CAACyE,OAAO,CAACK,QAAQ;0BAACJ,IAAI,EAAC,SAAS;0BAAAd,QAAA,EAAC;wBAEtC;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrD,OAAA,CAACb,MAAM;kBACLyE,IAAI,EAAC,QAAQ;kBACbN,OAAO,EAAC,SAAS;kBACjBX,SAAS,EAAC,YAAY;kBACtBwB,QAAQ,EAAEhD,OAAQ;kBAAA2B,QAAA,EAEjB3B,OAAO,gBACNnB,OAAA,CAAAE,SAAA;oBAAA4C,QAAA,gBACE9C,OAAA,CAACX,OAAO;sBAAC+E,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAAC1B,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,uBAE3D;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEPrD,OAAA;gBAAK2C,SAAS,EAAC,aAAa;gBAAAG,QAAA,eAC1B9C,OAAA;kBAAG2C,SAAS,EAAC,MAAM;kBAAAG,QAAA,GAAC,0BACM,EAAC,GAAG,eAC5B9C,OAAA,CAACV,IAAI;oBAACgF,EAAE,EAAC,QAAQ;oBAAC3B,SAAS,EAAC,mCAAmC;oBAAAG,QAAA,EAAC;kBAEhE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjD,EAAA,CAjRID,QAAQ;EAAA,QACKZ,WAAW,EACPO,OAAO;AAAA;AAAAyE,EAAA,GAFxBpE,QAAQ;AAmRd,eAAeA,QAAQ;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}