const express = require('express');
const cors = require('cors');

console.log('Starting simple server...');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

console.log('Middleware configured...');

// Test route
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'OK',
    message: 'Simple server is running',
    timestamp: new Date().toISOString()
  });
});

// Mock login route
app.post('/api/auth/login', (req, res) => {
  console.log('Login requested:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      data: {
        user: {
          _id: '1',
          firstName: 'Admin',
          lastName: 'User',
          email: email,
          role: 'admin',
          fullName: 'Admin User'
        },
        token: 'mock-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

console.log('Routes configured...');

const PORT = process.env.PORT || 5000;

console.log(`Attempting to start server on port ${PORT}...`);

app.listen(PORT, () => {
  console.log(`✓ Simple server running on port ${PORT}`);
  console.log(`✓ Health check: http://localhost:${PORT}/api/health`);
});
