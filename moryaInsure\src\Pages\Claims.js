import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Table, Badge, Modal, Form, Alert, Spinner } from 'react-bootstrap';
import { FaPlus, FaEye, FaFileAlt, FaCalendarAlt, FaMoneyBillWave, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { claimsAPI, policiesAPI } from '../services/api';

const Claims = () => {
  const { user } = useAuth();
  const [claims, setClaims] = useState([]);
  const [policies, setPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedClaim, setSelectedClaim] = useState(null);
  const [formData, setFormData] = useState({
    policyId: '',
    claimType: '',
    incidentDate: '',
    claimAmount: '',
    description: '',
    documents: []
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchClaims();
    fetchPolicies();
  }, []);

  const fetchClaims = async () => {
    try {
      const response = await claimsAPI.getClaims();
      if (response.success) {
        setClaims(response.data.claims || []);
      }
    } catch (error) {
      console.error('Error fetching claims:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPolicies = async () => {
    try {
      const response = await policiesAPI.getPolicies();
      if (response.success) {
        // Filter only active policies for the customer
        const activePolicies = response.data.policies.filter(
          policy => policy.status === 'active' && policy.policyHolder._id === user.id
        );
        setPolicies(activePolicies);
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData(prev => ({
      ...prev,
      documents: files
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const claimData = new FormData();
      claimData.append('policyId', formData.policyId);
      claimData.append('claimType', formData.claimType);
      claimData.append('incidentDate', formData.incidentDate);
      claimData.append('claimAmount', formData.claimAmount);
      claimData.append('description', formData.description);
      
      // Append documents
      formData.documents.forEach((file, index) => {
        claimData.append('documents', file);
      });

      const response = await claimsAPI.createClaim(claimData);
      
      if (response.success) {
        setSuccess('Claim submitted successfully! You will receive updates via email.');
        setShowModal(false);
        setFormData({
          policyId: '',
          claimType: '',
          incidentDate: '',
          claimAmount: '',
          description: '',
          documents: []
        });
        fetchClaims(); // Refresh claims list
      } else {
        setError(response.message || 'Failed to submit claim');
      }
    } catch (error) {
      console.error('Error submitting claim:', error);
      setError('Failed to submit claim. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'pending': { variant: 'warning', text: 'Pending Review' },
      'investigating': { variant: 'info', text: 'Under Investigation' },
      'approved': { variant: 'success', text: 'Approved' },
      'rejected': { variant: 'danger', text: 'Rejected' },
      'settled': { variant: 'primary', text: 'Settled' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Insurance Claims</h2>
              <p className="text-muted">File and track your insurance claims</p>
            </div>
            <Button 
              variant="primary" 
              onClick={() => setShowModal(true)}
              disabled={policies.length === 0}
            >
              <FaPlus className="me-2" />
              File New Claim
            </Button>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {policies.length === 0 && (
        <Alert variant="info">
          <FaExclamationTriangle className="me-2" />
          You need to have an active insurance policy to file a claim.
        </Alert>
      )}

      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Your Claims</h5>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading claims...</p>
                </div>
              ) : claims.length === 0 ? (
                <div className="text-center py-4">
                  <FaFileAlt size={48} className="text-muted mb-3" />
                  <h5>No Claims Found</h5>
                  <p className="text-muted">You haven't filed any claims yet.</p>
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Claim ID</th>
                      <th>Policy</th>
                      <th>Type</th>
                      <th>Incident Date</th>
                      <th>Claim Amount</th>
                      <th>Status</th>
                      <th>Filed Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {claims.map((claim) => (
                      <tr key={claim._id}>
                        <td>
                          <strong>{claim.claimNumber}</strong>
                        </td>
                        <td>{claim.policy?.policyNumber}</td>
                        <td>
                          <Badge bg="light" text="dark">
                            {claim.claimType}
                          </Badge>
                        </td>
                        <td>
                          <FaCalendarAlt className="me-1 text-muted" />
                          {formatDate(claim.incidentDate)}
                        </td>
                        <td>
                          <FaMoneyBillWave className="me-1 text-success" />
                          {formatCurrency(claim.claimAmount)}
                        </td>
                        <td>{getStatusBadge(claim.status)}</td>
                        <td>{formatDate(claim.createdAt)}</td>
                        <td>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => {
                              setSelectedClaim(claim);
                              // You can add a view details modal here
                            }}
                          >
                            <FaEye className="me-1" />
                            View
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* File New Claim Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>File New Insurance Claim</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            {error && <Alert variant="danger">{error}</Alert>}
            
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Select Policy *</Form.Label>
                  <Form.Select
                    name="policyId"
                    value={formData.policyId}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Choose policy...</option>
                    {policies.map(policy => (
                      <option key={policy._id} value={policy._id}>
                        {policy.policyNumber} - {policy.type}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Claim Type *</Form.Label>
                  <Form.Select
                    name="claimType"
                    value={formData.claimType}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select claim type...</option>
                    <option value="accident">Accident</option>
                    <option value="theft">Theft</option>
                    <option value="fire">Fire Damage</option>
                    <option value="natural_disaster">Natural Disaster</option>
                    <option value="medical">Medical</option>
                    <option value="other">Other</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Incident Date *</Form.Label>
                  <Form.Control
                    type="date"
                    name="incidentDate"
                    value={formData.incidentDate}
                    onChange={handleInputChange}
                    max={new Date().toISOString().split('T')[0]}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Claim Amount (₹) *</Form.Label>
                  <Form.Control
                    type="number"
                    name="claimAmount"
                    value={formData.claimAmount}
                    onChange={handleInputChange}
                    placeholder="Enter claim amount"
                    min="1"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description *</Form.Label>
              <Form.Control
                as="textarea"
                rows={4}
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe the incident and damages in detail..."
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Supporting Documents</Form.Label>
              <Form.Control
                type="file"
                multiple
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                onChange={handleFileChange}
              />
              <Form.Text className="text-muted">
                Upload photos, police reports, medical bills, etc. (PDF, JPG, PNG, DOC)
              </Form.Text>
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="primary" 
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Submitting...
                </>
              ) : (
                'Submit Claim'
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default Claims;
