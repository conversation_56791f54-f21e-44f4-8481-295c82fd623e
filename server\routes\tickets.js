const express = require('express');
const { body, validationResult } = require('express-validator');
const Ticket = require('../models/Ticket');
const { authenticate, adminOrEmployee } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/tickets
// @desc    Get tickets (role-based filtering)
// @access  Private
router.get('/', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    // Customers can only see their own tickets
    if (req.user.role === 'customer') {
      query.submittedBy = req.user._id;
    }
    
    // Filter by status if provided
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Filter by assigned agent for employees
    if (req.user.role === 'employee' && req.query.assigned === 'me') {
      query.assignedTo = req.user._id;
    }

    const tickets = await Ticket.find(query)
      .populate('submittedBy', 'firstName lastName email')
      .populate('assignedTo', 'firstName lastName email')
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Ticket.countDocuments(query);

    res.json({
      success: true,
      data: {
        tickets,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalTickets: total
        }
      }
    });
  } catch (error) {
    console.error('Get tickets error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching tickets'
    });
  }
});

// @route   POST /api/tickets
// @desc    Create new ticket
// @access  Private
router.post('/', authenticate, [
  body('title').trim().isLength({ min: 1, max: 200 }).withMessage('Title must be between 1 and 200 characters'),
  body('description').trim().isLength({ min: 1, max: 2000 }).withMessage('Description must be between 1 and 2000 characters'),
  body('category').isMongoId().withMessage('Valid category ID is required'),
  body('type').isIn(['general', 'technical', 'billing', 'claim', 'policy', 'complaint']).withMessage('Invalid ticket type'),
  body('priority').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('Invalid priority')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { title, description, category, type, priority, relatedPolicy } = req.body;

    const ticket = new Ticket({
      title,
      description,
      category,
      type,
      priority: priority || 'medium',
      submittedBy: req.user._id,
      relatedPolicy
    });

    await ticket.save();

    const populatedTicket = await Ticket.findById(ticket._id)
      .populate('submittedBy', 'firstName lastName email')
      .populate('category', 'name');

    res.status(201).json({
      success: true,
      message: 'Ticket created successfully',
      data: {
        ticket: populatedTicket
      }
    });
  } catch (error) {
    console.error('Create ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating ticket'
    });
  }
});

// @route   PUT /api/tickets/:id/assign
// @desc    Assign ticket to agent
// @access  Private (Admin/Employee)
router.put('/:id/assign', authenticate, adminOrEmployee, [
  body('assignedTo').isMongoId().withMessage('Valid agent ID is required')
], async (req, res) => {
  try {
    const { assignedTo } = req.body;
    
    const ticket = await Ticket.findById(req.params.id);
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    await ticket.assignTo(assignedTo);

    const updatedTicket = await Ticket.findById(ticket._id)
      .populate('submittedBy assignedTo category');

    res.json({
      success: true,
      message: 'Ticket assigned successfully',
      data: {
        ticket: updatedTicket
      }
    });
  } catch (error) {
    console.error('Assign ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while assigning ticket'
    });
  }
});

// @route   POST /api/tickets/:id/comments
// @desc    Add comment to ticket
// @access  Private
router.post('/:id/comments', authenticate, [
  body('content').trim().isLength({ min: 1, max: 1000 }).withMessage('Comment must be between 1 and 1000 characters'),
  body('isInternal').optional().isBoolean().withMessage('isInternal must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { content, isInternal } = req.body;
    const ticket = await Ticket.findById(req.params.id);
    
    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    // Check access permissions
    const canAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'employee' && ticket.assignedTo && ticket.assignedTo.toString() === req.user._id.toString()) ||
      (req.user.role === 'customer' && ticket.submittedBy.toString() === req.user._id.toString());

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Only staff can add internal comments
    const isInternalComment = isInternal && ['admin', 'employee'].includes(req.user.role);

    await ticket.addComment(content, req.user._id, isInternalComment);

    const updatedTicket = await Ticket.findById(ticket._id)
      .populate('submittedBy assignedTo category')
      .populate('comments.author', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Comment added successfully',
      data: {
        ticket: updatedTicket
      }
    });
  } catch (error) {
    console.error('Add comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while adding comment'
    });
  }
});

module.exports = router;
