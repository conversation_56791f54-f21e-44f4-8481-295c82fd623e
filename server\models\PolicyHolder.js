const mongoose = require('mongoose');

const policyHolderSchema = new mongoose.Schema({
  // Personal Information
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^\+?[\d\s-()]+$/, 'Please provide a valid phone number']
  },
  alternatePhone: {
    type: String,
    trim: true,
    match: [/^\+?[\d\s-()]+$/, 'Please provide a valid alternate phone number']
  },
  
  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true
    },
    zipCode: {
      type: String,
      required: [true, 'ZIP code is required'],
      trim: true
    },
    country: {
      type: String,
      required: [true, 'Country is required'],
      default: 'India',
      trim: true
    }
  },

  // Personal Details
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required']
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: [true, 'Gender is required']
  },
  maritalStatus: {
    type: String,
    enum: ['single', 'married', 'divorced', 'widowed'],
    default: 'single'
  },
  occupation: {
    type: String,
    trim: true
  },
  annualIncome: {
    type: Number,
    min: [0, 'Annual income cannot be negative']
  },

  // Identification
  identificationDocuments: [{
    type: {
      type: String,
      enum: ['aadhar', 'pan', 'passport', 'driving_license', 'voter_id'],
      required: true
    },
    number: {
      type: String,
      required: true,
      trim: true
    },
    verified: {
      type: Boolean,
      default: false
    },
    documentUrl: String
  }],

  // Emergency Contact
  emergencyContact: {
    name: {
      type: String,
      trim: true
    },
    relationship: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    }
  },

  // Beneficiaries
  beneficiaries: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    relationship: {
      type: String,
      required: true,
      trim: true
    },
    percentage: {
      type: Number,
      required: true,
      min: [0, 'Percentage cannot be negative'],
      max: [100, 'Percentage cannot exceed 100']
    },
    phone: String,
    email: String
  }],

  // Policy Information
  policies: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Policy'
  }],

  // Account Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending'],
    default: 'pending'
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDate: Date,
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Risk Assessment
  riskProfile: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  riskFactors: [String],
  medicalHistory: [{
    condition: String,
    diagnosedDate: Date,
    status: {
      type: String,
      enum: ['active', 'resolved', 'chronic']
    }
  }],

  // Financial Information
  bankDetails: {
    accountNumber: {
      type: String,
      trim: true
    },
    ifscCode: {
      type: String,
      trim: true
    },
    bankName: {
      type: String,
      trim: true
    },
    accountType: {
      type: String,
      enum: ['savings', 'current', 'salary']
    }
  },

  // Communication Preferences
  communicationPreferences: {
    email: {
      type: Boolean,
      default: true
    },
    sms: {
      type: Boolean,
      default: true
    },
    phone: {
      type: Boolean,
      default: false
    },
    whatsapp: {
      type: Boolean,
      default: false
    }
  },

  // Agent/Broker Information
  assignedAgent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  referredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Notes and Comments
  notes: [{
    content: {
      type: String,
      required: true
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['general', 'risk', 'financial', 'medical', 'verification'],
      default: 'general'
    }
  }],

  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastLoginDate: Date,
  lastContactDate: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (email already has unique index)
policyHolderSchema.index({ phone: 1 });
policyHolderSchema.index({ status: 1 });
policyHolderSchema.index({ assignedAgent: 1 });
policyHolderSchema.index({ createdAt: -1 });
policyHolderSchema.index({ 'address.city': 1 });
policyHolderSchema.index({ 'address.state': 1 });

// Virtual for full name
policyHolderSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for age
policyHolderSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for active policies count
policyHolderSchema.virtual('activePoliciesCount', {
  ref: 'Policy',
  localField: '_id',
  foreignField: 'policyHolder',
  count: true,
  match: { status: 'active' }
});

// Static method to find by email
policyHolderSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to search
policyHolderSchema.statics.search = function(searchTerm, filters = {}) {
  const query = {
    $or: [
      { firstName: { $regex: searchTerm, $options: 'i' } },
      { lastName: { $regex: searchTerm, $options: 'i' } },
      { email: { $regex: searchTerm, $options: 'i' } },
      { phone: { $regex: searchTerm, $options: 'i' } }
    ],
    ...filters
  };
  
  return this.find(query)
    .populate('assignedAgent', 'firstName lastName')
    .populate('policies', 'policyNumber type status')
    .sort({ createdAt: -1 });
};

// Method to calculate risk score
policyHolderSchema.methods.calculateRiskScore = function() {
  let score = 0;
  
  // Age factor
  const age = this.age;
  if (age > 60) score += 30;
  else if (age > 45) score += 20;
  else if (age > 30) score += 10;
  
  // Medical history factor
  if (this.medicalHistory && this.medicalHistory.length > 0) {
    score += this.medicalHistory.length * 10;
  }
  
  // Risk factors
  if (this.riskFactors && this.riskFactors.length > 0) {
    score += this.riskFactors.length * 5;
  }
  
  // Determine risk profile
  if (score >= 50) return 'high';
  if (score >= 25) return 'medium';
  return 'low';
};

// Pre-save middleware
policyHolderSchema.pre('save', function(next) {
  // Update risk profile
  this.riskProfile = this.calculateRiskScore();
  
  // Ensure beneficiaries percentages add up to 100%
  if (this.beneficiaries && this.beneficiaries.length > 0) {
    const totalPercentage = this.beneficiaries.reduce((sum, ben) => sum + ben.percentage, 0);
    if (totalPercentage !== 100) {
      return next(new Error('Beneficiaries percentages must add up to 100%'));
    }
  }
  
  next();
});

module.exports = mongoose.model('PolicyHolder', policyHolderSchema);
