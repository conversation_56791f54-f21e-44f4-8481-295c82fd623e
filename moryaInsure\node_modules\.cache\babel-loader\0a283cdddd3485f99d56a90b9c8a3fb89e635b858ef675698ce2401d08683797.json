{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\PolicyHolder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Form, Modal, Row, Col, Container, Card, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PolicyHolder = () => {\n  _s();\n  const [policyHolders, setPolicyHolders] = useState([]);\n  const [policies, setPolicies] = useState([]);\n  const [search, setSearch] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedHolder, setSelectedHolder] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form state\n  const [form, setForm] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    gender: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'India'\n    },\n    emergencyContact: {\n      name: '',\n      relationship: '',\n      phone: ''\n    },\n    occupation: '',\n    annualIncome: '',\n    status: 'active'\n  });\n  useEffect(() => {\n    fetchPolicyHolders();\n    fetchPolicies();\n  }, []);\n  const fetchPolicyHolders = async () => {\n    try {\n      setLoading(true);\n      const response = await policyHoldersAPI.getPolicyHolders();\n      if (response.success) {\n        setPolicyHolders(response.data.policyHolders || []);\n      } else {\n        setError('Failed to fetch policy holders');\n      }\n    } catch (error) {\n      console.error('Error fetching policy holders:', error);\n      setError('Failed to fetch policy holders');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchPolicies = async () => {\n    try {\n      const response = await policiesAPI.getPolicies();\n      if (response.success) {\n        setPolicies(response.data.policies || []);\n      }\n    } catch (error) {\n      console.error('Error fetching policies:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setForm(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setForm(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n    setError('');\n  };\n  const handleSave = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policyHoldersAPI.createPolicyHolder(form);\n      if (response.success) {\n        setSuccess('Policy holder created successfully!');\n        resetForm();\n        setShowModal(false);\n        fetchPolicyHolders();\n      } else {\n        setError(response.message || 'Failed to create policy holder');\n      }\n    } catch (error) {\n      console.error('Error creating policy holder:', error);\n      setError('Failed to create policy holder');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policyHoldersAPI.updatePolicyHolder(selectedHolder._id, form);\n      if (response.success) {\n        setSuccess('Policy holder updated successfully!');\n        setShowEditModal(false);\n        setSelectedHolder(null);\n        fetchPolicyHolders();\n      } else {\n        setError(response.message || 'Failed to update policy holder');\n      }\n    } catch (error) {\n      console.error('Error updating policy holder:', error);\n      setError('Failed to update policy holder');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDelete = async holderId => {\n    if (window.confirm('Are you sure you want to delete this policy holder?')) {\n      try {\n        const response = await policyHoldersAPI.deletePolicyHolder(holderId);\n        if (response.success) {\n          setSuccess('Policy holder deleted successfully!');\n          fetchPolicyHolders();\n        } else {\n          setError(response.message || 'Failed to delete policy holder');\n        }\n      } catch (error) {\n        console.error('Error deleting policy holder:', error);\n        setError('Failed to delete policy holder');\n      }\n    }\n  };\n  const resetForm = () => {\n    setForm({\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      dateOfBirth: '',\n      gender: '',\n      address: {\n        street: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        country: 'India'\n      },\n      emergencyContact: {\n        name: '',\n        relationship: '',\n        phone: ''\n      },\n      occupation: '',\n      annualIncome: '',\n      status: 'active'\n    });\n  };\n  const openCreateModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n  const openEditModal = holder => {\n    var _holder$address, _holder$address2, _holder$address3, _holder$address4, _holder$address5, _holder$emergencyCont, _holder$emergencyCont2, _holder$emergencyCont3;\n    setSelectedHolder(holder);\n    setForm({\n      firstName: holder.firstName,\n      lastName: holder.lastName,\n      email: holder.email,\n      phone: holder.phone,\n      dateOfBirth: holder.dateOfBirth ? new Date(holder.dateOfBirth).toISOString().split('T')[0] : '',\n      gender: holder.gender || '',\n      address: {\n        street: ((_holder$address = holder.address) === null || _holder$address === void 0 ? void 0 : _holder$address.street) || '',\n        city: ((_holder$address2 = holder.address) === null || _holder$address2 === void 0 ? void 0 : _holder$address2.city) || '',\n        state: ((_holder$address3 = holder.address) === null || _holder$address3 === void 0 ? void 0 : _holder$address3.state) || '',\n        zipCode: ((_holder$address4 = holder.address) === null || _holder$address4 === void 0 ? void 0 : _holder$address4.zipCode) || '',\n        country: ((_holder$address5 = holder.address) === null || _holder$address5 === void 0 ? void 0 : _holder$address5.country) || 'India'\n      },\n      emergencyContact: {\n        name: ((_holder$emergencyCont = holder.emergencyContact) === null || _holder$emergencyCont === void 0 ? void 0 : _holder$emergencyCont.name) || '',\n        relationship: ((_holder$emergencyCont2 = holder.emergencyContact) === null || _holder$emergencyCont2 === void 0 ? void 0 : _holder$emergencyCont2.relationship) || '',\n        phone: ((_holder$emergencyCont3 = holder.emergencyContact) === null || _holder$emergencyCont3 === void 0 ? void 0 : _holder$emergencyCont3.phone) || ''\n      },\n      occupation: holder.occupation || '',\n      annualIncome: holder.annualIncome || '',\n      status: holder.status || 'active'\n    });\n    setShowEditModal(true);\n  };\n  const openViewModal = holder => {\n    setSelectedHolder(holder);\n    setShowViewModal(true);\n  };\n  const filteredHolders = policyHolders.filter(holder => {\n    var _holder$email, _holder$phone;\n    return `${holder.firstName} ${holder.lastName}`.toLowerCase().includes(search.toLowerCase()) || ((_holder$email = holder.email) === null || _holder$email === void 0 ? void 0 : _holder$email.toLowerCase().includes(search.toLowerCase())) || ((_holder$phone = holder.phone) === null || _holder$phone === void 0 ? void 0 : _holder$phone.includes(search));\n  });\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'active': {\n        variant: 'success',\n        text: 'Active'\n      },\n      'inactive': {\n        variant: 'secondary',\n        text: 'Inactive'\n      },\n      'suspended': {\n        variant: 'warning',\n        text: 'Suspended'\n      },\n      'blocked': {\n        variant: 'danger',\n        text: 'Blocked'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDate = dateString => {\n    return dateString ? new Date(dateString).toLocaleDateString('en-IN') : 'N/A';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Policy Holders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search policy holders...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Holder Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Contact Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Policy Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sum Assured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Premium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tenure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredHolders.map(h => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.holderName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.contactNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.policyName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: h.subCategory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.sumAssured, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.premium, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [h.tenure, \" months\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge bg-${h.status === 'Active' ? 'success' : 'warning'}`,\n              children: h.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, h.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"New Policy Holder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Holder Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"holderName\",\n              value: form.holderName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Contact Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"contactNumber\",\n              value: form.contactNumber,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"policyName\",\n              value: form.policyName,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"category\",\n                value: form.category,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sub Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"subCategory\",\n                value: form.subCategory,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sum Assured\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"sumAssured\",\n                value: form.sumAssured,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"premium\",\n                value: form.premium,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tenure (Months)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"tenure\",\n              value: form.tenure,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(PolicyHolder, \"jySNE3FZ0lBwq294NSYCFHi4cCY=\");\n_c = PolicyHolder;\nexport default PolicyHolder;\nvar _c;\n$RefreshReg$(_c, \"PolicyHolder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Form", "Modal", "Row", "Col", "Container", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaSearch", "FaFileImport", "FaUser", "FaPhone", "FaEnvelope", "policyHoldersAPI", "policiesAPI", "jsxDEV", "_jsxDEV", "PolicyHolder", "_s", "policyHolders", "setPolicyHolders", "policies", "setPolicies", "search", "setSearch", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "showEditModal", "setShowEditModal", "showViewModal", "setShowViewModal", "selectedHolder", "setSelectedHolder", "submitting", "setSubmitting", "form", "setForm", "firstName", "lastName", "email", "phone", "dateOfBirth", "gender", "address", "street", "city", "state", "zipCode", "country", "emergencyContact", "name", "relationship", "occupation", "annualIncome", "status", "fetchPolicyHolders", "fetchPolicies", "response", "getPolicyHolders", "data", "console", "getPolicies", "handleChange", "e", "value", "target", "includes", "parent", "child", "split", "prev", "handleSave", "createPolicyHolder", "resetForm", "message", "handleEdit", "updatePolicyHolder", "_id", "handleDelete", "holderId", "window", "confirm", "deletePolicyHolder", "openCreateModal", "openEditModal", "holder", "_holder$address", "_holder$address2", "_holder$address3", "_holder$address4", "_holder$address5", "_holder$emergencyCont", "_holder$emergencyCont2", "_holder$emergencyCont3", "Date", "toISOString", "openViewModal", "filteredHolders", "filter", "_holder$email", "_holder$phone", "toLowerCase", "getStatusBadge", "statusConfig", "variant", "text", "config", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "toLocaleDateString", "className", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "h", "<PERSON><PERSON><PERSON>", "contactNumber", "policyName", "category", "subCategory", "sumAssured", "premium", "tenure", "id", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/PolicyHolder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>, Form, Modal, <PERSON>, Col, Con<PERSON>er, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\r\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\r\n\r\nconst PolicyHolder = () => {\r\n  const [policyHolders, setPolicyHolders] = useState([]);\r\n  const [policies, setPolicies] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedHolder, setSelectedHolder] = useState(null);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  // Form state\r\n  const [form, setForm] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    phone: '',\r\n    dateOfBirth: '',\r\n    gender: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: 'India'\r\n    },\r\n    emergencyContact: {\r\n      name: '',\r\n      relationship: '',\r\n      phone: ''\r\n    },\r\n    occupation: '',\r\n    annualIncome: '',\r\n    status: 'active'\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchPolicyHolders();\r\n    fetchPolicies();\r\n  }, []);\r\n\r\n  const fetchPolicyHolders = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await policyHoldersAPI.getPolicyHolders();\r\n      if (response.success) {\r\n        setPolicyHolders(response.data.policyHolders || []);\r\n      } else {\r\n        setError('Failed to fetch policy holders');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policy holders:', error);\r\n      setError('Failed to fetch policy holders');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchPolicies = async () => {\r\n    try {\r\n      const response = await policiesAPI.getPolicies();\r\n      if (response.success) {\r\n        setPolicies(response.data.policies || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policies:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      setForm(prev => ({\r\n        ...prev,\r\n        [parent]: {\r\n          ...prev[parent],\r\n          [child]: value\r\n        }\r\n      }));\r\n    } else {\r\n      setForm(prev => ({\r\n        ...prev,\r\n        [name]: value\r\n      }));\r\n    }\r\n    setError('');\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policyHoldersAPI.createPolicyHolder(form);\r\n      if (response.success) {\r\n        setSuccess('Policy holder created successfully!');\r\n        resetForm();\r\n        setShowModal(false);\r\n        fetchPolicyHolders();\r\n      } else {\r\n        setError(response.message || 'Failed to create policy holder');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating policy holder:', error);\r\n      setError('Failed to create policy holder');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleEdit = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policyHoldersAPI.updatePolicyHolder(selectedHolder._id, form);\r\n      if (response.success) {\r\n        setSuccess('Policy holder updated successfully!');\r\n        setShowEditModal(false);\r\n        setSelectedHolder(null);\r\n        fetchPolicyHolders();\r\n      } else {\r\n        setError(response.message || 'Failed to update policy holder');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating policy holder:', error);\r\n      setError('Failed to update policy holder');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (holderId) => {\r\n    if (window.confirm('Are you sure you want to delete this policy holder?')) {\r\n      try {\r\n        const response = await policyHoldersAPI.deletePolicyHolder(holderId);\r\n        if (response.success) {\r\n          setSuccess('Policy holder deleted successfully!');\r\n          fetchPolicyHolders();\r\n        } else {\r\n          setError(response.message || 'Failed to delete policy holder');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error deleting policy holder:', error);\r\n        setError('Failed to delete policy holder');\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setForm({\r\n      firstName: '',\r\n      lastName: '',\r\n      email: '',\r\n      phone: '',\r\n      dateOfBirth: '',\r\n      gender: '',\r\n      address: {\r\n        street: '',\r\n        city: '',\r\n        state: '',\r\n        zipCode: '',\r\n        country: 'India'\r\n      },\r\n      emergencyContact: {\r\n        name: '',\r\n        relationship: '',\r\n        phone: ''\r\n      },\r\n      occupation: '',\r\n      annualIncome: '',\r\n      status: 'active'\r\n    });\r\n  };\r\n\r\n  const openCreateModal = () => {\r\n    resetForm();\r\n    setShowModal(true);\r\n  };\r\n\r\n  const openEditModal = (holder) => {\r\n    setSelectedHolder(holder);\r\n    setForm({\r\n      firstName: holder.firstName,\r\n      lastName: holder.lastName,\r\n      email: holder.email,\r\n      phone: holder.phone,\r\n      dateOfBirth: holder.dateOfBirth ? new Date(holder.dateOfBirth).toISOString().split('T')[0] : '',\r\n      gender: holder.gender || '',\r\n      address: {\r\n        street: holder.address?.street || '',\r\n        city: holder.address?.city || '',\r\n        state: holder.address?.state || '',\r\n        zipCode: holder.address?.zipCode || '',\r\n        country: holder.address?.country || 'India'\r\n      },\r\n      emergencyContact: {\r\n        name: holder.emergencyContact?.name || '',\r\n        relationship: holder.emergencyContact?.relationship || '',\r\n        phone: holder.emergencyContact?.phone || ''\r\n      },\r\n      occupation: holder.occupation || '',\r\n      annualIncome: holder.annualIncome || '',\r\n      status: holder.status || 'active'\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const openViewModal = (holder) => {\r\n    setSelectedHolder(holder);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const filteredHolders = policyHolders.filter((holder) =>\r\n    `${holder.firstName} ${holder.lastName}`.toLowerCase().includes(search.toLowerCase()) ||\r\n    holder.email?.toLowerCase().includes(search.toLowerCase()) ||\r\n    holder.phone?.includes(search)\r\n  );\r\n\r\n  const getStatusBadge = (status) => {\r\n    const statusConfig = {\r\n      'active': { variant: 'success', text: 'Active' },\r\n      'inactive': { variant: 'secondary', text: 'Inactive' },\r\n      'suspended': { variant: 'warning', text: 'Suspended' },\r\n      'blocked': { variant: 'danger', text: 'Blocked' }\r\n    };\r\n\r\n    const config = statusConfig[status] || { variant: 'secondary', text: status };\r\n    return <Badge bg={config.variant}>{config.text}</Badge>;\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    return dateString ? new Date(dateString).toLocaleDateString('en-IN') : 'N/A';\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h4 className=\"fw-bold text-uppercase\">Policy Holders</h4>\r\n        <Button variant=\"primary\" onClick={() => setShowModal(true)}>+ New</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search policy holders...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Policy Holder Name</th>\r\n            <th>Contact Number</th>\r\n            <th>Policy Name</th>\r\n            <th>Category</th>\r\n            <th>Sub Category</th>\r\n            <th>Sum Assured</th>\r\n            <th>Premium</th>\r\n            <th>Tenure</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredHolders.map((h) => (\r\n            <tr key={h.id}>\r\n              <td>{h.holderName}</td>\r\n              <td>{h.contactNumber}</td>\r\n              <td>{h.policyName}</td>\r\n              <td>{h.category}</td>\r\n              <td>{h.subCategory}</td>\r\n              <td>{h.sumAssured} PHP</td>\r\n              <td>{h.premium} PHP</td>\r\n              <td>{h.tenure} months</td>\r\n              <td><span className={`badge bg-${h.status === 'Active' ? 'success' : 'warning'}`}>{h.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>New Policy Holder</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Holder Name</Form.Label>\r\n              <Form.Control name=\"holderName\" value={form.holderName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Contact Number</Form.Label>\r\n              <Form.Control name=\"contactNumber\" value={form.contactNumber} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Name</Form.Label>\r\n              <Form.Control name=\"policyName\" value={form.policyName} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Category</Form.Label>\r\n                <Form.Select name=\"category\" value={form.category} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Auto Insurance</option>\r\n                  <option>Life Insurance</option>\r\n                  <option>Travel Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Sub Category</Form.Label>\r\n                <Form.Select name=\"subCategory\" value={form.subCategory} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Comprehensive Coverage</option>\r\n                  <option>Term Life Insurance</option>\r\n                  <option>Travel Cancellation Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Sum Assured</Form.Label>\r\n                <Form.Control name=\"sumAssured\" value={form.sumAssured} onChange={handleChange} />\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Premium</Form.Label>\r\n                <Form.Control name=\"premium\" value={form.premium} onChange={handleChange} />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Tenure (Months)</Form.Label>\r\n              <Form.Control name=\"tenure\" value={form.tenure} onChange={handleChange} />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={handleSave}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PolicyHolder;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC9G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AACpH,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAGnD,QAAQ,CAAC;IAC/BoD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBX,KAAK,EAAE;IACT,CAAC;IACDY,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEFpE,SAAS,CAAC,MAAM;IACdqE,kBAAkB,CAAC,CAAC;IACpBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,QAAQ,GAAG,MAAMlD,gBAAgB,CAACmD,gBAAgB,CAAC,CAAC;MAC1D,IAAID,QAAQ,CAAClC,OAAO,EAAE;QACpBT,gBAAgB,CAAC2C,QAAQ,CAACE,IAAI,CAAC9C,aAAa,IAAI,EAAE,CAAC;MACrD,CAAC,MAAM;QACLS,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjD,WAAW,CAACqD,WAAW,CAAC,CAAC;MAChD,IAAIJ,QAAQ,CAAClC,OAAO,EAAE;QACpBP,WAAW,CAACyC,QAAQ,CAACE,IAAI,CAAC5C,QAAQ,IAAI,EAAE,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEb,IAAI;MAAEc;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIf,IAAI,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGlB,IAAI,CAACmB,KAAK,CAAC,GAAG,CAAC;MACvCjC,OAAO,CAACkC,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACH,MAAM,GAAG;UACR,GAAGG,IAAI,CAACH,MAAM,CAAC;UACf,CAACC,KAAK,GAAGJ;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL5B,OAAO,CAACkC,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACpB,IAAI,GAAGc;MACV,CAAC,CAAC,CAAC;IACL;IACA1C,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMiD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMmC,QAAQ,GAAG,MAAMlD,gBAAgB,CAACiE,kBAAkB,CAACrC,IAAI,CAAC;MAChE,IAAIsB,QAAQ,CAAClC,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjDiD,SAAS,CAAC,CAAC;QACX/C,YAAY,CAAC,KAAK,CAAC;QACnB6B,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMmC,QAAQ,GAAG,MAAMlD,gBAAgB,CAACqE,kBAAkB,CAAC7C,cAAc,CAAC8C,GAAG,EAAE1C,IAAI,CAAC;MACpF,IAAIsB,QAAQ,CAAClC,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjDI,gBAAgB,CAAC,KAAK,CAAC;QACvBI,iBAAiB,CAAC,IAAI,CAAC;QACvBuB,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAIC,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MACzE,IAAI;QACF,MAAMxB,QAAQ,GAAG,MAAMlD,gBAAgB,CAAC2E,kBAAkB,CAACH,QAAQ,CAAC;QACpE,IAAItB,QAAQ,CAAClC,OAAO,EAAE;UACpBC,UAAU,CAAC,qCAAqC,CAAC;UACjD+B,kBAAkB,CAAC,CAAC;QACtB,CAAC,MAAM;UACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;QAChE;MACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;QACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDC,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMmD,SAAS,GAAGA,CAAA,KAAM;IACtBrC,OAAO,CAAC;MACNC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;MACX,CAAC;MACDC,gBAAgB,EAAE;QAChBC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChBX,KAAK,EAAE;MACT,CAAC;MACDY,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BV,SAAS,CAAC,CAAC;IACX/C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0D,aAAa,GAAIC,MAAM,IAAK;IAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChC7D,iBAAiB,CAACqD,MAAM,CAAC;IACzBjD,OAAO,CAAC;MACNC,SAAS,EAAEgD,MAAM,CAAChD,SAAS;MAC3BC,QAAQ,EAAE+C,MAAM,CAAC/C,QAAQ;MACzBC,KAAK,EAAE8C,MAAM,CAAC9C,KAAK;MACnBC,KAAK,EAAE6C,MAAM,CAAC7C,KAAK;MACnBC,WAAW,EAAE4C,MAAM,CAAC5C,WAAW,GAAG,IAAIqD,IAAI,CAACT,MAAM,CAAC5C,WAAW,CAAC,CAACsD,WAAW,CAAC,CAAC,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MAC/F3B,MAAM,EAAE2C,MAAM,CAAC3C,MAAM,IAAI,EAAE;MAC3BC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAA0C,eAAA,GAAAD,MAAM,CAAC1C,OAAO,cAAA2C,eAAA,uBAAdA,eAAA,CAAgB1C,MAAM,KAAI,EAAE;QACpCC,IAAI,EAAE,EAAA0C,gBAAA,GAAAF,MAAM,CAAC1C,OAAO,cAAA4C,gBAAA,uBAAdA,gBAAA,CAAgB1C,IAAI,KAAI,EAAE;QAChCC,KAAK,EAAE,EAAA0C,gBAAA,GAAAH,MAAM,CAAC1C,OAAO,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgB1C,KAAK,KAAI,EAAE;QAClCC,OAAO,EAAE,EAAA0C,gBAAA,GAAAJ,MAAM,CAAC1C,OAAO,cAAA8C,gBAAA,uBAAdA,gBAAA,CAAgB1C,OAAO,KAAI,EAAE;QACtCC,OAAO,EAAE,EAAA0C,gBAAA,GAAAL,MAAM,CAAC1C,OAAO,cAAA+C,gBAAA,uBAAdA,gBAAA,CAAgB1C,OAAO,KAAI;MACtC,CAAC;MACDC,gBAAgB,EAAE;QAChBC,IAAI,EAAE,EAAAyC,qBAAA,GAAAN,MAAM,CAACpC,gBAAgB,cAAA0C,qBAAA,uBAAvBA,qBAAA,CAAyBzC,IAAI,KAAI,EAAE;QACzCC,YAAY,EAAE,EAAAyC,sBAAA,GAAAP,MAAM,CAACpC,gBAAgB,cAAA2C,sBAAA,uBAAvBA,sBAAA,CAAyBzC,YAAY,KAAI,EAAE;QACzDX,KAAK,EAAE,EAAAqD,sBAAA,GAAAR,MAAM,CAACpC,gBAAgB,cAAA4C,sBAAA,uBAAvBA,sBAAA,CAAyBrD,KAAK,KAAI;MAC3C,CAAC;MACDY,UAAU,EAAEiC,MAAM,CAACjC,UAAU,IAAI,EAAE;MACnCC,YAAY,EAAEgC,MAAM,CAAChC,YAAY,IAAI,EAAE;MACvCC,MAAM,EAAE+B,MAAM,CAAC/B,MAAM,IAAI;IAC3B,CAAC,CAAC;IACF1B,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMoE,aAAa,GAAIX,MAAM,IAAK;IAChCrD,iBAAiB,CAACqD,MAAM,CAAC;IACzBvD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmE,eAAe,GAAGpF,aAAa,CAACqF,MAAM,CAAEb,MAAM;IAAA,IAAAc,aAAA,EAAAC,aAAA;IAAA,OAClD,GAAGf,MAAM,CAAChD,SAAS,IAAIgD,MAAM,CAAC/C,QAAQ,EAAE,CAAC+D,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACjD,MAAM,CAACoF,WAAW,CAAC,CAAC,CAAC,MAAAF,aAAA,GACrFd,MAAM,CAAC9C,KAAK,cAAA4D,aAAA,uBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACjD,MAAM,CAACoF,WAAW,CAAC,CAAC,CAAC,OAAAD,aAAA,GAC1Df,MAAM,CAAC7C,KAAK,cAAA4D,aAAA,uBAAZA,aAAA,CAAclC,QAAQ,CAACjD,MAAM,CAAC;EAAA,CAChC,CAAC;EAED,MAAMqF,cAAc,GAAIhD,MAAM,IAAK;IACjC,MAAMiD,YAAY,GAAG;MACnB,QAAQ,EAAE;QAAEC,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAS,CAAC;MAChD,UAAU,EAAE;QAAED,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAW,CAAC;MACtD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAY,CAAC;MACtD,SAAS,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAU;IAClD,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACjD,MAAM,CAAC,IAAI;MAAEkD,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAEnD;IAAO,CAAC;IAC7E,oBAAO5C,OAAA,CAACb,KAAK;MAAC8G,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,EAAEF,MAAM,CAACD;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACzD,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAOA,UAAU,GAAG,IAAIpB,IAAI,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,GAAG,KAAK;EAC9E,CAAC;EAED,oBACEzG,OAAA;IAAK0G,SAAS,EAAC,qBAAqB;IAAAR,QAAA,gBAClClG,OAAA;MAAK0G,SAAS,EAAC,wDAAwD;MAAAR,QAAA,gBACrElG,OAAA;QAAI0G,SAAS,EAAC,wBAAwB;QAAAR,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DtG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,SAAS;QAACa,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,IAAI,CAAE;QAAAkF,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eAENtG,OAAA;MAAK0G,SAAS,EAAC,6BAA6B;MAAAR,QAAA,gBAC1ClG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,mBAAmB;QAACc,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DtG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,mBAAmB;QAACc,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DtG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,mBAAmB;QAACc,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DtG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,mBAAmB;QAACc,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DtG,OAAA,CAACtB,MAAM;QAACoH,OAAO,EAAC,mBAAmB;QAACc,IAAI,EAAC,IAAI;QAAAV,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENtG,OAAA;MAAK0G,SAAS,EAAC,MAAM;MAAAR,QAAA,eACnBlG,OAAA,CAACrB,IAAI,CAACkI,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,0BAA0B;QACtCzD,KAAK,EAAE/C,MAAO;QACdyG,QAAQ,EAAG3D,CAAC,IAAK7C,SAAS,CAAC6C,CAAC,CAACE,MAAM,CAACD,KAAK;MAAE;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtG,OAAA,CAACvB,KAAK;MAACwI,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACT,SAAS,EAAC,WAAW;MAAAR,QAAA,gBACpDlG,OAAA;QAAO0G,SAAS,EAAC,eAAe;QAAAR,QAAA,eAC9BlG,OAAA;UAAAkG,QAAA,gBACElG,OAAA;YAAAkG,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BtG,OAAA;YAAAkG,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBtG,OAAA;YAAAkG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBtG,OAAA;YAAAkG,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBtG,OAAA;YAAAkG,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBtG,OAAA;YAAAkG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBtG,OAAA;YAAAkG,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBtG,OAAA;YAAAkG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACftG,OAAA;YAAAkG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACftG,OAAA;YAAAkG,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRtG,OAAA;QAAAkG,QAAA,EACGX,eAAe,CAAC6B,GAAG,CAAEC,CAAC,iBACrBrH,OAAA;UAAAkG,QAAA,gBACElG,OAAA;YAAAkG,QAAA,EAAKmB,CAAC,CAACC;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBtG,OAAA;YAAAkG,QAAA,EAAKmB,CAAC,CAACE;UAAa;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BtG,OAAA;YAAAkG,QAAA,EAAKmB,CAAC,CAACG;UAAU;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBtG,OAAA;YAAAkG,QAAA,EAAKmB,CAAC,CAACI;UAAQ;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBtG,OAAA;YAAAkG,QAAA,EAAKmB,CAAC,CAACK;UAAW;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBtG,OAAA;YAAAkG,QAAA,GAAKmB,CAAC,CAACM,UAAU,EAAC,MAAI;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BtG,OAAA;YAAAkG,QAAA,GAAKmB,CAAC,CAACO,OAAO,EAAC,MAAI;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBtG,OAAA;YAAAkG,QAAA,GAAKmB,CAAC,CAACQ,MAAM,EAAC,SAAO;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BtG,OAAA;YAAAkG,QAAA,eAAIlG,OAAA;cAAM0G,SAAS,EAAE,YAAYW,CAAC,CAACzE,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAG;cAAAsD,QAAA,EAAEmB,CAAC,CAACzE;YAAM;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxGtG,OAAA;YAAAkG,QAAA,gBACElG,OAAA,CAACtB,MAAM;cAACoH,OAAO,EAAC,SAAS;cAACc,IAAI,EAAC,IAAI;cAACF,SAAS,EAAC,MAAM;cAAAR,QAAA,eAAClG,OAAA,CAACX,MAAM;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxEtG,OAAA,CAACtB,MAAM;cAACoH,OAAO,EAAC,QAAQ;cAACc,IAAI,EAAC,IAAI;cAAAV,QAAA,eAAClG,OAAA,CAACV,OAAO;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAbEe,CAAC,CAACS,EAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcT,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRtG,OAAA,CAACpB,KAAK;MAACmJ,IAAI,EAAEhH,SAAU;MAACiH,MAAM,EAAEA,CAAA,KAAMhH,YAAY,CAAC,KAAK,CAAE;MAACiH,QAAQ;MAAA/B,QAAA,gBACjElG,OAAA,CAACpB,KAAK,CAACsJ,MAAM;QAACC,WAAW;QAAAjC,QAAA,eACvBlG,OAAA,CAACpB,KAAK,CAACwJ,KAAK;UAAAlC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACftG,OAAA,CAACpB,KAAK,CAACyJ,IAAI;QAAAnC,QAAA,eACTlG,OAAA,CAACrB,IAAI;UAAAuH,QAAA,gBACHlG,OAAA,CAACrB,IAAI,CAAC2J,KAAK;YAAC5B,SAAS,EAAC,MAAM;YAAAR,QAAA,gBAC1BlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;cAAArC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;cAACrE,IAAI,EAAC,YAAY;cAACc,KAAK,EAAE7B,IAAI,CAAC6F,UAAW;cAACN,QAAQ,EAAE5D;YAAa;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbtG,OAAA,CAACrB,IAAI,CAAC2J,KAAK;YAAC5B,SAAS,EAAC,MAAM;YAAAR,QAAA,gBAC1BlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;cAAArC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;cAACrE,IAAI,EAAC,eAAe;cAACc,KAAK,EAAE7B,IAAI,CAAC8F,aAAc;cAACP,QAAQ,EAAE5D;YAAa;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEbtG,OAAA,CAACrB,IAAI,CAAC2J,KAAK;YAAC5B,SAAS,EAAC,MAAM;YAAAR,QAAA,gBAC1BlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;cAAArC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;cAACrE,IAAI,EAAC,YAAY;cAACc,KAAK,EAAE7B,IAAI,CAAC+F,UAAW;cAACR,QAAQ,EAAE5D;YAAa;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbtG,OAAA,CAACnB,GAAG;YAAC6H,SAAS,EAAC,MAAM;YAAAR,QAAA,gBACnBlG,OAAA,CAAClB,GAAG;cAAAoH,QAAA,gBACFlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;gBAAArC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCtG,OAAA,CAACrB,IAAI,CAAC6J,MAAM;gBAAChG,IAAI,EAAC,UAAU;gBAACc,KAAK,EAAE7B,IAAI,CAACgG,QAAS;gBAACT,QAAQ,EAAE5D,YAAa;gBAAA8C,QAAA,eACxElG,OAAA;kBAAQsD,KAAK,EAAC,EAAE;kBAAA4C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNtG,OAAA,CAAClB,GAAG;cAAAoH,QAAA,gBACFlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;gBAAArC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCtG,OAAA,CAACrB,IAAI,CAAC6J,MAAM;gBAAChG,IAAI,EAAC,aAAa;gBAACc,KAAK,EAAE7B,IAAI,CAACiG,WAAY;gBAACV,QAAQ,EAAE5D,YAAa;gBAAA8C,QAAA,eAC9ElG,OAAA;kBAAQsD,KAAK,EAAC,EAAE;kBAAA4C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtG,OAAA,CAACnB,GAAG;YAAC6H,SAAS,EAAC,MAAM;YAAAR,QAAA,gBACnBlG,OAAA,CAAClB,GAAG;cAAAoH,QAAA,gBACFlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;gBAAArC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;gBAACrE,IAAI,EAAC,YAAY;gBAACc,KAAK,EAAE7B,IAAI,CAACkG,UAAW;gBAACX,QAAQ,EAAE5D;cAAa;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNtG,OAAA,CAAClB,GAAG;cAAAoH,QAAA,gBACFlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;gBAAArC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChCtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;gBAACrE,IAAI,EAAC,SAAS;gBAACc,KAAK,EAAE7B,IAAI,CAACmG,OAAQ;gBAACZ,QAAQ,EAAE5D;cAAa;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtG,OAAA,CAACrB,IAAI,CAAC2J,KAAK;YAAC5B,SAAS,EAAC,MAAM;YAAAR,QAAA,gBAC1BlG,OAAA,CAACrB,IAAI,CAAC4J,KAAK;cAAArC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxCtG,OAAA,CAACrB,IAAI,CAACkI,OAAO;cAACrE,IAAI,EAAC,QAAQ;cAACc,KAAK,EAAE7B,IAAI,CAACoG,MAAO;cAACb,QAAQ,EAAE5D;YAAa;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbtG,OAAA,CAACpB,KAAK,CAAC6J,MAAM;QAAAvC,QAAA,gBACXlG,OAAA,CAACtB,MAAM;UAACoH,OAAO,EAAC,WAAW;UAACa,OAAO,EAAEA,CAAA,KAAM3F,YAAY,CAAC,KAAK,CAAE;UAAAkF,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EtG,OAAA,CAACtB,MAAM;UAACoH,OAAO,EAAC,SAAS;UAACa,OAAO,EAAE9C,UAAW;UAAAqC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpG,EAAA,CA/WID,YAAY;AAAAyI,EAAA,GAAZzI,YAAY;AAiXlB,eAAeA,YAAY;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}