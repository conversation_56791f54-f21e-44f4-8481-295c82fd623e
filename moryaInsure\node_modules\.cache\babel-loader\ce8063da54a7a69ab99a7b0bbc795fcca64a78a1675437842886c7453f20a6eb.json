{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [theme, setTheme] = useState('light');\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('moryaInsure_theme');\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setTheme(prefersDark ? 'dark' : 'light');\n    }\n    setIsLoading(false);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (isLoading) return;\n    const root = document.documentElement;\n\n    // Remove existing theme classes\n    root.classList.remove('theme-light', 'theme-dark');\n\n    // Add current theme class\n    root.classList.add(`theme-${theme}`);\n\n    // Set Bootstrap data-bs-theme attribute\n    root.setAttribute('data-bs-theme', theme);\n\n    // Save to localStorage\n    localStorage.setItem('moryaInsure_theme', theme);\n    console.log(`🎨 Theme changed to: ${theme}`);\n  }, [theme, isLoading]);\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n  const setLightTheme = () => {\n    setTheme('light');\n  };\n  const setDarkTheme = () => {\n    setTheme('dark');\n  };\n  const value = {\n    theme,\n    isLight: theme === 'light',\n    isDark: theme === 'dark',\n    toggleTheme,\n    setLightTheme,\n    setDarkTheme,\n    isLoading\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"oW77ZtL4198LwzaDF5fp9PWZb5g=\");\n_c = ThemeProvider;\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "theme", "setTheme", "isLoading", "setIsLoading", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "root", "document", "documentElement", "classList", "remove", "add", "setAttribute", "setItem", "console", "log", "toggleTheme", "prevTheme", "setLightTheme", "setDarkTheme", "value", "isLight", "isDark", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState('light');\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('moryaInsure_theme');\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setTheme(prefersDark ? 'dark' : 'light');\n    }\n    setIsLoading(false);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (isLoading) return;\n\n    const root = document.documentElement;\n    \n    // Remove existing theme classes\n    root.classList.remove('theme-light', 'theme-dark');\n    \n    // Add current theme class\n    root.classList.add(`theme-${theme}`);\n    \n    // Set Bootstrap data-bs-theme attribute\n    root.setAttribute('data-bs-theme', theme);\n    \n    // Save to localStorage\n    localStorage.setItem('moryaInsure_theme', theme);\n    \n    console.log(`🎨 Theme changed to: ${theme}`);\n  }, [theme, isLoading]);\n\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const setLightTheme = () => {\n    setTheme('light');\n  };\n\n  const setDarkTheme = () => {\n    setTheme('dark');\n  };\n\n  const value = {\n    theme,\n    isLight: theme === 'light',\n    isDark: theme === 'dark',\n    toggleTheme,\n    setLightTheme,\n    setDarkTheme,\n    isLoading\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC5D,IAAIF,UAAU,KAAKA,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,MAAM,CAAC,EAAE;MACnEH,QAAQ,CAACG,UAAU,CAAC;IACtB,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAC7ET,QAAQ,CAACM,WAAW,GAAG,MAAM,GAAG,OAAO,CAAC;IAC1C;IACAJ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAd,SAAS,CAAC,MAAM;IACd,IAAIa,SAAS,EAAE;IAEf,MAAMS,IAAI,GAAGC,QAAQ,CAACC,eAAe;;IAErC;IACAF,IAAI,CAACG,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC;;IAElD;IACAJ,IAAI,CAACG,SAAS,CAACE,GAAG,CAAC,SAAShB,KAAK,EAAE,CAAC;;IAEpC;IACAW,IAAI,CAACM,YAAY,CAAC,eAAe,EAAEjB,KAAK,CAAC;;IAEzC;IACAK,YAAY,CAACa,OAAO,CAAC,mBAAmB,EAAElB,KAAK,CAAC;IAEhDmB,OAAO,CAACC,GAAG,CAAC,wBAAwBpB,KAAK,EAAE,CAAC;EAC9C,CAAC,EAAE,CAACA,KAAK,EAAEE,SAAS,CAAC,CAAC;EAEtB,MAAMmB,WAAW,GAAGA,CAAA,KAAM;IACxBpB,QAAQ,CAACqB,SAAS,IAAIA,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EACjE,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BtB,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzBvB,QAAQ,CAAC,MAAM,CAAC;EAClB,CAAC;EAED,MAAMwB,KAAK,GAAG;IACZzB,KAAK;IACL0B,OAAO,EAAE1B,KAAK,KAAK,OAAO;IAC1B2B,MAAM,EAAE3B,KAAK,KAAK,MAAM;IACxBqB,WAAW;IACXE,aAAa;IACbC,YAAY;IACZtB;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,YAAY,CAACoC,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EACjCA;EAAQ;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACjC,GAAA,CAjEWF,aAAa;AAAAoC,EAAA,GAAbpC,aAAa;AAmE1B,eAAeL,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}