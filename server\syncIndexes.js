const mongoose = require('mongoose');
require('dotenv').config();

// Import all models
const User = require('./models/User');
const Category = require('./models/Category');
const SubCategory = require('./models/SubCategory');
const Policy = require('./models/Policy');
const PolicyHolder = require('./models/PolicyHolder');
const Ticket = require('./models/Ticket');
const TicketCategory = require('./models/TicketCategory');
const Claim = require('./models/Claim');
const Notification = require('./models/Notification');
const SystemSettings = require('./models/SystemSettings');

async function syncAllIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/insurance23jun');
    console.log('✓ Connected to MongoDB');

    const models = [
      User, Category, SubCategory, Policy, PolicyHolder, 
      Ticket, TicketCategory, Claim, Notification, SystemSettings
    ];

    console.log('\n🔧 Synchronizing indexes for all models...\n');

    for (const model of models) {
      try {
        console.log(`📋 Processing ${model.modelName}...`);
        
        // Sync indexes - this will drop unused indexes and create missing ones
        await model.syncIndexes();
        console.log(`✅ ${model.modelName} indexes synchronized`);
        
      } catch (error) {
        console.log(`❌ Error syncing ${model.modelName}:`, error.message);
      }
    }

    console.log('\n🎉 Index synchronization complete!');
    console.log('\n📋 Final index summary:');
    
    // Show final indexes for verification
    for (const model of models) {
      try {
        const indexes = await model.collection.getIndexes();
        const indexNames = Object.keys(indexes).filter(name => name !== '_id_');
        console.log(`  ${model.modelName}: ${indexNames.length} custom indexes`);
      } catch (error) {
        console.log(`  ${model.modelName}: Error reading indexes`);
      }
    }

  } catch (error) {
    console.error('❌ Error during index synchronization:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✓ Disconnected from MongoDB');
    console.log('\n🚀 You can now restart your server - duplicate index warnings should be gone!');
  }
}

// Run the sync
syncAllIndexes();
