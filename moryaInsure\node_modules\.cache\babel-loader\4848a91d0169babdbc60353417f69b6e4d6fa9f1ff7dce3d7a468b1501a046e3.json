{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\layout\\\\MainLayout.js\";\nimport React from \"react\";\n// import Sidebar from '../components/Sidebar';\nimport { Outlet } from \"react-router-dom\";\nimport Sidebar from \"../Component/Sidebar\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainLayout = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 p-4\",\n      style: {\n        marginLeft: \"10px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "Outlet", "Sidebar", "jsxDEV", "_jsxDEV", "MainLayout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/layout/MainLayout.js"], "sourcesContent": ["import React from \"react\";\r\n// import Sidebar from '../components/Sidebar';\r\nimport { Outlet } from \"react-router-dom\";\r\nimport Sidebar from \"../Component/Sidebar\";\r\n\r\nconst MainLayout = () => {\r\n  return (\r\n    <div className=\"d-flex\">\r\n      <Sidebar />\r\n      {/* <div className=\"flex-grow-1 p-4\" style={{ marginLeft: '50px' }}> */}\r\n      <div className=\"flex-grow-1 p-4\" style={{ marginLeft: \"10px\" }}>\r\n        <Outlet />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MainLayout;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,OAAO,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAAKE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBH,OAAA,CAACF,OAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEXP,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAACM,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAN,QAAA,eAC7DH,OAAA,CAACH,MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAVIT,UAAU;AAYhB,eAAeA,UAAU;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}