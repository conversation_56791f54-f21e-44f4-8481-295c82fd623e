import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, <PERSON>, Col, Card, Button, Form, Alert, Spinner, Table, Badge, Accordion } from 'react-bootstrap';
import { FaPhone, FaEnvelope, FaMapMarkerAlt, FaClock, FaTicketAlt, FaQuestionCircle, FaHeadset } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { ticketsAPI, categoriesAPI } from '../services/api';

const ContactSupport = () => {
  const { user } = useAuth();
  const [tickets, setTickets] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    subject: '',
    category: '',
    priority: 'medium',
    description: '',
    attachments: []
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('contact');

  useEffect(() => {
    fetchTickets();
    fetchCategories();
  }, []);

  const fetchTickets = async () => {
    try {
      const response = await ticketsAPI.getTickets();
      if (response.success) {
        setTickets(response.data.tickets || []);
      }
    } catch (error) {
      console.error('Error fetching tickets:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesAPI.getCategories();
      if (response.success) {
        setCategories(response.data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: files
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const ticketData = new FormData();
      ticketData.append('subject', formData.subject);
      ticketData.append('category', formData.category);
      ticketData.append('priority', formData.priority);
      ticketData.append('description', formData.description);
      
      // Append attachments
      formData.attachments.forEach((file) => {
        ticketData.append('attachments', file);
      });

      const response = await ticketsAPI.createTicket(ticketData);
      
      if (response.success) {
        setSuccess('Support ticket submitted successfully! We will get back to you soon.');
        setFormData({
          subject: '',
          category: '',
          priority: 'medium',
          description: '',
          attachments: []
        });
        fetchTickets(); // Refresh tickets list
      } else {
        setError(response.message || 'Failed to submit ticket');
      }
    } catch (error) {
      console.error('Error submitting ticket:', error);
      setError('Failed to submit ticket. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'open': { variant: 'primary', text: 'Open' },
      'in-progress': { variant: 'warning', text: 'In Progress' },
      'resolved': { variant: 'success', text: 'Resolved' },
      'closed': { variant: 'secondary', text: 'Closed' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'low': { variant: 'success', text: 'Low' },
      'medium': { variant: 'warning', text: 'Medium' },
      'high': { variant: 'danger', text: 'High' },
      'urgent': { variant: 'dark', text: 'Urgent' }
    };
    
    const config = priorityConfig[priority] || { variant: 'secondary', text: priority };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const faqData = [
    {
      question: "How do I file an insurance claim?",
      answer: "You can file a claim by visiting the Claims page in your dashboard. Select your policy, provide incident details, and upload supporting documents. Our team will review and process your claim within 3-5 business days."
    },
    {
      question: "How can I update my policy information?",
      answer: "You can update your personal information and policy details through your profile page. For major changes like beneficiary updates, please contact our support team."
    },
    {
      question: "What documents do I need for claim processing?",
      answer: "Required documents vary by claim type but typically include: incident photos, police reports (for theft/accidents), medical bills (for health claims), and any relevant receipts or invoices."
    },
    {
      question: "How long does claim processing take?",
      answer: "Most claims are processed within 7-14 business days. Complex claims may take longer. You'll receive email updates throughout the process."
    },
    {
      question: "Can I cancel my policy?",
      answer: "Yes, you can request policy cancellation by contacting our support team. Please note that cancellation terms and any applicable fees depend on your policy type and duration."
    }
  ];

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <h2 className="mb-1">Contact Support</h2>
          <p className="text-muted">Get help with your insurance needs</p>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Row className="mb-4">
        <Col>
          <div className="d-flex gap-2 mb-3">
            <Button 
              variant={activeTab === 'contact' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('contact')}
            >
              <FaHeadset className="me-2" />
              Contact Info
            </Button>
            <Button 
              variant={activeTab === 'ticket' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('ticket')}
            >
              <FaTicketAlt className="me-2" />
              Submit Ticket
            </Button>
            <Button 
              variant={activeTab === 'tickets' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('tickets')}
            >
              <FaTicketAlt className="me-2" />
              My Tickets
            </Button>
            <Button 
              variant={activeTab === 'faq' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('faq')}
            >
              <FaQuestionCircle className="me-2" />
              FAQ
            </Button>
          </div>
        </Col>
      </Row>

      {activeTab === 'contact' && (
        <Row>
          <Col md={6} className="mb-4">
            <Card className="h-100">
              <Card.Header>
                <h5 className="mb-0">Contact Information</h5>
              </Card.Header>
              <Card.Body>
                <div className="mb-3">
                  <FaPhone className="me-2 text-primary" />
                  <strong>Phone:</strong> +91 98765 43210
                </div>
                <div className="mb-3">
                  <FaEnvelope className="me-2 text-primary" />
                  <strong>Email:</strong> <EMAIL>
                </div>
                <div className="mb-3">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  <strong>Address:</strong><br />
                  Morya Insurance Ltd.<br />
                  123 Business District<br />
                  Mumbai, Maharashtra 400001
                </div>
                <div className="mb-3">
                  <FaClock className="me-2 text-primary" />
                  <strong>Business Hours:</strong><br />
                  Monday - Friday: 9:00 AM - 6:00 PM<br />
                  Saturday: 9:00 AM - 2:00 PM<br />
                  Sunday: Closed
                </div>
              </Card.Body>
            </Card>
          </Col>
          <Col md={6} className="mb-4">
            <Card className="h-100">
              <Card.Header>
                <h5 className="mb-0">Emergency Contact</h5>
              </Card.Header>
              <Card.Body>
                <Alert variant="danger">
                  <strong>24/7 Emergency Helpline</strong><br />
                  <FaPhone className="me-2" />
                  <strong>+91 98765 43211</strong>
                </Alert>
                <p>For urgent claims and emergencies, call our 24/7 helpline. Our emergency response team is available round the clock to assist you.</p>
                
                <h6 className="mt-4">Quick Actions:</h6>
                <div className="d-grid gap-2">
                  <Button variant="outline-primary" onClick={() => setActiveTab('ticket')}>
                    Submit Support Ticket
                  </Button>
                  <Button variant="outline-success" onClick={() => setActiveTab('faq')}>
                    Browse FAQ
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {activeTab === 'ticket' && (
        <Row>
          <Col lg={8} className="mx-auto">
            <Card>
              <Card.Header>
                <h5 className="mb-0">Submit Support Ticket</h5>
              </Card.Header>
              <Card.Body>
                {error && <Alert variant="danger">{error}</Alert>}
                
                <Form onSubmit={handleSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Subject *</Form.Label>
                        <Form.Control
                          type="text"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="Brief description of your issue"
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Category *</Form.Label>
                        <Form.Select
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                          required
                        >
                          <option value="">Select category...</option>
                          <option value="technical">Technical Issue</option>
                          <option value="billing">Billing & Payments</option>
                          <option value="claims">Claims Support</option>
                          <option value="policy">Policy Information</option>
                          <option value="general">General Inquiry</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Priority</Form.Label>
                    <Form.Select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </Form.Select>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Description *</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={5}
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Please provide detailed information about your issue..."
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Attachments</Form.Label>
                    <Form.Control
                      type="file"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                      onChange={handleFileChange}
                    />
                    <Form.Text className="text-muted">
                      Upload screenshots, documents, or other relevant files (PDF, JPG, PNG, DOC)
                    </Form.Text>
                  </Form.Group>

                  <div className="d-grid">
                    <Button 
                      type="submit" 
                      variant="primary" 
                      size="lg"
                      disabled={submitting}
                    >
                      {submitting ? (
                        <>
                          <Spinner animation="border" size="sm" className="me-2" />
                          Submitting...
                        </>
                      ) : (
                        'Submit Ticket'
                      )}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {activeTab === 'tickets' && (
        <Row>
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">My Support Tickets</h5>
              </Card.Header>
              <Card.Body>
                {loading ? (
                  <div className="text-center py-4">
                    <Spinner animation="border" />
                    <p className="mt-2">Loading tickets...</p>
                  </div>
                ) : tickets.length === 0 ? (
                  <div className="text-center py-4">
                    <FaTicketAlt size={48} className="text-muted mb-3" />
                    <h5>No Tickets Found</h5>
                    <p className="text-muted">You haven't submitted any support tickets yet.</p>
                    <Button variant="primary" onClick={() => setActiveTab('ticket')}>
                      Submit Your First Ticket
                    </Button>
                  </div>
                ) : (
                  <Table responsive hover>
                    <thead>
                      <tr>
                        <th>Ticket ID</th>
                        <th>Subject</th>
                        <th>Category</th>
                        <th>Priority</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Last Updated</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tickets.map((ticket) => (
                        <tr key={ticket._id}>
                          <td>
                            <strong>#{ticket.ticketId}</strong>
                          </td>
                          <td>{ticket.subject}</td>
                          <td>
                            <Badge bg="light" text="dark">
                              {ticket.category}
                            </Badge>
                          </td>
                          <td>{getPriorityBadge(ticket.priority)}</td>
                          <td>{getStatusBadge(ticket.status)}</td>
                          <td>{formatDate(ticket.createdAt)}</td>
                          <td>{formatDate(ticket.updatedAt)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {activeTab === 'faq' && (
        <Row>
          <Col lg={10} className="mx-auto">
            <Card>
              <Card.Header>
                <h5 className="mb-0">Frequently Asked Questions</h5>
              </Card.Header>
              <Card.Body>
                <Accordion>
                  {faqData.map((faq, index) => (
                    <Accordion.Item eventKey={index.toString()} key={index}>
                      <Accordion.Header>{faq.question}</Accordion.Header>
                      <Accordion.Body>{faq.answer}</Accordion.Body>
                    </Accordion.Item>
                  ))}
                </Accordion>
                
                <div className="mt-4 text-center">
                  <p className="text-muted">Can't find what you're looking for?</p>
                  <Button variant="primary" onClick={() => setActiveTab('ticket')}>
                    Submit a Support Ticket
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default ContactSupport;
