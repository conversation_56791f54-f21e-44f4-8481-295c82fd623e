{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Component\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { OverlayTrigger, Tooltip } from 'react-bootstrap';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  collapsed = false\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n\n  // Define menu items with role-based access\n  const allMenuItems = [{\n    to: '/dashboard',\n    label: 'Dashboard',\n    icon: 'fas fa-chart-line',\n    roles: ['admin', 'employee', 'customer']\n  },\n  // Admin only\n  {\n    to: '/categories',\n    label: 'Categories',\n    icon: 'fas fa-folder',\n    roles: ['admin']\n  }, {\n    to: '/subcategories',\n    label: 'SubCategories',\n    icon: 'fas fa-folder-open',\n    roles: ['admin']\n  }, {\n    to: '/ticketcategories',\n    label: 'Ticket Categories',\n    icon: 'fas fa-ticket-alt',\n    roles: ['admin']\n  }, {\n    to: '/staff',\n    label: 'Staff',\n    icon: 'fas fa-user-tie',\n    roles: ['admin']\n  }, {\n    to: '/users',\n    label: 'Users',\n    icon: 'fas fa-users',\n    roles: ['admin']\n  }, {\n    to: '/system-settings',\n    label: 'System Settings',\n    icon: 'fas fa-cogs',\n    roles: ['admin']\n  },\n  // Admin and Employee\n  {\n    to: '/insurance-policy',\n    label: 'Insurance Policy',\n    icon: 'fas fa-file-signature',\n    roles: ['admin', 'employee']\n  }, {\n    to: '/policy-holder',\n    label: 'Policy Holder',\n    icon: 'fas fa-id-card',\n    roles: ['admin', 'employee']\n  }, {\n    to: '/support-ticket',\n    label: 'Support Ticket',\n    icon: 'fas fa-headset',\n    roles: ['admin', 'employee']\n  }, {\n    to: '/report-tool',\n    label: 'Report Tool',\n    icon: 'fas fa-chart-bar',\n    roles: ['admin', 'employee']\n  },\n  // Task Management\n  {\n    to: '/task-management',\n    label: 'Task Management',\n    icon: 'fas fa-tasks',\n    roles: ['admin']\n  }, {\n    to: '/my-tasks',\n    label: 'My Tasks',\n    icon: 'fas fa-clipboard-list',\n    roles: ['employee']\n  },\n  // Customer only\n  {\n    to: '/claims',\n    label: 'My Claims',\n    icon: 'fas fa-file-medical',\n    roles: ['customer']\n  }, {\n    to: '/contact-support',\n    label: 'Contact Support',\n    icon: 'fas fa-life-ring',\n    roles: ['customer']\n  }];\n\n  // Filter menu items based on user role\n  const menu = allMenuItems.filter(item => {\n    if (!user) return false;\n    return item.roles.includes(user.role);\n  });\n  const renderMenuItem = (item, idx) => {\n    const linkContent = /*#__PURE__*/_jsxDEV(NavLink, {\n      to: item.to,\n      className: ({\n        isActive\n      }) => `text-white py-2 px-3 sidebar-link d-block text-decoration-none ${isActive ? 'active-link bg-light bg-opacity-25 rounded' : ''}`,\n      style: {\n        transition: 'all 0.3s ease',\n        whiteSpace: 'nowrap',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `${item.icon} ${collapsed ? '' : 'me-2'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 24\n      }, this)]\n    }, idx, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n    if (collapsed) {\n      return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n        placement: \"right\",\n        overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 20\n        }, this),\n        children: linkContent\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this);\n    }\n    return linkContent;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar d-flex flex-column bg-primary text-white position-fixed\",\n    style: {\n      width: collapsed ? '70px' : '260px',\n      height: '100vh',\n      top: '56px',\n      // Navbar height\n      left: 0,\n      transition: 'width 0.3s ease',\n      zIndex: 999,\n      overflowX: 'hidden',\n      overflowY: 'auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1\",\n      children: menu.map((item, idx) => renderMenuItem(item, idx))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "OverlayTrigger", "<PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Sidebar", "collapsed", "_s", "user", "allMenuItems", "to", "label", "icon", "roles", "menu", "filter", "item", "includes", "role", "renderMenuItem", "idx", "linkContent", "className", "isActive", "style", "transition", "whiteSpace", "overflow", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "overlay", "width", "height", "top", "left", "zIndex", "overflowX", "overflowY", "map", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Component/Sidebar.js"], "sourcesContent": ["import React from 'react';\r\nimport { NavLink } from 'react-router-dom';\r\nimport { OverlayTrigger, Tooltip } from 'react-bootstrap';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nconst Sidebar = ({ collapsed = false }) => {\r\n  const { user } = useAuth();\r\n\r\n  // Define menu items with role-based access\r\n  const allMenuItems = [\r\n    { to: '/dashboard', label: 'Dashboard', icon: 'fas fa-chart-line', roles: ['admin', 'employee', 'customer'] },\r\n   // Admin only\r\n    { to: '/categories', label: 'Categories', icon: 'fas fa-folder', roles: ['admin'] },\r\n    { to: '/subcategories', label: 'SubCategories', icon: 'fas fa-folder-open', roles: ['admin'] },\r\n    { to: '/ticketcategories', label: 'Ticket Categories', icon: 'fas fa-ticket-alt', roles: ['admin'] },\r\n    { to: '/staff', label: 'Staff', icon: 'fas fa-user-tie', roles: ['admin'] },\r\n    { to: '/users', label: 'Users', icon: 'fas fa-users', roles: ['admin'] },\r\n    { to: '/system-settings', label: 'System Settings', icon: 'fas fa-cogs', roles: ['admin'] },\r\n\r\n    // Admin and Employee\r\n    { to: '/insurance-policy', label: 'Insurance Policy', icon: 'fas fa-file-signature', roles: ['admin', 'employee'] },\r\n    { to: '/policy-holder', label: 'Policy Holder', icon: 'fas fa-id-card', roles: ['admin', 'employee'] },\r\n    { to: '/support-ticket', label: 'Support Ticket', icon: 'fas fa-headset', roles: ['admin', 'employee'] },\r\n    { to: '/report-tool', label: 'Report Tool', icon: 'fas fa-chart-bar', roles: ['admin', 'employee'] },\r\n\r\n    // Task Management\r\n    { to: '/task-management', label: 'Task Management', icon: 'fas fa-tasks', roles: ['admin'] },\r\n    { to: '/my-tasks', label: 'My Tasks', icon: 'fas fa-clipboard-list', roles: ['employee'] },\r\n\r\n    // Customer only\r\n    { to: '/claims', label: 'My Claims', icon: 'fas fa-file-medical', roles: ['customer'] },\r\n    { to: '/contact-support', label: 'Contact Support', icon: 'fas fa-life-ring', roles: ['customer'] },\r\n  ];\r\n\r\n  // Filter menu items based on user role\r\n  const menu = allMenuItems.filter(item => {\r\n    if (!user) return false;\r\n    return item.roles.includes(user.role);\r\n  });\r\n\r\n  const renderMenuItem = (item, idx) => {\r\n    const linkContent = (\r\n      <NavLink\r\n        key={idx}\r\n        to={item.to}\r\n        className={({ isActive }) =>\r\n          `text-white py-2 px-3 sidebar-link d-block text-decoration-none ${\r\n            isActive ? 'active-link bg-light bg-opacity-25 rounded' : ''\r\n          }`\r\n        }\r\n        style={{\r\n          transition: 'all 0.3s ease',\r\n          whiteSpace: 'nowrap',\r\n          overflow: 'hidden'\r\n        }}\r\n      >\r\n        <i className={`${item.icon} ${collapsed ? '' : 'me-2'}`}></i>\r\n        {!collapsed && <span>{item.label}</span>}\r\n      </NavLink>\r\n    );\r\n\r\n    if (collapsed) {\r\n      return (\r\n        <OverlayTrigger\r\n          key={idx}\r\n          placement=\"right\"\r\n          overlay={<Tooltip>{item.label}</Tooltip>}\r\n        >\r\n          {linkContent}\r\n        </OverlayTrigger>\r\n      );\r\n    }\r\n\r\n    return linkContent;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"sidebar d-flex flex-column bg-primary text-white position-fixed\"\r\n      style={{\r\n        width: collapsed ? '70px' : '260px',\r\n        height: '100vh',\r\n        top: '56px', // Navbar height\r\n        left: 0,\r\n        transition: 'width 0.3s ease',\r\n        zIndex: 999,\r\n        overflowX: 'hidden',\r\n        overflowY: 'auto'\r\n      }}\r\n    >\r\n      <div className=\"flex-grow-1\">\r\n        {menu.map((item, idx) => renderMenuItem(item, idx))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,cAAc,EAAEC,OAAO,QAAQ,iBAAiB;AACzD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,OAAO,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMO,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU;EAAE,CAAC;EAC9G;EACC;IAAEH,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACnF;IAAEH,EAAE,EAAE,gBAAgB;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC9F;IAAEH,EAAE,EAAE,mBAAmB;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACpG;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC3E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EACxE;IAAEH,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC;EAE3F;EACA;IAAEH,EAAE,EAAE,mBAAmB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;EAAE,CAAC,EACnH;IAAEH,EAAE,EAAE,gBAAgB;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;EAAE,CAAC,EACtG;IAAEH,EAAE,EAAE,iBAAiB;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;EAAE,CAAC,EACxG;IAAEH,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU;EAAE,CAAC;EAEpG;EACA;IAAEH,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,CAAC,OAAO;EAAE,CAAC,EAC5F;IAAEH,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,KAAK,EAAE,CAAC,UAAU;EAAE,CAAC;EAE1F;EACA;IAAEH,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE,CAAC,UAAU;EAAE,CAAC,EACvF;IAAEH,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE,CAAC,UAAU;EAAE,CAAC,CACpG;;EAED;EACA,MAAMC,IAAI,GAAGL,YAAY,CAACM,MAAM,CAACC,IAAI,IAAI;IACvC,IAAI,CAACR,IAAI,EAAE,OAAO,KAAK;IACvB,OAAOQ,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACT,IAAI,CAACU,IAAI,CAAC;EACvC,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAGA,CAACH,IAAI,EAAEI,GAAG,KAAK;IACpC,MAAMC,WAAW,gBACfjB,OAAA,CAACL,OAAO;MAENW,EAAE,EAAEM,IAAI,CAACN,EAAG;MACZY,SAAS,EAAEA,CAAC;QAAEC;MAAS,CAAC,KACtB,kEACEA,QAAQ,GAAG,4CAA4C,GAAG,EAAE,EAE/D;MACDC,KAAK,EAAE;QACLC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,gBAEFxB,OAAA;QAAGkB,SAAS,EAAE,GAAGN,IAAI,CAACJ,IAAI,IAAIN,SAAS,GAAG,EAAE,GAAG,MAAM;MAAG;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC5D,CAAC1B,SAAS,iBAAIF,OAAA;QAAAwB,QAAA,EAAOZ,IAAI,CAACL;MAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA,GAdnCZ,GAAG;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeD,CACV;IAED,IAAI1B,SAAS,EAAE;MACb,oBACEF,OAAA,CAACJ,cAAc;QAEbiC,SAAS,EAAC,OAAO;QACjBC,OAAO,eAAE9B,OAAA,CAACH,OAAO;UAAA2B,QAAA,EAAEZ,IAAI,CAACL;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAE;QAAAJ,QAAA,EAExCP;MAAW,GAJPD,GAAG;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKM,CAAC;IAErB;IAEA,OAAOX,WAAW;EACpB,CAAC;EAED,oBACEjB,OAAA;IACEkB,SAAS,EAAC,iEAAiE;IAC3EE,KAAK,EAAE;MACLW,KAAK,EAAE7B,SAAS,GAAG,MAAM,GAAG,OAAO;MACnC8B,MAAM,EAAE,OAAO;MACfC,GAAG,EAAE,MAAM;MAAE;MACbC,IAAI,EAAE,CAAC;MACPb,UAAU,EAAE,iBAAiB;MAC7Bc,MAAM,EAAE,GAAG;MACXC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAE;IAAAb,QAAA,eAEFxB,OAAA;MAAKkB,SAAS,EAAC,aAAa;MAAAM,QAAA,EACzBd,IAAI,CAAC4B,GAAG,CAAC,CAAC1B,IAAI,EAAEI,GAAG,KAAKD,cAAc,CAACH,IAAI,EAAEI,GAAG,CAAC;IAAC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CA1FIF,OAAO;EAAA,QACMH,OAAO;AAAA;AAAAyC,EAAA,GADpBtC,OAAO;AA4Fb,eAAeA,OAAO;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}