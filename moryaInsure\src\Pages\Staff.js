import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form, Badge, InputGroup } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaUserTie } from 'react-icons/fa';
import { userAPI } from '../services/api';

const Staff = () => {
  // State for staff data
  const [staff, setStaff] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Form state
  const [staffForm, setStaffForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: 'employee',
    phone: ''
  });

  // Fetch staff on component mount
  useEffect(() => {
    fetchStaff();
  }, []);

  // Fetch all staff (employees and admins)
  const fetchStaff = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getUsers({ role: 'employee', limit: 100 });
      if (response.success) {
        setStaff(response.data.users);
      }
    } catch (error) {
      console.error('Error fetching staff:', error);
      alert('Error fetching staff. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleFormChange = (e) => {
    setStaffForm({
      ...staffForm,
      [e.target.name]: e.target.value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (selectedStaff) {
        // Update existing staff
        await userAPI.updateUser(selectedStaff._id, staffForm);
        alert('Staff member updated successfully!');
      } else {
        // Create new staff
        await userAPI.createUser(staffForm);
        alert('Staff member created successfully!');
      }
      setShowModal(false);
      resetForm();
      fetchStaff();
    } catch (error) {
      console.error('Error saving staff:', error);
      alert('Error saving staff member. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setStaffForm({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      role: 'employee',
      phone: ''
    });
    setSelectedStaff(null);
  };

  // Handle edit staff
  const handleEdit = (staffMember) => {
    setSelectedStaff(staffMember);
    setStaffForm({
      firstName: staffMember.firstName,
      lastName: staffMember.lastName,
      email: staffMember.email,
      password: '',
      role: staffMember.role,
      phone: staffMember.phone || ''
    });
    setShowModal(true);
  };

  // Handle delete staff
  const handleDelete = async (staffId) => {
    if (window.confirm('Are you sure you want to delete this staff member?')) {
      try {
        await userAPI.deleteUser(staffId);
        alert('Staff member deleted successfully!');
        fetchStaff();
      } catch (error) {
        console.error('Error deleting staff:', error);
        alert('Error deleting staff member. Please try again.');
      }
    }
  };

  // Handle add new staff
  const handleAddNew = () => {
    resetForm();
    setShowModal(true);
  };

  // Filter staff based on search term
  const filteredStaff = staff.filter(member =>
    member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <h2 className="text-info mb-0">Staff Management</h2>
          <p className="text-muted">Manage employees and administrators</p>
        </Col>
      </Row>

      {/* Action Bar */}
      <Row className="mb-4">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text>
              <FaSearch />
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search staff by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={6} className="text-end">
          <Button
            variant="info"
            onClick={handleAddNew}
          >
            <FaPlus className="me-2" />
            Add Staff Member
          </Button>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={4}>
          <Card className="bg-info text-white">
            <Card.Body>
              <div className="d-flex justify-content-between">
                <div>
                  <h6>Total Staff</h6>
                  <h4>{staff.length}</h4>
                </div>
                <FaUserTie className="fs-2" />
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="bg-success text-white">
            <Card.Body>
              <div className="d-flex justify-content-between">
                <div>
                  <h6>Active Staff</h6>
                  <h4>{staff.filter(s => s.isActive).length}</h4>
                </div>
                <FaEye className="fs-2" />
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col md={4}>
          <Card className="bg-warning text-white">
            <Card.Body>
              <div className="d-flex justify-content-between">
                <div>
                  <h6>Admins</h6>
                  <h4>{staff.filter(s => s.role === 'admin').length}</h4>
                </div>
                <FaEye className="fs-2" />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Staff Table */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">All Staff Members ({filteredStaff.length})</h5>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <div className="spinner-border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              ) : (
                <Table responsive striped hover>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Phone</th>
                      <th>Status</th>
                      <th>Joined</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredStaff.length > 0 ? filteredStaff.map((member) => (
                      <tr key={member._id}>
                        <td>
                          <div>
                            <strong>{member.firstName} {member.lastName}</strong>
                            {member.isEmailVerified && (
                              <i className="bi bi-patch-check-fill text-success ms-1" title="Email Verified"></i>
                            )}
                            <br />
                            <small className="text-muted">
                              {member.role === 'admin' ? 'Administrator' : 'Employee'}
                            </small>
                          </div>
                        </td>
                        <td>{member.email}</td>
                        <td>
                          <Badge bg={member.role === 'admin' ? 'danger' : 'info'}>
                            {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                          </Badge>
                        </td>
                        <td>{member.phone || <span className="text-muted">Not provided</span>}</td>
                        <td>
                          <Badge bg={member.isActive ? 'success' : 'danger'}>
                            {member.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </td>
                        <td>
                          <div>
                            {new Date(member.createdAt).toLocaleDateString()}
                            <br />
                            <small className="text-muted">
                              {new Date(member.createdAt).toLocaleTimeString()}
                            </small>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              size="sm"
                              variant="outline-primary"
                              onClick={() => handleEdit(member)}
                              title="Edit Staff"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-danger"
                              onClick={() => handleDelete(member._id)}
                              title="Delete Staff"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan="7" className="text-center py-4">
                          <i className="bi bi-person-workspace text-muted fs-1 mb-2 d-block"></i>
                          <p className="text-muted mb-0">No staff members found</p>
                          <small className="text-muted">
                            {searchTerm ? 'Try adjusting your search terms' : 'Add your first staff member to get started'}
                          </small>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Staff Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedStaff ? 'Edit Staff Member' : 'Add New Staff Member'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="firstName"
                    value={staffForm.firstName}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="lastName"
                    value={staffForm.lastName}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={staffForm.email}
                    onChange={handleFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={staffForm.phone}
                    onChange={handleFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Role</Form.Label>
                  <Form.Select
                    name="role"
                    value={staffForm.role}
                    onChange={handleFormChange}
                    required
                  >
                    <option value="employee">Employee</option>
                    <option value="admin">Administrator</option>
                  </Form.Select>
                  <Form.Text className="text-muted">
                    {staffForm.role === 'employee' && 'Employee will have limited access to assigned work'}
                    {staffForm.role === 'admin' && 'Administrator will have full system access'}
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Password {selectedStaff && '(leave blank to keep current)'}</Form.Label>
                  <Form.Control
                    type="password"
                    name="password"
                    value={staffForm.password}
                    onChange={handleFormChange}
                    required={!selectedStaff}
                  />
                </Form.Group>
              </Col>
            </Row>
            <div className="alert alert-info">
              <small>
                <strong>Note:</strong> Staff members will be able to access the employee dashboard and manage assigned work.
                Administrators will have full system access including user management.
              </small>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button variant="info" type="submit" disabled={loading}>
              {loading ? 'Saving...' : selectedStaff ? 'Update Staff' : 'Create Staff'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default Staff;