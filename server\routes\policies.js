const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Policy = require('../models/Policy');
const User = require('../models/User');
const Category = require('../models/Category');
const { authenticate, adminOrEmployee, authorize, ownerOrStaff } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/policies
// @desc    Get all policies (with role-based filtering)
// @access  Private
router.get('/', authenticate, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['draft', 'pending', 'approved', 'active', 'suspended', 'cancelled', 'expired']).withMessage('Invalid status'),
  query('type').optional().isIn(['life', 'health', 'auto', 'home', 'business', 'travel']).withMessage('Invalid type'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('category').optional().isMongoId().withMessage('Invalid category ID'),
  query('subCategory').optional().isMongoId().withMessage('Invalid subcategory ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query = {};
    
    // Customers can only see their own policies
    if (req.user.role === 'customer') {
      query.policyHolder = req.user._id;
    }
    
    // Filter by status if provided
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Filter by type if provided
    if (req.query.type) {
      query.type = req.query.type;
    }

    // Filter by assigned agent for employees
    if (req.user.role === 'employee' && req.query.assigned === 'me') {
      query.assignedAgent = req.user._id;
    }

    // Filter by category if provided
    if (req.query.category) {
      query.category = req.query.category;
    }

    // Filter by subcategory if provided
    if (req.query.subCategory) {
      query.subCategory = req.query.subCategory;
    }

    // Search functionality
    if (req.query.search) {
      query.$or = [
        { policyNumber: { $regex: req.query.search, $options: 'i' } },
        { type: { $regex: req.query.search, $options: 'i' } },
        { description: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    const policies = await Policy.find(query)
      .populate('policyHolder', 'firstName lastName email phone')
      .populate('assignedAgent', 'firstName lastName email')
      .populate('category', 'name type')
      .populate('subCategory', 'name')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Policy.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        policies,
        pagination: {
          currentPage: page,
          totalPages,
          totalPolicies: total,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get policies error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching policies'
    });
  }
});

// @route   GET /api/policies/:id
// @desc    Get policy by ID
// @access  Private (Owner, Admin, or assigned Employee)
router.get('/:id', authenticate, async (req, res) => {
  try {
    const policy = await Policy.findById(req.params.id)
      .populate('policyHolder', 'firstName lastName email phone address')
      .populate('assignedAgent', 'firstName lastName email')
      .populate('approvedBy', 'firstName lastName email')
      .populate('category', 'name description');

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }

    // Check access permissions
    const canAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'employee' && policy.assignedAgent && policy.assignedAgent._id.toString() === req.user._id.toString()) ||
      (req.user.role === 'customer' && policy.policyHolder._id.toString() === req.user._id.toString());

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only view policies you are authorized to see.'
      });
    }

    res.json({
      success: true,
      data: {
        policy
      }
    });
  } catch (error) {
    console.error('Get policy error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching policy'
    });
  }
});

// @route   POST /api/policies
// @desc    Create new policy
// @access  Private (Admin/Employee)
router.post('/', authenticate, adminOrEmployee, [
  body('policyHolder').isMongoId().withMessage('Valid policy holder ID is required'),
  body('type').isIn(['life', 'health', 'auto', 'home', 'business', 'travel']).withMessage('Invalid policy type'),
  body('category').isMongoId().withMessage('Valid category ID is required'),
  body('coverageAmount').isFloat({ min: 0 }).withMessage('Coverage amount must be a positive number'),
  body('premiumAmount').isFloat({ min: 0 }).withMessage('Premium amount must be a positive number'),
  body('premiumFrequency').isIn(['monthly', 'quarterly', 'semi-annual', 'annual']).withMessage('Invalid premium frequency'),
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('endDate').isISO8601().withMessage('Valid end date is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      policyHolder,
      type,
      category,
      coverageAmount,
      premiumAmount,
      premiumFrequency,
      startDate,
      endDate,
      beneficiaries,
      terms
    } = req.body;

    // Verify policy holder exists
    const holder = await User.findById(policyHolder);
    if (!holder) {
      return res.status(404).json({
        success: false,
        message: 'Policy holder not found'
      });
    }

    // Verify category exists
    const categoryDoc = await Category.findById(category);
    if (!categoryDoc) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Validate dates
    if (new Date(startDate) >= new Date(endDate)) {
      return res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
    }

    const policy = new Policy({
      policyHolder,
      type,
      category,
      coverageAmount,
      premiumAmount,
      premiumFrequency,
      startDate,
      endDate,
      beneficiaries,
      terms,
      assignedAgent: req.user._id,
      status: 'pending'
    });

    await policy.save();

    const populatedPolicy = await Policy.findById(policy._id)
      .populate('policyHolder', 'firstName lastName email')
      .populate('assignedAgent', 'firstName lastName email')
      .populate('category', 'name');

    res.status(201).json({
      success: true,
      message: 'Policy created successfully',
      data: {
        policy: populatedPolicy
      }
    });
  } catch (error) {
    console.error('Create policy error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating policy'
    });
  }
});

// @route   PUT /api/policies/:id
// @desc    Update policy
// @access  Private (Admin or assigned Employee)
router.put('/:id', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const policy = await Policy.findById(req.params.id);
    
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }

    // Check if user can update this policy
    const canUpdate = 
      req.user.role === 'admin' ||
      (req.user.role === 'employee' && policy.assignedAgent && policy.assignedAgent.toString() === req.user._id.toString());

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only update policies assigned to you.'
      });
    }

    // Define allowed updates
    const allowedUpdates = [
      'coverageAmount', 'premiumAmount', 'premiumFrequency', 
      'endDate', 'beneficiaries', 'terms', 'notes'
    ];

    // Only admins can update certain fields
    if (req.user.role === 'admin') {
      allowedUpdates.push('status', 'assignedAgent', 'type', 'category');
    }

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    // Add approval info if status is being changed to approved
    if (updates.status === 'approved' && policy.status !== 'approved') {
      updates.approvedBy = req.user._id;
      updates.approvalDate = new Date();
    }

    const updatedPolicy = await Policy.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate('policyHolder assignedAgent approvedBy category');

    res.json({
      success: true,
      message: 'Policy updated successfully',
      data: {
        policy: updatedPolicy
      }
    });
  } catch (error) {
    console.error('Update policy error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating policy'
    });
  }
});

// @route   PUT /api/policies/:id/status
// @desc    Update policy status
// @access  Private (Admin or assigned Employee)
router.put('/:id/status', authenticate, adminOrEmployee, [
  body('status').isIn(['draft', 'pending', 'approved', 'active', 'suspended', 'cancelled', 'expired']).withMessage('Invalid status'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes } = req.body;
    const policy = await Policy.findById(req.params.id);
    
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }

    // Check permissions
    const canUpdate = 
      req.user.role === 'admin' ||
      (req.user.role === 'employee' && policy.assignedAgent && policy.assignedAgent.toString() === req.user._id.toString());

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    policy.status = status;
    
    if (status === 'approved' && policy.status !== 'approved') {
      policy.approvedBy = req.user._id;
      policy.approvalDate = new Date();
    }

    if (notes) {
      policy.notes.push({
        content: notes,
        addedBy: req.user._id
      });
    }

    await policy.save();

    const updatedPolicy = await Policy.findById(policy._id)
      .populate('policyHolder assignedAgent approvedBy category');

    res.json({
      success: true,
      message: 'Policy status updated successfully',
      data: {
        policy: updatedPolicy
      }
    });
  } catch (error) {
    console.error('Update policy status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating policy status'
    });
  }
});

module.exports = router;
