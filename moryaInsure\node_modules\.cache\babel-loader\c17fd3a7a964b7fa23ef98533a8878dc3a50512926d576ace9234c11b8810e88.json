{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\InsurancePolicy.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InsurancePolicy = () => {\n  _s();\n  const [showModal, setShowModal] = useState(false);\n  const [search, setSearch] = useState('');\n  const [policies, setPolicies] = useState([\n    // {\n    //   id: 1,\n    //   name: 'AutoSecure Collision Plan',\n    //   category: 'Auto Insurance',\n    //   subCategory: 'Comprehensive Coverage',\n    //   sumAssured: '80000',\n    //   premium: '8000',\n    //   tenure: '36',\n    //   status: 'Active'\n    // },\n    // {\n    //   id: 2,\n    //   name: 'FamilyShield Term Plan',\n    //   category: 'Life Insurance',\n    //   subCategory: 'Term Life Insurance',\n    //   sumAssured: '150000',\n    //   premium: '20000',\n    //   tenure: '24',\n    //   status: 'Pending'\n    // }\n  ]);\n  const [form, setForm] = useState({\n    name: '',\n    category: '',\n    subCategory: '',\n    sumAssured: '',\n    premium: '',\n    tenure: ''\n  });\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSave = () => {\n    const newPolicy = {\n      ...form,\n      id: policies.length + 1,\n      status: 'Active'\n    };\n    setPolicies([...policies, newPolicy]);\n    setShowModal(false);\n    setForm({\n      name: '',\n      category: '',\n      subCategory: '',\n      sumAssured: '',\n      premium: '',\n      tenure: ''\n    });\n  };\n  const filteredPolicies = policies.filter(p => p.name.toLowerCase().includes(search.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Insurance Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search policies...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sub Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Sum Assured\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Premium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Tenure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredPolicies.map(p => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: p.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: p.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: p.subCategory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [p.sumAssured, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [p.premium, \" PHP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [p.tenure, \" months\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge bg-${p.status === 'Active' ? 'success' : 'warning'}`,\n              children: p.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)]\n        }, p.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"New Insurance Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Policy Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"name\",\n              value: form.name,\n              onChange: handleChange,\n              placeholder: \"Enter Policy Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"category\",\n                value: form.category,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sub Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"subCategory\",\n                value: form.subCategory,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Sum Assured (PHP)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                name: \"sumAssured\",\n                value: form.sumAssured,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Premium (PHP)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                name: \"premium\",\n                value: form.premium,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Tenure (months)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"number\",\n              name: \"tenure\",\n              value: form.tenure,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(InsurancePolicy, \"mo1+uJZE7rfouSgjCIHIiKlvNH4=\");\n_c = InsurancePolicy;\nexport default InsurancePolicy;\nvar _c;\n$RefreshReg$(_c, \"InsurancePolicy\");", "map": {"version": 3, "names": ["React", "useState", "Table", "<PERSON><PERSON>", "Modal", "Form", "Row", "Col", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "InsurancePolicy", "_s", "showModal", "setShowModal", "search", "setSearch", "policies", "setPolicies", "form", "setForm", "name", "category", "subCategory", "sumAssured", "premium", "tenure", "handleChange", "e", "target", "value", "handleSave", "newPolicy", "id", "length", "status", "filteredPolicies", "filter", "p", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/InsurancePolicy.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';\r\nimport { FaEdit, FaTrash } from 'react-icons/fa';\r\n\r\nconst InsurancePolicy = () => {\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [search, setSearch] = useState('');\r\n\r\n  const [policies, setPolicies] = useState([\r\n    // {\r\n    //   id: 1,\r\n    //   name: 'AutoSecure Collision Plan',\r\n    //   category: 'Auto Insurance',\r\n    //   subCategory: 'Comprehensive Coverage',\r\n    //   sumAssured: '80000',\r\n    //   premium: '8000',\r\n    //   tenure: '36',\r\n    //   status: 'Active'\r\n    // },\r\n    // {\r\n    //   id: 2,\r\n    //   name: 'FamilyShield Term Plan',\r\n    //   category: 'Life Insurance',\r\n    //   subCategory: 'Term Life Insurance',\r\n    //   sumAssured: '150000',\r\n    //   premium: '20000',\r\n    //   tenure: '24',\r\n    //   status: 'Pending'\r\n    // }\r\n  ]);\r\n\r\n  const [form, setForm] = useState({\r\n    name: '',\r\n    category: '',\r\n    subCategory: '',\r\n    sumAssured: '',\r\n    premium: '',\r\n    tenure: ''\r\n  });\r\n\r\n  const handleChange = (e) => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSave = () => {\r\n    const newPolicy = {\r\n      ...form,\r\n      id: policies.length + 1,\r\n      status: 'Active'\r\n    };\r\n    setPolicies([...policies, newPolicy]);\r\n    setShowModal(false);\r\n    setForm({ name: '', category: '', subCategory: '', sumAssured: '', premium: '', tenure: '' });\r\n  };\r\n\r\n  const filteredPolicies = policies.filter((p) =>\r\n    p.name.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h4 className=\"fw-bold text-uppercase\">Insurance Policy</h4>\r\n        <Button variant=\"primary\" onClick={() => setShowModal(true)}>+ New</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search policies...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Name</th>\r\n            <th>Category</th>\r\n            <th>Sub Category</th>\r\n            <th>Sum Assured</th>\r\n            <th>Premium</th>\r\n            <th>Tenure</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredPolicies.map((p) => (\r\n            <tr key={p.id}>\r\n              <td>{p.name}</td>\r\n              <td>{p.category}</td>\r\n              <td>{p.subCategory}</td>\r\n              <td>{p.sumAssured} PHP</td>\r\n              <td>{p.premium} PHP</td>\r\n              <td>{p.tenure} months</td>\r\n              <td><span className={`badge bg-${p.status === 'Active' ? 'success' : 'warning'}`}>{p.status}</span></td>\r\n              <td>\r\n                <Button variant=\"primary\" size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </Table>\r\n\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>New Insurance Policy</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Policy Name</Form.Label>\r\n              <Form.Control\r\n                name=\"name\"\r\n                value={form.name}\r\n                onChange={handleChange}\r\n                placeholder=\"Enter Policy Name\"\r\n              />\r\n            </Form.Group>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Category</Form.Label>\r\n                <Form.Select name=\"category\" value={form.category} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Auto Insurance</option>\r\n                  <option>Life Insurance</option>\r\n                  <option>Travel Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Sub Category</Form.Label>\r\n                <Form.Select name=\"subCategory\" value={form.subCategory} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Comprehensive Coverage</option>\r\n                  <option>Term Life Insurance</option>\r\n                  <option>Travel Cancellation Insurance</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"mb-3\">\r\n              <Col>\r\n                <Form.Label>Sum Assured (PHP)</Form.Label>\r\n                <Form.Control\r\n                  type=\"number\"\r\n                  name=\"sumAssured\"\r\n                  value={form.sumAssured}\r\n                  onChange={handleChange}\r\n                />\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Premium (PHP)</Form.Label>\r\n                <Form.Control\r\n                  type=\"number\"\r\n                  name=\"premium\"\r\n                  value={form.premium}\r\n                  onChange={handleChange}\r\n                />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Tenure (months)</Form.Label>\r\n              <Form.Control\r\n                type=\"number\"\r\n                name=\"tenure\"\r\n                value={form.tenure}\r\n                onChange={handleChange}\r\n              />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={handleSave}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InsurancePolicy;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACtE,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD,CAAC;EAEF,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC;IAC/BqB,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1BR,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAACS,CAAC,CAACC,MAAM,CAACR,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACC;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAG;MAChB,GAAGb,IAAI;MACPc,EAAE,EAAEhB,QAAQ,CAACiB,MAAM,GAAG,CAAC;MACvBC,MAAM,EAAE;IACV,CAAC;IACDjB,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEe,SAAS,CAAC,CAAC;IACrClB,YAAY,CAAC,KAAK,CAAC;IACnBM,OAAO,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC;EAC/F,CAAC;EAED,MAAMU,gBAAgB,GAAGnB,QAAQ,CAACoB,MAAM,CAAEC,CAAC,IACzCA,CAAC,CAACjB,IAAI,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzB,MAAM,CAACwB,WAAW,CAAC,CAAC,CACpD,CAAC;EAED,oBACE7B,OAAA;IAAK+B,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClChC,OAAA;MAAK+B,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrEhC,OAAA;QAAI+B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5DpC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,IAAI,CAAE;QAAA4B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1ChC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DpC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DpC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DpC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DpC,OAAA,CAACR,MAAM;QAAC6C,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBhC,OAAA,CAACN,IAAI,CAAC8C,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,oBAAoB;QAChCtB,KAAK,EAAEf,MAAO;QACdsC,QAAQ,EAAGzB,CAAC,IAAKZ,SAAS,CAACY,CAAC,CAACC,MAAM,CAACC,KAAK;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpC,OAAA,CAACT,KAAK;MAACqD,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACf,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDhC,OAAA;QAAO+B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BhC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAAgC,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbpC,OAAA;YAAAgC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBpC,OAAA;YAAAgC,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBpC,OAAA;YAAAgC,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpC,OAAA;YAAAgC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBpC,OAAA;YAAAgC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfpC,OAAA;YAAAgC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfpC,OAAA;YAAAgC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRpC,OAAA;QAAAgC,QAAA,EACGN,gBAAgB,CAACqB,GAAG,CAAEnB,CAAC,iBACtB5B,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAAgC,QAAA,EAAKJ,CAAC,CAACjB;UAAI;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjBpC,OAAA;YAAAgC,QAAA,EAAKJ,CAAC,CAAChB;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBpC,OAAA;YAAAgC,QAAA,EAAKJ,CAAC,CAACf;UAAW;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBpC,OAAA;YAAAgC,QAAA,GAAKJ,CAAC,CAACd,UAAU,EAAC,MAAI;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BpC,OAAA;YAAAgC,QAAA,GAAKJ,CAAC,CAACb,OAAO,EAAC,MAAI;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBpC,OAAA;YAAAgC,QAAA,GAAKJ,CAAC,CAACZ,MAAM,EAAC,SAAO;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BpC,OAAA;YAAAgC,QAAA,eAAIhC,OAAA;cAAM+B,SAAS,EAAE,YAAYH,CAAC,CAACH,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAG;cAAAO,QAAA,EAAEJ,CAAC,CAACH;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxGpC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA,CAACR,MAAM;cAAC6C,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAAChC,OAAA,CAACH,MAAM;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxEpC,OAAA,CAACR,MAAM;cAAC6C,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAAChC,OAAA,CAACF,OAAO;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAXER,CAAC,CAACL,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYT,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAERpC,OAAA,CAACP,KAAK;MAACuD,IAAI,EAAE7C,SAAU;MAAC8C,MAAM,EAAEA,CAAA,KAAM7C,YAAY,CAAC,KAAK,CAAE;MAAC8C,QAAQ;MAAAlB,QAAA,gBACjEhC,OAAA,CAACP,KAAK,CAAC0D,MAAM;QAACC,WAAW;QAAApB,QAAA,eACvBhC,OAAA,CAACP,KAAK,CAAC4D,KAAK;UAAArB,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACfpC,OAAA,CAACP,KAAK,CAAC6D,IAAI;QAAAtB,QAAA,eACThC,OAAA,CAACN,IAAI;UAAAsC,QAAA,gBACHhC,OAAA,CAACN,IAAI,CAAC6D,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;cAAAxB,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCpC,OAAA,CAACN,IAAI,CAAC8C,OAAO;cACX7B,IAAI,EAAC,MAAM;cACXS,KAAK,EAAEX,IAAI,CAACE,IAAK;cACjBgC,QAAQ,EAAE1B,YAAa;cACvByB,WAAW,EAAC;YAAmB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEbpC,OAAA,CAACL,GAAG;YAACoC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhC,OAAA,CAACJ,GAAG;cAAAoC,QAAA,gBACFhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;gBAAAxB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCpC,OAAA,CAACN,IAAI,CAAC+D,MAAM;gBAAC9C,IAAI,EAAC,UAAU;gBAACS,KAAK,EAAEX,IAAI,CAACG,QAAS;gBAAC+B,QAAQ,EAAE1B,YAAa;gBAAAe,QAAA,eACxEhC,OAAA;kBAAQoB,KAAK,EAAC,EAAE;kBAAAY,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNpC,OAAA,CAACJ,GAAG;cAAAoC,QAAA,gBACFhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;gBAAAxB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCpC,OAAA,CAACN,IAAI,CAAC+D,MAAM;gBAAC9C,IAAI,EAAC,aAAa;gBAACS,KAAK,EAAEX,IAAI,CAACI,WAAY;gBAAC8B,QAAQ,EAAE1B,YAAa;gBAAAe,QAAA,eAC9EhC,OAAA;kBAAQoB,KAAK,EAAC,EAAE;kBAAAY,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA,CAACL,GAAG;YAACoC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhC,OAAA,CAACJ,GAAG;cAAAoC,QAAA,gBACFhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;gBAAAxB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpC,OAAA,CAACN,IAAI,CAAC8C,OAAO;gBACXC,IAAI,EAAC,QAAQ;gBACb9B,IAAI,EAAC,YAAY;gBACjBS,KAAK,EAAEX,IAAI,CAACK,UAAW;gBACvB6B,QAAQ,EAAE1B;cAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA,CAACJ,GAAG;cAAAoC,QAAA,gBACFhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;gBAAAxB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtCpC,OAAA,CAACN,IAAI,CAAC8C,OAAO;gBACXC,IAAI,EAAC,QAAQ;gBACb9B,IAAI,EAAC,SAAS;gBACdS,KAAK,EAAEX,IAAI,CAACM,OAAQ;gBACpB4B,QAAQ,EAAE1B;cAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA,CAACN,IAAI,CAAC6D,KAAK;YAACxB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BhC,OAAA,CAACN,IAAI,CAAC8D,KAAK;cAAAxB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxCpC,OAAA,CAACN,IAAI,CAAC8C,OAAO;cACXC,IAAI,EAAC,QAAQ;cACb9B,IAAI,EAAC,QAAQ;cACbS,KAAK,EAAEX,IAAI,CAACO,MAAO;cACnB2B,QAAQ,EAAE1B;YAAa;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbpC,OAAA,CAACP,KAAK,CAACiE,MAAM;QAAA1B,QAAA,gBACXhC,OAAA,CAACR,MAAM;UAAC6C,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,KAAK,CAAE;UAAA4B,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EpC,OAAA,CAACR,MAAM;UAAC6C,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEjB,UAAW;UAAAW,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAClC,EAAA,CA3LID,eAAe;AAAA0D,EAAA,GAAf1D,eAAe;AA6LrB,eAAeA,eAAe;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}