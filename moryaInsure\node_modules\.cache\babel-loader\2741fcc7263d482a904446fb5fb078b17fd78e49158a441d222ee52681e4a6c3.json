{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    if (token && userData) {\n      try {\n        setUser(JSON.parse(userData));\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async (email, password) => {\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Mock authentication logic\n      let userData = null;\n      if (email === '<EMAIL>' && password === 'admin123') {\n        userData = {\n          id: 1,\n          name: 'Admin User',\n          email: email,\n          role: 'admin'\n        };\n      } else if (email === '<EMAIL>' && password === 'emp123') {\n        userData = {\n          id: 2,\n          name: 'Employee User',\n          email: email,\n          role: 'employee'\n        };\n      } else if (email === '<EMAIL>' && password === 'cust123') {\n        userData = {\n          id: 3,\n          name: 'Customer User',\n          email: email,\n          role: 'customer'\n        };\n      } else {\n        throw new Error('Invalid credentials');\n      }\n      const token = 'mock-jwt-token-' + Date.now();\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setUser(userData);\n      return {\n        success: true,\n        user: userData\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Mock registration logic\n      const newUser = {\n        id: Date.now(),\n        name: `${userData.firstName} ${userData.lastName}`,\n        email: userData.email,\n        role: userData.role\n      };\n      return {\n        success: true,\n        user: newUser\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n  const updateUser = updatedUserData => {\n    const updatedUser = {\n      ...user,\n      ...updatedUserData\n    };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n  const hasAnyRole = roles => {\n    return user && roles.includes(user.role);\n  };\n  const isAuthenticated = () => {\n    return !!user && !!localStorage.getItem('token');\n  };\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    hasRole,\n    hasAnyRole,\n    isAuthenticated\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "userData", "JSON", "parse", "error", "console", "removeItem", "login", "email", "password", "Promise", "resolve", "setTimeout", "id", "name", "role", "Date", "now", "setItem", "stringify", "success", "message", "register", "newUser", "firstName", "lastName", "logout", "updateUser", "updatedUserData", "updatedUser", "hasRole", "hasAnyRole", "roles", "includes", "isAuthenticated", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    \n    if (token && userData) {\n      try {\n        setUser(JSON.parse(userData));\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock authentication logic\n      let userData = null;\n      \n      if (email === '<EMAIL>' && password === 'admin123') {\n        userData = {\n          id: 1,\n          name: 'Admin User',\n          email: email,\n          role: 'admin'\n        };\n      } else if (email === '<EMAIL>' && password === 'emp123') {\n        userData = {\n          id: 2,\n          name: 'Employee User',\n          email: email,\n          role: 'employee'\n        };\n      } else if (email === '<EMAIL>' && password === 'cust123') {\n        userData = {\n          id: 3,\n          name: 'Customer User',\n          email: email,\n          role: 'customer'\n        };\n      } else {\n        throw new Error('Invalid credentials');\n      }\n      \n      const token = 'mock-jwt-token-' + Date.now();\n      \n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setUser(userData);\n      \n      return { success: true, user: userData };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Mock registration logic\n      const newUser = {\n        id: Date.now(),\n        name: `${userData.firstName} ${userData.lastName}`,\n        email: userData.email,\n        role: userData.role\n      };\n      \n      return { success: true, user: newUser };\n    } catch (error) {\n      return { success: false, error: error.message };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  const updateUser = (updatedUserData) => {\n    const updatedUser = { ...user, ...updatedUserData };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  const hasAnyRole = (roles) => {\n    return user && roles.includes(user.role);\n  };\n\n  const isAuthenticated = () => {\n    return !!user && !!localStorage.getItem('token');\n  };\n\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    hasRole,\n    hasAnyRole,\n    isAuthenticated\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7C,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACFN,OAAO,CAACO,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF;MACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,IAAIV,QAAQ,GAAG,IAAI;MAEnB,IAAIO,KAAK,KAAK,iBAAiB,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAC1DR,QAAQ,GAAG;UACTY,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,YAAY;UAClBN,KAAK,EAAEA,KAAK;UACZO,IAAI,EAAE;QACR,CAAC;MACH,CAAC,MAAM,IAAIP,KAAK,KAAK,oBAAoB,IAAIC,QAAQ,KAAK,QAAQ,EAAE;QAClER,QAAQ,GAAG;UACTY,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,eAAe;UACrBN,KAAK,EAAEA,KAAK;UACZO,IAAI,EAAE;QACR,CAAC;MACH,CAAC,MAAM,IAAIP,KAAK,KAAK,oBAAoB,IAAIC,QAAQ,KAAK,SAAS,EAAE;QACnER,QAAQ,GAAG;UACTY,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,eAAe;UACrBN,KAAK,EAAEA,KAAK;UACZO,IAAI,EAAE;QACR,CAAC;MACH,CAAC,MAAM;QACL,MAAM,IAAIzB,KAAK,CAAC,qBAAqB,CAAC;MACxC;MAEA,MAAMQ,KAAK,GAAG,iBAAiB,GAAGkB,IAAI,CAACC,GAAG,CAAC,CAAC;MAE5ClB,YAAY,CAACmB,OAAO,CAAC,OAAO,EAAEpB,KAAK,CAAC;MACpCC,YAAY,CAACmB,OAAO,CAAC,MAAM,EAAEhB,IAAI,CAACiB,SAAS,CAAClB,QAAQ,CAAC,CAAC;MACtDN,OAAO,CAACM,QAAQ,CAAC;MAEjB,OAAO;QAAEmB,OAAO,EAAE,IAAI;QAAE1B,IAAI,EAAEO;MAAS,CAAC;IAC1C,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOrB,QAAQ,IAAK;IACnC,IAAI;MACF;MACA,MAAM,IAAIS,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAMY,OAAO,GAAG;QACdV,EAAE,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC;QACdH,IAAI,EAAE,GAAGb,QAAQ,CAACuB,SAAS,IAAIvB,QAAQ,CAACwB,QAAQ,EAAE;QAClDjB,KAAK,EAAEP,QAAQ,CAACO,KAAK;QACrBO,IAAI,EAAEd,QAAQ,CAACc;MACjB,CAAC;MAED,OAAO;QAAEK,OAAO,EAAE,IAAI;QAAE1B,IAAI,EAAE6B;MAAQ,CAAC;IACzC,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd,OAAO;QAAEgB,OAAO,EAAE,KAAK;QAAEhB,KAAK,EAAEA,KAAK,CAACiB;MAAQ,CAAC;IACjD;EACF,CAAC;EAED,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnB3B,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BX,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMgC,UAAU,GAAIC,eAAe,IAAK;IACtC,MAAMC,WAAW,GAAG;MAAE,GAAGnC,IAAI;MAAE,GAAGkC;IAAgB,CAAC;IACnDjC,OAAO,CAACkC,WAAW,CAAC;IACpB9B,YAAY,CAACmB,OAAO,CAAC,MAAM,EAAEhB,IAAI,CAACiB,SAAS,CAACU,WAAW,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,OAAO,GAAIf,IAAI,IAAK;IACxB,OAAOrB,IAAI,IAAIA,IAAI,CAACqB,IAAI,KAAKA,IAAI;EACnC,CAAC;EAED,MAAMgB,UAAU,GAAIC,KAAK,IAAK;IAC5B,OAAOtC,IAAI,IAAIsC,KAAK,CAACC,QAAQ,CAACvC,IAAI,CAACqB,IAAI,CAAC;EAC1C,CAAC;EAED,MAAMmB,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACxC,IAAI,IAAI,CAAC,CAACK,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAClD,CAAC;EAED,MAAMmC,KAAK,GAAG;IACZzC,IAAI;IACJE,OAAO;IACPW,KAAK;IACLe,QAAQ;IACRI,MAAM;IACNC,UAAU;IACVG,OAAO;IACPC,UAAU;IACVG;EACF,CAAC;EAED,oBACEjD,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CA9HWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAgIzB,eAAeL,WAAW;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}