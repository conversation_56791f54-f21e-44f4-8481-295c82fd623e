import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, <PERSON>, Col, Card, <PERSON>, Button, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { FaShieldAlt, FaEnvelope, FaRedo } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

const VerifyOTP = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { verifyOTP, resendOTP } = useAuth();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const [canResend, setCanResend] = useState(false);

  // Get user data from registration
  const userData = location.state?.userData;
  const userEmail = userData?.email || '';
  const userId = userData?.userId || '';

  useEffect(() => {
    // Redirect if no user data
    if (!userData || !userId) {
      navigate('/register');
      return;
    }

    // Start countdown timer
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [userData, userId, navigate]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleOtpChange = (index, value) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');
    setSuccess('');

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = [...otp];
    
    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/^\d$/.test(pastedData[i])) {
        newOtp[i] = pastedData[i];
      }
    }
    
    setOtp(newOtp);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      setError('Please enter the complete 6-digit OTP');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const result = await verifyOTP(userId, otpString);

      if (result.success) {
        setSuccess('Email verified successfully! Redirecting to login...');

        // Redirect to login after 2 seconds
        setTimeout(() => {
          navigate('/login', {
            state: {
              message: 'Registration completed successfully! Please log in.',
              email: userEmail
            }
          });
        }, 2000);
      } else {
        setError(result.error || 'Failed to verify OTP. Please try again.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      setError('Failed to verify OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setResendLoading(true);
    setError('');
    setSuccess('');

    try {
      const result = await resendOTP(userId);

      if (result.success) {
        setSuccess('New OTP sent to your email!');
        setTimeLeft(600); // Reset timer to 10 minutes
        setCanResend(false);
        setOtp(['', '', '', '', '', '']); // Clear current OTP

        // Restart timer
        const timer = setInterval(() => {
          setTimeLeft((prevTime) => {
            if (prevTime <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prevTime - 1;
          });
        }, 1000);
      } else {
        setError(result.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      setError('Failed to resend OTP. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Container fluid className="min-vh-100 d-flex align-items-center justify-content-center bg-light">
      <Row className="w-100 justify-content-center">
        <Col xs={12} sm={10} md={8} lg={6} xl={5}>
          <Card className="shadow-lg border-0">
            <Card.Header className="bg-primary text-white text-center py-4">
              <FaShieldAlt size={48} className="mb-3" />
              <h3 className="mb-0">Verify Your Email</h3>
              <p className="mb-0 mt-2">Enter the 6-digit code sent to your email</p>
            </Card.Header>
            
            <Card.Body className="p-5">
              {error && <Alert variant="danger" className="mb-4">{error}</Alert>}
              {success && <Alert variant="success" className="mb-4">{success}</Alert>}
              
              <div className="text-center mb-4">
                <FaEnvelope size={24} className="text-primary mb-2" />
                <p className="text-muted mb-0">
                  We've sent a verification code to:
                </p>
                <p className="fw-bold text-primary">{userEmail}</p>
              </div>

              <Form onSubmit={handleSubmit}>
                <div className="d-flex justify-content-center mb-4">
                  {otp.map((digit, index) => (
                    <Form.Control
                      key={index}
                      id={`otp-${index}`}
                      type="text"
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      onPaste={handlePaste}
                      className="text-center mx-1 otp-input"
                      style={{
                        width: '50px',
                        height: '50px',
                        fontSize: '20px',
                        fontWeight: 'bold'
                      }}
                      maxLength={1}
                      pattern="[0-9]"
                      inputMode="numeric"
                      autoComplete="off"
                    />
                  ))}
                </div>

                <div className="text-center mb-4">
                  {timeLeft > 0 ? (
                    <p className="text-muted">
                      Code expires in: <span className="fw-bold text-warning">{formatTime(timeLeft)}</span>
                    </p>
                  ) : (
                    <p className="text-danger fw-bold">Code has expired</p>
                  )}
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-100 mb-3"
                  disabled={loading || otp.join('').length !== 6}
                >
                  {loading ? (
                    <>
                      <Spinner animation="border" size="sm" className="me-2" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Email'
                  )}
                </Button>

                <div className="text-center">
                  <p className="text-muted mb-2">Didn't receive the code?</p>
                  <Button
                    variant="outline-primary"
                    onClick={handleResendOTP}
                    disabled={!canResend || resendLoading}
                    className="me-2"
                  >
                    {resendLoading ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <FaRedo className="me-2" />
                        Resend Code
                      </>
                    )}
                  </Button>
                </div>
              </Form>

              <hr className="my-4" />
              
              <div className="text-center">
                <p className="text-muted mb-0">
                  Remember your password? 
                  <Link to="/login" className="text-primary text-decoration-none ms-1">
                    Back to Login
                  </Link>
                </p>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <style jsx>{`
        .otp-input:focus {
          border-color: #0d6efd;
          box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
      `}</style>
    </Container>
  );
};

export default VerifyOTP;
