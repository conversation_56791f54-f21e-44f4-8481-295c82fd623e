import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5002/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      if (status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
      
      return Promise.reject({
        message: data.message || 'An error occurred',
        status,
        errors: data.errors || []
      });
    } else if (error.request) {
      // Network error
      return Promise.reject({
        message: 'Network error. Please check your connection.',
        status: 0
      });
    } else {
      // Other error
      return Promise.reject({
        message: error.message || 'An unexpected error occurred',
        status: 0
      });
    }
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  verifyOTP: (otpData) => api.post('/auth/verify-otp', otpData),
  resendOTP: (userData) => api.post('/auth/resend-otp', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
  logout: () => api.post('/auth/logout'),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  toggleUserStatus: (id) => api.put(`/users/${id}/status`),
  getUserStats: () => api.get('/users/stats/overview'),
};

// Policies API
export const policiesAPI = {
  getPolicies: (params) => api.get('/policies', { params }),
  getPolicy: (id) => api.get(`/policies/${id}`),
  createPolicy: (policyData) => api.post('/policies', policyData),
  updatePolicy: (id, policyData) => api.put(`/policies/${id}`, policyData),
  updatePolicyStatus: (id, statusData) => api.put(`/policies/${id}/status`, statusData),
};

// Categories API
export const categoriesAPI = {
  getCategories: (params) => api.get('/categories', { params }),
  createCategory: (categoryData) => api.post('/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/categories/${id}`),
};

// Tickets API
export const ticketsAPI = {
  getTickets: (params) => api.get('/tickets', { params }),
  getTicket: (id) => api.get(`/tickets/${id}`),
  createTicket: (ticketData) => api.post('/tickets', ticketData),
  assignTicket: (id, assignData) => api.put(`/tickets/${id}/assign`, assignData),
  addComment: (id, commentData) => api.post(`/tickets/${id}/comments`, commentData),
};

// Claims API
export const claimsAPI = {
  getClaims: (params) => api.get('/claims', { params }),
  getClaimById: (id) => api.get(`/claims/${id}`),
  createClaim: (claimData) => api.post('/claims', claimData),
  updateClaim: (id, claimData) => api.put(`/claims/${id}`, claimData),
  deleteClaim: (id) => api.delete(`/claims/${id}`)
};

// Reports API
export const reportsAPI = {
  getDashboardStats: () => api.get('/reports/dashboard'),
  getPolicyReports: (params) => api.get('/reports/policies', { params }),
  getTicketReports: (params) => api.get('/reports/tickets', { params }),
};

// Notification API
export const notificationAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  getUnreadNotifications: () => api.get('/notifications/unread'),
  getNotificationCounts: () => api.get('/notifications/counts'),
  markAsRead: (id) => api.put(`/notifications/${id}/read`),
  markMultipleAsRead: (notificationIds) => api.put('/notifications/read-multiple', { notificationIds }),
  markAllAsRead: () => api.put('/notifications/read-all'),
  archiveNotification: (id) => api.put(`/notifications/${id}/archive`),
  deleteNotification: (id) => api.delete(`/notifications/${id}`),
  sendNotification: (data) => api.post('/notifications/send', data)
};

// Claims API
export const claimAPI = {
  getClaims: (params) => api.get('/claims', { params }),
  getClaim: (id) => api.get(`/claims/${id}`),
  createClaim: (data) => api.post('/claims', data),
  assignClaim: (id, adjusterId) => api.put(`/claims/${id}/assign`, { adjusterId }),
  updateClaimStatus: (id, status, notes) => api.put(`/claims/${id}/status`, { status, notes }),
  addClaimComment: (id, content, isInternal) => api.post(`/claims/${id}/comments`, { content, isInternal }),
  getClaimStats: () => api.get('/claims/stats/overview')
};

// Upload API
export const uploadAPI = {
  uploadPolicyDocuments: (formData) => api.post('/uploads/policy-documents', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  uploadClaimDocuments: (formData) => api.post('/uploads/claim-documents', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  uploadTicketAttachments: (formData) => api.post('/uploads/ticket-attachments', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  uploadProfilePicture: (formData) => api.post('/uploads/profile-picture', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  uploadTempFiles: (formData) => api.post('/uploads/temp', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  moveTempFiles: (tempFilenames, targetSubfolder, entityType, entityId) =>
    api.post('/uploads/move-temp', { tempFilenames, targetSubfolder, entityType, entityId }),
  deleteFile: (subfolder, filename) => api.delete(`/uploads/${subfolder}/${filename}`),
  getFileInfo: (subfolder, filename) => api.get(`/uploads/info/${subfolder}/${filename}`)
};

// User Management API
export const userAPI = {
  getUsers: (params) => api.get('/users', { params }),
  createUser: (data) => api.post('/users', data),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),
  toggleUserStatus: (id) => api.put(`/users/${id}/status`),
  getUserById: (id) => api.get(`/users/${id}`)
};

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
