const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Task = require('../models/Task');
const User = require('../models/User');
const { authenticate, adminOnly, adminOrEmployee } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/tasks/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx|xls|xlsx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images, PDFs, and documents are allowed'));
    }
  }
});

// @route   GET /api/tasks
// @desc    Get tasks (role-based filtering)
// @access  Private
router.get('/', authenticate, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isString().withMessage('Status must be a string'),
  query('priority').optional().isString().withMessage('Priority must be a string'),
  query('type').optional().isString().withMessage('Type must be a string'),
  query('assignedTo').optional().isMongoId().withMessage('Invalid assignee ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { status, priority, type, assignedTo } = req.query;
    
    let query = { isDeleted: false };
    
    // Role-based filtering
    if (req.user.role === 'employee') {
      query.assignedTo = req.user._id;
    } else if (req.user.role === 'admin') {
      // Admin can see all tasks or filter by assignedTo
      if (assignedTo) {
        query.assignedTo = assignedTo;
      }
    }
    
    // Apply filters
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (type) query.type = type;

    const tasks = await Task.find(query)
      .populate('assignedTo', 'firstName lastName email role')
      .populate('assignedBy', 'firstName lastName email')
      .populate('comments.author', 'firstName lastName')
      .sort({ dueDate: 1, priority: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Task.countDocuments(query);

    res.json({
      success: true,
      data: {
        tasks,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching tasks'
    });
  }
});

// @route   GET /api/tasks/my-tasks
// @desc    Get tasks assigned to current user
// @access  Private (Employee)
router.get('/my-tasks', authenticate, async (req, res) => {
  try {
    const { status } = req.query;
    
    const tasks = await Task.getByAssignee(req.user._id, { status });
    
    res.json({
      success: true,
      data: { tasks }
    });
  } catch (error) {
    console.error('Get my tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching your tasks'
    });
  }
});

// @route   GET /api/tasks/assigned-by-me
// @desc    Get tasks assigned by current user
// @access  Private (Admin)
router.get('/assigned-by-me', authenticate, adminOnly, async (req, res) => {
  try {
    const { status } = req.query;
    
    const tasks = await Task.getByAssigner(req.user._id, { status });
    
    res.json({
      success: true,
      data: { tasks }
    });
  } catch (error) {
    console.error('Get assigned tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching assigned tasks'
    });
  }
});

// @route   GET /api/tasks/overdue
// @desc    Get overdue tasks
// @access  Private
router.get('/overdue', authenticate, async (req, res) => {
  try {
    let assigneeId = null;
    
    // Employees can only see their overdue tasks
    if (req.user.role === 'employee') {
      assigneeId = req.user._id;
    }
    
    const tasks = await Task.getOverdueTasks(assigneeId);
    
    res.json({
      success: true,
      data: { tasks }
    });
  } catch (error) {
    console.error('Get overdue tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching overdue tasks'
    });
  }
});

// @route   GET /api/tasks/stats
// @desc    Get task statistics
// @access  Private
router.get('/stats', authenticate, async (req, res) => {
  try {
    const stats = await Task.getTaskStats(req.user._id, req.user.role);
    
    // Format stats for frontend
    const formattedStats = {
      pending: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0,
      on_hold: 0
    };
    
    stats.forEach(stat => {
      formattedStats[stat._id] = stat.count;
    });
    
    res.json({
      success: true,
      data: { stats: formattedStats }
    });
  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching task statistics'
    });
  }
});

// @route   GET /api/tasks/:id
// @desc    Get task by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    let query = { _id: req.params.id, isDeleted: false };
    
    // Role-based filtering
    if (req.user.role === 'employee') {
      query.assignedTo = req.user._id;
    }

    const task = await Task.findOne(query)
      .populate('assignedTo', 'firstName lastName email role phone')
      .populate('assignedBy', 'firstName lastName email')
      .populate('comments.author', 'firstName lastName')
      .populate('timeEntries.loggedBy', 'firstName lastName');

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    res.json({
      success: true,
      data: { task }
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching task'
    });
  }
});

// @route   POST /api/tasks
// @desc    Create new task (Admin only)
// @access  Private (Admin)
router.post('/', authenticate, adminOnly, upload.array('attachments', 5), [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('type')
    .isIn(['policy_review', 'claim_processing', 'customer_support', 'document_verification', 'follow_up', 'investigation', 'other'])
    .withMessage('Invalid task type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('assignedTo')
    .isMongoId()
    .withMessage('Valid assignee ID is required'),
  body('dueDate')
    .isISO8601()
    .withMessage('Valid due date is required'),
  body('estimatedHours')
    .optional()
    .isFloat({ min: 0.5, max: 40 })
    .withMessage('Estimated hours must be between 0.5 and 40')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { title, description, type, priority, assignedTo, dueDate, estimatedHours, relatedEntity, tags } = req.body;

    // Verify assignee exists and is an employee
    const assignee = await User.findById(assignedTo);
    if (!assignee || assignee.role !== 'employee') {
      return res.status(400).json({
        success: false,
        message: 'Assignee must be a valid employee'
      });
    }

    // Process uploaded attachments
    const attachments = req.files ? req.files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      path: file.path,
      mimetype: file.mimetype,
      size: file.size,
      uploadedBy: req.user._id
    })) : [];

    // Create task
    const task = new Task({
      title,
      description,
      type,
      priority: priority || 'medium',
      assignedTo,
      assignedBy: req.user._id,
      dueDate,
      estimatedHours: estimatedHours || 1,
      relatedEntity: relatedEntity ? JSON.parse(relatedEntity) : undefined,
      tags: tags ? JSON.parse(tags) : [],
      attachments
    });

    await task.save();
    await task.populate('assignedTo', 'firstName lastName email');
    await task.populate('assignedBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      message: 'Task created and assigned successfully',
      data: { task }
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating task'
    });
  }
});

// @route   PUT /api/tasks/:id
// @desc    Update task
// @access  Private
router.put('/:id', authenticate, [
  body('status')
    .optional()
    .isIn(['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'])
    .withMessage('Invalid status'),
  body('progress')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Progress must be between 0 and 100'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let query = { _id: req.params.id, isDeleted: false };

    // Role-based access control
    if (req.user.role === 'employee') {
      query.assignedTo = req.user._id;
    }

    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    const { status, progress, priority, completionNotes } = req.body;

    // Update fields
    if (status) task.status = status;
    if (progress !== undefined) task.progress = progress;
    if (priority && req.user.role === 'admin') task.priority = priority;
    if (completionNotes) task.completionNotes = completionNotes;

    // Handle completion
    if (status === 'completed') {
      task.completedAt = new Date();
      task.progress = 100;
    }

    await task.save();
    await task.populate('assignedTo', 'firstName lastName email');
    await task.populate('assignedBy', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Task updated successfully',
      data: { task }
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating task'
    });
  }
});

// @route   POST /api/tasks/:id/comments
// @desc    Add comment to task
// @access  Private
router.post('/:id/comments', authenticate, [
  body('content')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Comment must be between 1 and 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let query = { _id: req.params.id, isDeleted: false };

    // Role-based access control
    if (req.user.role === 'employee') {
      query.assignedTo = req.user._id;
    }

    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    await task.addComment(req.user._id, req.body.content);
    await task.populate('comments.author', 'firstName lastName');

    res.json({
      success: true,
      message: 'Comment added successfully',
      data: {
        comment: task.comments[task.comments.length - 1]
      }
    });
  } catch (error) {
    console.error('Add comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while adding comment'
    });
  }
});

// @route   POST /api/tasks/:id/time
// @desc    Log time for task
// @access  Private
router.post('/:id/time', authenticate, [
  body('startTime')
    .isISO8601()
    .withMessage('Valid start time is required'),
  body('endTime')
    .isISO8601()
    .withMessage('Valid end time is required'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Description cannot exceed 200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let query = { _id: req.params.id, isDeleted: false };

    // Role-based access control
    if (req.user.role === 'employee') {
      query.assignedTo = req.user._id;
    }

    const task = await Task.findOne(query);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    const { startTime, endTime, description } = req.body;
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (end <= start) {
      return res.status(400).json({
        success: false,
        message: 'End time must be after start time'
      });
    }

    await task.logTime(req.user._id, start, end, description);

    res.json({
      success: true,
      message: 'Time logged successfully',
      data: {
        timeEntry: task.timeEntries[task.timeEntries.length - 1],
        totalTimeSpent: task.totalTimeSpent
      }
    });
  } catch (error) {
    console.error('Log time error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while logging time'
    });
  }
});

// @route   DELETE /api/tasks/:id
// @desc    Delete task (soft delete)
// @access  Private (Admin only)
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Soft delete
    task.isDeleted = true;
    task.deletedAt = new Date();
    task.deletedBy = req.user._id;

    await task.save();

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting task'
    });
  }
});

module.exports = router;
