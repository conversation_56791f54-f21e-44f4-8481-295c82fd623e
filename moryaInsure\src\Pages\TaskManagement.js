import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Table, Badge, Modal, Form, Alert, Spin<PERSON>, Ta<PERSON>, Tab } from 'react-bootstrap';
import { FaPlus, FaEye, FaEdit, FaTrash, FaClock, FaUser, FaCalendarAlt, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { tasksAPI, usersAPI } from '../services/api';

const TaskManagement = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [activeTab, setActiveTab] = useState('all');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'other',
    priority: 'medium',
    assignedTo: '',
    dueDate: '',
    estimatedHours: 1,
    tags: []
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [taskStats, setTaskStats] = useState({});

  useEffect(() => {
    fetchTasks();
    fetchEmployees();
    fetchTaskStats();
  }, [activeTab]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      let response;
      
      if (activeTab === 'assigned-by-me') {
        response = await tasksAPI.getAssignedByMe();
      } else if (activeTab === 'overdue') {
        response = await tasksAPI.getOverdueTasks();
      } else {
        const params = activeTab !== 'all' ? { status: activeTab } : {};
        response = await tasksAPI.getTasks(params);
      }
      
      if (response.success) {
        setTasks(response.data.tasks || []);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setError('Failed to fetch tasks');
    } finally {
      setLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await usersAPI.getUsers({ role: 'employee' });
      if (response.success) {
        setEmployees(response.data.users || []);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const fetchTaskStats = async () => {
    try {
      const response = await tasksAPI.getTaskStats();
      if (response.success) {
        setTaskStats(response.data.stats || {});
      }
    } catch (error) {
      console.error('Error fetching task stats:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const taskData = {
        ...formData,
        dueDate: new Date(formData.dueDate).toISOString(),
        estimatedHours: parseFloat(formData.estimatedHours)
      };

      let response;
      if (modalMode === 'create') {
        response = await tasksAPI.createTask(taskData);
      } else {
        response = await tasksAPI.updateTask(selectedTask._id, taskData);
      }
      
      if (response.success) {
        setSuccess(`Task ${modalMode === 'create' ? 'created' : 'updated'} successfully!`);
        setShowModal(false);
        resetForm();
        fetchTasks();
        fetchTaskStats();
      } else {
        setError(response.message || `Failed to ${modalMode} task`);
      }
    } catch (error) {
      console.error(`Error ${modalMode} task:`, error);
      setError(`Failed to ${modalMode} task. Please try again.`);
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'other',
      priority: 'medium',
      assignedTo: '',
      dueDate: '',
      estimatedHours: 1,
      tags: []
    });
    setSelectedTask(null);
    setModalMode('create');
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (task) => {
    setSelectedTask(task);
    setFormData({
      title: task.title,
      description: task.description,
      type: task.type,
      priority: task.priority,
      assignedTo: task.assignedTo._id,
      dueDate: new Date(task.dueDate).toISOString().split('T')[0],
      estimatedHours: task.estimatedHours,
      tags: task.tags || []
    });
    setModalMode('edit');
    setShowModal(true);
  };

  const openViewModal = (task) => {
    setSelectedTask(task);
    setModalMode('view');
    setShowModal(true);
  };

  const handleDeleteTask = async (taskId) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        const response = await tasksAPI.deleteTask(taskId);
        if (response.success) {
          setSuccess('Task deleted successfully!');
          fetchTasks();
          fetchTaskStats();
        } else {
          setError('Failed to delete task');
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        setError('Failed to delete task');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'pending': { variant: 'warning', text: 'Pending' },
      'in_progress': { variant: 'info', text: 'In Progress' },
      'completed': { variant: 'success', text: 'Completed' },
      'cancelled': { variant: 'secondary', text: 'Cancelled' },
      'on_hold': { variant: 'dark', text: 'On Hold' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'low': { variant: 'success', text: 'Low' },
      'medium': { variant: 'warning', text: 'Medium' },
      'high': { variant: 'danger', text: 'High' },
      'urgent': { variant: 'dark', text: 'Urgent' }
    };
    
    const config = priorityConfig[priority] || { variant: 'secondary', text: priority };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const isOverdue = (dueDate, status) => {
    return new Date(dueDate) < new Date() && status !== 'completed' && status !== 'cancelled';
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Task Management</h2>
              <p className="text-muted">Assign and manage tasks for employees</p>
            </div>
            <Button variant="primary" onClick={openCreateModal}>
              <FaPlus className="me-2" />
              Assign New Task
            </Button>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Task Statistics */}
      <Row className="mb-4">
        <Col md={2}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-warning">{taskStats.pending || 0}</h4>
              <small>Pending</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={2}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-info">{taskStats.in_progress || 0}</h4>
              <small>In Progress</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={2}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-success">{taskStats.completed || 0}</h4>
              <small>Completed</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={2}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-secondary">{taskStats.cancelled || 0}</h4>
              <small>Cancelled</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={2}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-dark">{taskStats.on_hold || 0}</h4>
              <small>On Hold</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Task Tabs */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <Tabs activeKey={activeTab} onSelect={setActiveTab}>
                <Tab eventKey="all" title="All Tasks" />
                <Tab eventKey="pending" title="Pending" />
                <Tab eventKey="in_progress" title="In Progress" />
                <Tab eventKey="completed" title="Completed" />
                <Tab eventKey="overdue" title="Overdue" />
                <Tab eventKey="assigned-by-me" title="Assigned by Me" />
              </Tabs>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading tasks...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-4">
                  <FaUser size={48} className="text-muted mb-3" />
                  <h5>No Tasks Found</h5>
                  <p className="text-muted">No tasks match the current filter.</p>
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Task</th>
                      <th>Assigned To</th>
                      <th>Type</th>
                      <th>Priority</th>
                      <th>Status</th>
                      <th>Due Date</th>
                      <th>Progress</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tasks.map((task) => (
                      <tr key={task._id} className={isOverdue(task.dueDate, task.status) ? 'table-danger' : ''}>
                        <td>
                          <div>
                            <strong>{task.title}</strong>
                            {isOverdue(task.dueDate, task.status) && (
                              <FaExclamationTriangle className="text-danger ms-2" title="Overdue" />
                            )}
                          </div>
                          <small className="text-muted">{task.description.substring(0, 50)}...</small>
                        </td>
                        <td>
                          <div>
                            <FaUser className="me-1" />
                            {task.assignedTo.firstName} {task.assignedTo.lastName}
                          </div>
                          <small className="text-muted">{task.assignedTo.email}</small>
                        </td>
                        <td>
                          <Badge bg="light" text="dark">
                            {task.type.replace('_', ' ')}
                          </Badge>
                        </td>
                        <td>{getPriorityBadge(task.priority)}</td>
                        <td>{getStatusBadge(task.status)}</td>
                        <td>
                          <FaCalendarAlt className="me-1 text-muted" />
                          {formatDate(task.dueDate)}
                        </td>
                        <td>
                          <div className="progress" style={{ height: '20px' }}>
                            <div 
                              className="progress-bar" 
                              style={{ width: `${task.progress}%` }}
                            >
                              {task.progress}%
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-info"
                              size="sm"
                              onClick={() => openViewModal(task)}
                              title="View Details"
                            >
                              <FaEye />
                            </Button>
                            <Button
                              variant="outline-warning"
                              size="sm"
                              onClick={() => openEditModal(task)}
                              title="Edit Task"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDeleteTask(task._id)}
                              title="Delete Task"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Task Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {modalMode === 'create' && 'Assign New Task'}
            {modalMode === 'edit' && 'Edit Task'}
            {modalMode === 'view' && 'Task Details'}
          </Modal.Title>
        </Modal.Header>
        
        {modalMode === 'view' && selectedTask ? (
          <Modal.Body>
            <Row>
              <Col md={6}>
                <h6>Task Information</h6>
                <p><strong>Title:</strong> {selectedTask.title}</p>
                <p><strong>Description:</strong> {selectedTask.description}</p>
                <p><strong>Type:</strong> {selectedTask.type.replace('_', ' ')}</p>
                <p><strong>Priority:</strong> {getPriorityBadge(selectedTask.priority)}</p>
                <p><strong>Status:</strong> {getStatusBadge(selectedTask.status)}</p>
              </Col>
              <Col md={6}>
                <h6>Assignment Details</h6>
                <p><strong>Assigned To:</strong> {selectedTask.assignedTo.firstName} {selectedTask.assignedTo.lastName}</p>
                <p><strong>Assigned By:</strong> {selectedTask.assignedBy.firstName} {selectedTask.assignedBy.lastName}</p>
                <p><strong>Due Date:</strong> {formatDate(selectedTask.dueDate)}</p>
                <p><strong>Estimated Hours:</strong> {selectedTask.estimatedHours}</p>
                <p><strong>Progress:</strong> {selectedTask.progress}%</p>
              </Col>
            </Row>
            
            {selectedTask.comments && selectedTask.comments.length > 0 && (
              <div className="mt-3">
                <h6>Comments</h6>
                {selectedTask.comments.map((comment, index) => (
                  <div key={index} className="border-start border-3 border-primary ps-3 mb-2">
                    <small className="text-muted">
                      {comment.author.firstName} {comment.author.lastName} - {formatDate(comment.createdAt)}
                    </small>
                    <p className="mb-0">{comment.content}</p>
                  </div>
                ))}
              </div>
            )}
          </Modal.Body>
        ) : (
          <Form onSubmit={handleSubmit}>
            <Modal.Body>
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Task Title *</Form.Label>
                    <Form.Control
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter task title"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Assign To *</Form.Label>
                    <Form.Select
                      name="assignedTo"
                      value={formData.assignedTo}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select employee...</option>
                      {employees.map(employee => (
                        <option key={employee._id} value={employee._id}>
                          {employee.firstName} {employee.lastName} - {employee.email}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Description *</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the task in detail..."
                  required
                />
              </Form.Group>

              <Row>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Task Type *</Form.Label>
                    <Form.Select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="policy_review">Policy Review</option>
                      <option value="claim_processing">Claim Processing</option>
                      <option value="customer_support">Customer Support</option>
                      <option value="document_verification">Document Verification</option>
                      <option value="follow_up">Follow Up</option>
                      <option value="investigation">Investigation</option>
                      <option value="other">Other</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Priority</Form.Label>
                    <Form.Select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Form.Group className="mb-3">
                    <Form.Label>Estimated Hours</Form.Label>
                    <Form.Control
                      type="number"
                      name="estimatedHours"
                      value={formData.estimatedHours}
                      onChange={handleInputChange}
                      min="0.5"
                      max="40"
                      step="0.5"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Due Date *</Form.Label>
                <Form.Control
                  type="date"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    {modalMode === 'create' ? 'Assigning...' : 'Updating...'}
                  </>
                ) : (
                  modalMode === 'create' ? 'Assign Task' : 'Update Task'
                )}
              </Button>
            </Modal.Footer>
          </Form>
        )}
      </Modal>
    </Container>
  );
};

export default TaskManagement;
