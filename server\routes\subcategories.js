const express = require('express');
const { body, query, validationResult } = require('express-validator');
const SubCategory = require('../models/SubCategory');
const Category = require('../models/Category');
const { authenticate, adminOrEmployee, adminOnly } = require('../middleware/auth');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/temp/' });

// @route   GET /api/subcategories
// @desc    Get all subcategories
// @access  Private
router.get('/', authenticate, [
  query('category').optional().isMongoId().withMessage('Invalid category ID'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('active').optional().isBoolean().withMessage('Active must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { category, search, active } = req.query;

    // Build filter
    let filter = {};
    if (category) filter.category = category;
    if (active !== undefined) filter.isActive = active === 'true';
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } }
      ];
    }

    const subcategories = await SubCategory.find(filter)
      .populate('category', 'name type description')
      .populate('createdBy', 'firstName lastName')
      .sort({ 'category.name': 1, sortOrder: 1, name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await SubCategory.countDocuments(filter);

    res.json({
      success: true,
      data: {
        subcategories,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Get subcategories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching subcategories'
    });
  }
});

// @route   GET /api/subcategories/:id
// @desc    Get subcategory by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const subcategory = await SubCategory.findById(req.params.id)
      .populate('category', 'name type description')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    if (!subcategory) {
      return res.status(404).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    res.json({
      success: true,
      data: { subcategory }
    });
  } catch (error) {
    console.error('Get subcategory error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching subcategory'
    });
  }
});

// @route   POST /api/subcategories
// @desc    Create new subcategory
// @access  Private (Admin/Employee)
router.post('/', authenticate, adminOrEmployee, [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('category')
    .isMongoId()
    .withMessage('Valid category ID is required'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('code')
    .optional()
    .trim()
    .isLength({ max: 10 })
    .withMessage('Code cannot exceed 10 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, category, description, code, isActive, sortOrder, metadata, insuranceDetails, businessRules, pricing } = req.body;

    // Check if category exists
    const parentCategory = await Category.findById(category);
    if (!parentCategory) {
      return res.status(400).json({
        success: false,
        message: 'Parent category not found'
      });
    }

    // Check for duplicate name within the same category
    const existingSubCategory = await SubCategory.findOne({ 
      name: name.trim(), 
      category 
    });
    
    if (existingSubCategory) {
      return res.status(400).json({
        success: false,
        message: 'Subcategory with this name already exists in the selected category'
      });
    }

    const subcategory = new SubCategory({
      name: name.trim(),
      category,
      description: description?.trim(),
      code: code?.trim().toUpperCase(),
      isActive,
      sortOrder,
      metadata,
      insuranceDetails,
      businessRules,
      pricing,
      createdBy: req.user._id
    });

    await subcategory.save();
    await subcategory.populate('category', 'name type description');

    res.status(201).json({
      success: true,
      message: 'Subcategory created successfully',
      data: { subcategory }
    });
  } catch (error) {
    console.error('Create subcategory error:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `${field} already exists`
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while creating subcategory'
    });
  }
});

// @route   PUT /api/subcategories/:id
// @desc    Update subcategory
// @access  Private (Admin/Employee)
router.put('/:id', authenticate, adminOrEmployee, [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('category')
    .optional()
    .isMongoId()
    .withMessage('Valid category ID is required'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const subcategory = await SubCategory.findById(req.params.id);
    if (!subcategory) {
      return res.status(404).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    const { name, category, description, code, isActive, sortOrder, metadata, insuranceDetails, businessRules, pricing } = req.body;

    // Check if category exists (if being updated)
    if (category && category !== subcategory.category.toString()) {
      const parentCategory = await Category.findById(category);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
    }

    // Check for duplicate name within the same category (if name or category is being updated)
    if (name || category) {
      const checkName = name || subcategory.name;
      const checkCategory = category || subcategory.category;
      
      const existingSubCategory = await SubCategory.findOne({ 
        name: checkName.trim(), 
        category: checkCategory,
        _id: { $ne: req.params.id }
      });
      
      if (existingSubCategory) {
        return res.status(400).json({
          success: false,
          message: 'Subcategory with this name already exists in the selected category'
        });
      }
    }

    // Update fields
    if (name) subcategory.name = name.trim();
    if (category) subcategory.category = category;
    if (description !== undefined) subcategory.description = description?.trim();
    if (code) subcategory.code = code.trim().toUpperCase();
    if (isActive !== undefined) subcategory.isActive = isActive;
    if (sortOrder !== undefined) subcategory.sortOrder = sortOrder;
    if (metadata) subcategory.metadata = { ...subcategory.metadata, ...metadata };
    if (insuranceDetails) subcategory.insuranceDetails = { ...subcategory.insuranceDetails, ...insuranceDetails };
    if (businessRules) subcategory.businessRules = { ...subcategory.businessRules, ...businessRules };
    if (pricing) subcategory.pricing = { ...subcategory.pricing, ...pricing };
    
    subcategory.updatedBy = req.user._id;

    await subcategory.save();
    await subcategory.populate('category', 'name type description');

    res.json({
      success: true,
      message: 'Subcategory updated successfully',
      data: { subcategory }
    });
  } catch (error) {
    console.error('Update subcategory error:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `${field} already exists`
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while updating subcategory'
    });
  }
});

// @route   DELETE /api/subcategories/:id
// @desc    Delete subcategory
// @access  Private (Admin only)
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const subcategory = await SubCategory.findById(req.params.id);
    if (!subcategory) {
      return res.status(404).json({
        success: false,
        message: 'Subcategory not found'
      });
    }

    // Check if subcategory is being used by any policies
    const Policy = require('../models/Policy');
    const policiesCount = await Policy.countDocuments({ subCategory: req.params.id });
    
    if (policiesCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete subcategory. It is being used by ${policiesCount} policies.`
      });
    }

    await SubCategory.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Subcategory deleted successfully'
    });
  } catch (error) {
    console.error('Delete subcategory error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting subcategory'
    });
  }
});

// @route   GET /api/subcategories/category/:categoryId
// @desc    Get subcategories by category
// @access  Private
router.get('/category/:categoryId', authenticate, async (req, res) => {
  try {
    const subcategories = await SubCategory.findByCategory(req.params.categoryId);

    res.json({
      success: true,
      data: { subcategories }
    });
  } catch (error) {
    console.error('Get subcategories by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching subcategories'
    });
  }
});

// @route   POST /api/subcategories/import
// @desc    Import subcategories from CSV
// @access  Private (Admin only)
router.post('/import', authenticate, adminOnly, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const results = [];
    const errors = [];

    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          let imported = 0;
          
          for (let i = 0; i < results.length; i++) {
            const row = results[i];
            
            try {
              // Validate required fields
              if (!row.name || !row.category) {
                errors.push(`Row ${i + 1}: Name and category are required`);
                continue;
              }

              // Find category by name or ID
              const category = await Category.findOne({
                $or: [
                  { _id: row.category },
                  { name: row.category }
                ]
              });

              if (!category) {
                errors.push(`Row ${i + 1}: Category '${row.category}' not found`);
                continue;
              }

              // Check for duplicate
              const existing = await SubCategory.findOne({
                name: row.name.trim(),
                category: category._id
              });

              if (existing) {
                errors.push(`Row ${i + 1}: Subcategory '${row.name}' already exists in category '${category.name}'`);
                continue;
              }

              // Create subcategory
              const subcategory = new SubCategory({
                name: row.name.trim(),
                category: category._id,
                description: row.description?.trim(),
                code: row.code?.trim().toUpperCase(),
                isActive: row.isActive !== 'false',
                sortOrder: parseInt(row.sortOrder) || 0,
                createdBy: req.user._id
              });

              await subcategory.save();
              imported++;
            } catch (error) {
              errors.push(`Row ${i + 1}: ${error.message}`);
            }
          }

          // Clean up uploaded file
          fs.unlinkSync(req.file.path);

          res.json({
            success: true,
            message: `Import completed. ${imported} subcategories imported.`,
            data: {
              imported,
              errors: errors.length > 0 ? errors : undefined
            }
          });
        } catch (error) {
          console.error('Import processing error:', error);
          res.status(500).json({
            success: false,
            message: 'Error processing import file'
          });
        }
      });
  } catch (error) {
    console.error('Import subcategories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during import'
    });
  }
});

module.exports = router;
