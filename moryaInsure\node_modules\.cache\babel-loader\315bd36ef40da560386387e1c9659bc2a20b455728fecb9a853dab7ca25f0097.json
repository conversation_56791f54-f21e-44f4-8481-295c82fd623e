{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\dashboards\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalPolicyHolders: 0,\n    pendingPolicies: 0,\n    approvedPolicies: 0,\n    deniedPolicies: 0,\n    totalTickets: 0,\n    pendingTickets: 0,\n    closedTickets: 0,\n    activeFields: 0,\n    totalEmployees: 0,\n    totalCustomers: 0,\n    monthlyRevenue: 0,\n    systemAlerts: 0\n  });\n\n  // Fetch dashboard data\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        const response = await reportsAPI.getDashboardStats();\n        if (response.success) {\n          const {\n            overview\n          } = response.data;\n          setStats({\n            totalPolicyHolders: overview.totalUsers || 0,\n            pendingPolicies: overview.pendingPolicies || 0,\n            approvedPolicies: overview.activePolicies || 0,\n            deniedPolicies: 0,\n            // Calculate from total - active - pending\n            totalTickets: overview.totalTickets || 0,\n            pendingTickets: overview.openTickets || 0,\n            closedTickets: (overview.totalTickets || 0) - (overview.openTickets || 0),\n            activeFields: 12,\n            // Static for now\n            totalEmployees: overview.totalEmployees || 0,\n            totalCustomers: overview.totalCustomers || 0,\n            monthlyRevenue: overview.monthlyRevenue || 0,\n            systemAlerts: 3 // Static for now\n          });\n        }\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        // Fallback to mock data\n        setStats({\n          totalPolicyHolders: 1250,\n          pendingPolicies: 45,\n          approvedPolicies: 980,\n          deniedPolicies: 125,\n          totalTickets: 180,\n          pendingTickets: 35,\n          closedTickets: 145,\n          activeFields: 12,\n          totalEmployees: 25,\n          totalCustomers: 1200,\n          monthlyRevenue: 125000,\n          systemAlerts: 3\n        });\n      }\n    };\n    fetchDashboardData();\n  }, []);\n  const cardData = [{\n    label: 'Total Customers',\n    count: stats.totalCustomers,\n    icon: 'bi-people-fill',\n    color: 'primary'\n  }, {\n    label: 'Total Employees',\n    count: stats.totalEmployees,\n    icon: 'bi-person-badge-fill',\n    color: 'info'\n  }, {\n    label: 'Policy Holders',\n    count: stats.totalPolicyHolders,\n    icon: 'bi-shield-fill-check',\n    color: 'success'\n  }, {\n    label: 'Monthly Revenue',\n    count: `$${stats.monthlyRevenue.toLocaleString()}`,\n    icon: 'bi-currency-dollar',\n    color: 'warning'\n  }, {\n    label: 'Pending Policies',\n    count: stats.pendingPolicies,\n    icon: 'bi-hourglass-split',\n    color: 'warning'\n  }, {\n    label: 'Approved Policies',\n    count: stats.approvedPolicies,\n    icon: 'bi-check-circle-fill',\n    color: 'success'\n  }, {\n    label: 'Support Tickets',\n    count: stats.totalTickets,\n    icon: 'bi-ticket-detailed-fill',\n    color: 'info'\n  }, {\n    label: 'System Alerts',\n    count: stats.systemAlerts,\n    icon: 'bi-exclamation-triangle-fill',\n    color: 'danger'\n  }];\n  const quickActions = [{\n    title: 'Manage Users',\n    description: 'Add, edit, or remove users',\n    link: '/users',\n    icon: 'bi-people'\n  }, {\n    title: 'System Settings',\n    description: 'Configure system parameters',\n    link: '/system-settings',\n    icon: 'bi-gear'\n  }, {\n    title: 'View Reports',\n    description: 'Generate and view reports',\n    link: '/report-tool',\n    icon: 'bi-graph-up'\n  }, {\n    title: 'Manage Staff',\n    description: 'Employee management',\n    link: '/staff',\n    icon: 'bi-person-workspace'\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"fw-bold\",\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"lead\",\n        children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-primary fw-semibold\",\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this), \"! Here's your system overview.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: cardData.map((item, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: `shadow-sm border-0 bg-${item.color} text-white h-100`,\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"d-flex align-items-center justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"card-title mb-2\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"fw-bold\",\n                children: item.count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${item.icon} fs-2`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"fw-bold mb-4\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: quickActions.map((action, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm border-0 h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${action.icon} text-primary fs-1 mb-3`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-3\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              href: action.link,\n              className: \"w-100\",\n              children: \"Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"Recent System Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"list-group list-group-flush\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"New user registration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"John Doe registered as customer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"2 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Policy approved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Life insurance policy #LP-2024-001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"4 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: \"Support ticket resolved\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Ticket #ST-2024-045 closed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"6 hours ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"System Health\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Server Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-success\",\n                  children: \"Online\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Database\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-success\",\n                  children: \"Connected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Backup Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-warning\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Security Alerts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-danger\",\n                  children: stats.systemAlerts\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"QTpw6KxO6XfHUNY1kst13BY2iHs=\", false, function () {\n  return [useAuth];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "useAuth", "reportsAPI", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "stats", "setStats", "totalPolicyHolders", "pendingPolicies", "approvedPolicies", "deniedPolicies", "totalTickets", "pendingTickets", "closedTickets", "activeFields", "totalEmployees", "totalCustomers", "monthlyRevenue", "systemAlerts", "fetchDashboardData", "response", "getDashboardStats", "success", "overview", "data", "totalUsers", "activePolicies", "openTickets", "error", "console", "cardData", "label", "count", "icon", "color", "toLocaleString", "quickActions", "title", "description", "link", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "item", "idx", "lg", "md", "Body", "action", "variant", "href", "Header", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/dashboards/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button } from 'react-bootstrap';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI } from '../../services/api';\n\nconst AdminDashboard = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalPolicyHolders: 0,\n    pendingPolicies: 0,\n    approvedPolicies: 0,\n    deniedPolicies: 0,\n    totalTickets: 0,\n    pendingTickets: 0,\n    closedTickets: 0,\n    activeFields: 0,\n    totalEmployees: 0,\n    totalCustomers: 0,\n    monthlyRevenue: 0,\n    systemAlerts: 0,\n  });\n\n  // Fetch dashboard data\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        const response = await reportsAPI.getDashboardStats();\n        if (response.success) {\n          const { overview } = response.data;\n          setStats({\n            totalPolicyHolders: overview.totalUsers || 0,\n            pendingPolicies: overview.pendingPolicies || 0,\n            approvedPolicies: overview.activePolicies || 0,\n            deniedPolicies: 0, // Calculate from total - active - pending\n            totalTickets: overview.totalTickets || 0,\n            pendingTickets: overview.openTickets || 0,\n            closedTickets: (overview.totalTickets || 0) - (overview.openTickets || 0),\n            activeFields: 12, // Static for now\n            totalEmployees: overview.totalEmployees || 0,\n            totalCustomers: overview.totalCustomers || 0,\n            monthlyRevenue: overview.monthlyRevenue || 0,\n            systemAlerts: 3, // Static for now\n          });\n        }\n      } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        // Fallback to mock data\n        setStats({\n          totalPolicyHolders: 1250,\n          pendingPolicies: 45,\n          approvedPolicies: 980,\n          deniedPolicies: 125,\n          totalTickets: 180,\n          pendingTickets: 35,\n          closedTickets: 145,\n          activeFields: 12,\n          totalEmployees: 25,\n          totalCustomers: 1200,\n          monthlyRevenue: 125000,\n          systemAlerts: 3,\n        });\n      }\n    };\n\n    fetchDashboardData();\n  }, []);\n\n  const cardData = [\n    { label: 'Total Customers', count: stats.totalCustomers, icon: 'bi-people-fill', color: 'primary' },\n    { label: 'Total Employees', count: stats.totalEmployees, icon: 'bi-person-badge-fill', color: 'info' },\n    { label: 'Policy Holders', count: stats.totalPolicyHolders, icon: 'bi-shield-fill-check', color: 'success' },\n    { label: 'Monthly Revenue', count: `$${stats.monthlyRevenue.toLocaleString()}`, icon: 'bi-currency-dollar', color: 'warning' },\n    { label: 'Pending Policies', count: stats.pendingPolicies, icon: 'bi-hourglass-split', color: 'warning' },\n    { label: 'Approved Policies', count: stats.approvedPolicies, icon: 'bi-check-circle-fill', color: 'success' },\n    { label: 'Support Tickets', count: stats.totalTickets, icon: 'bi-ticket-detailed-fill', color: 'info' },\n    { label: 'System Alerts', count: stats.systemAlerts, icon: 'bi-exclamation-triangle-fill', color: 'danger' },\n  ];\n\n  const quickActions = [\n    { title: 'Manage Users', description: 'Add, edit, or remove users', link: '/users', icon: 'bi-people' },\n    { title: 'System Settings', description: 'Configure system parameters', link: '/system-settings', icon: 'bi-gear' },\n    { title: 'View Reports', description: 'Generate and view reports', link: '/report-tool', icon: 'bi-graph-up' },\n    { title: 'Manage Staff', description: 'Employee management', link: '/staff', icon: 'bi-person-workspace' },\n  ];\n\n  return (\n    <Container className=\"mt-4\">\n      <div className=\"mb-4\">\n        <h2 className=\"fw-bold\">Admin Dashboard</h2>\n        <p className=\"lead\">\n          Welcome back, <span className=\"text-primary fw-semibold\">{user.name}</span>! \n          Here's your system overview.\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <Row className=\"g-4 mb-5\">\n        {cardData.map((item, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>\n              <Card.Body className=\"d-flex align-items-center justify-content-between\">\n                <div>\n                  <h6 className=\"card-title mb-2\">{item.label}</h6>\n                  <h4 className=\"fw-bold\">{item.count}</h4>\n                </div>\n                <i className={`bi ${item.icon} fs-2`}></i>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Quick Actions */}\n      <Row>\n        <Col>\n          <h4 className=\"fw-bold mb-4\">Quick Actions</h4>\n        </Col>\n      </Row>\n      <Row className=\"g-4 mb-5\">\n        {quickActions.map((action, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className=\"shadow-sm border-0 h-100\">\n              <Card.Body className=\"text-center\">\n                <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>\n                <h5 className=\"fw-bold\">{action.title}</h5>\n                <p className=\"text-muted mb-3\">{action.description}</p>\n                <Button variant=\"primary\" href={action.link} className=\"w-100\">\n                  Access\n                </Button>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Recent Activity */}\n      <Row>\n        <Col lg={8}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">Recent System Activity</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"list-group list-group-flush\">\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">New user registration</h6>\n                    <small className=\"text-muted\">John Doe registered as customer</small>\n                  </div>\n                  <small className=\"text-muted\">2 hours ago</small>\n                </div>\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">Policy approved</h6>\n                    <small className=\"text-muted\">Life insurance policy #LP-2024-001</small>\n                  </div>\n                  <small className=\"text-muted\">4 hours ago</small>\n                </div>\n                <div className=\"list-group-item d-flex justify-content-between align-items-center\">\n                  <div>\n                    <h6 className=\"mb-1\">Support ticket resolved</h6>\n                    <small className=\"text-muted\">Ticket #ST-2024-045 closed</small>\n                  </div>\n                  <small className=\"text-muted\">6 hours ago</small>\n                </div>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col lg={4}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">System Health</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Server Status</span>\n                  <span className=\"badge bg-success\">Online</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Database</span>\n                  <span className=\"badge bg-success\">Connected</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Backup Status</span>\n                  <span className=\"badge bg-warning\">Pending</span>\n                </div>\n              </div>\n              <div className=\"mb-3\">\n                <div className=\"d-flex justify-content-between\">\n                  <span>Security Alerts</span>\n                  <span className=\"badge bg-danger\">{stats.systemAlerts}</span>\n                </div>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AACnE,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC;IACjCgB,kBAAkB,EAAE,CAAC;IACrBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA1B,SAAS,CAAC,MAAM;IACd,MAAM2B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMrB,UAAU,CAACsB,iBAAiB,CAAC,CAAC;QACrD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM;YAAEC;UAAS,CAAC,GAAGH,QAAQ,CAACI,IAAI;UAClClB,QAAQ,CAAC;YACPC,kBAAkB,EAAEgB,QAAQ,CAACE,UAAU,IAAI,CAAC;YAC5CjB,eAAe,EAAEe,QAAQ,CAACf,eAAe,IAAI,CAAC;YAC9CC,gBAAgB,EAAEc,QAAQ,CAACG,cAAc,IAAI,CAAC;YAC9ChB,cAAc,EAAE,CAAC;YAAE;YACnBC,YAAY,EAAEY,QAAQ,CAACZ,YAAY,IAAI,CAAC;YACxCC,cAAc,EAAEW,QAAQ,CAACI,WAAW,IAAI,CAAC;YACzCd,aAAa,EAAE,CAACU,QAAQ,CAACZ,YAAY,IAAI,CAAC,KAAKY,QAAQ,CAACI,WAAW,IAAI,CAAC,CAAC;YACzEb,YAAY,EAAE,EAAE;YAAE;YAClBC,cAAc,EAAEQ,QAAQ,CAACR,cAAc,IAAI,CAAC;YAC5CC,cAAc,EAAEO,QAAQ,CAACP,cAAc,IAAI,CAAC;YAC5CC,cAAc,EAAEM,QAAQ,CAACN,cAAc,IAAI,CAAC;YAC5CC,YAAY,EAAE,CAAC,CAAE;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACAtB,QAAQ,CAAC;UACPC,kBAAkB,EAAE,IAAI;UACxBC,eAAe,EAAE,EAAE;UACnBC,gBAAgB,EAAE,GAAG;UACrBC,cAAc,EAAE,GAAG;UACnBC,YAAY,EAAE,GAAG;UACjBC,cAAc,EAAE,EAAE;UAClBC,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE,EAAE;UAChBC,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE,IAAI;UACpBC,cAAc,EAAE,MAAM;UACtBC,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC;IAEDC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,QAAQ,GAAG,CACf;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE3B,KAAK,CAACW,cAAc;IAAEiB,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnG;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE3B,KAAK,CAACU,cAAc;IAAEkB,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAO,CAAC,EACtG;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE3B,KAAK,CAACE,kBAAkB;IAAE0B,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5G;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,IAAI3B,KAAK,CAACY,cAAc,CAACkB,cAAc,CAAC,CAAC,EAAE;IAAEF,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9H;IAAEH,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE3B,KAAK,CAACG,eAAe;IAAEyB,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzG;IAAEH,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE3B,KAAK,CAACI,gBAAgB;IAAEwB,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7G;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE3B,KAAK,CAACM,YAAY;IAAEsB,IAAI,EAAE,yBAAyB;IAAEC,KAAK,EAAE;EAAO,CAAC,EACvG;IAAEH,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE3B,KAAK,CAACa,YAAY;IAAEe,IAAI,EAAE,8BAA8B;IAAEC,KAAK,EAAE;EAAS,CAAC,CAC7G;EAED,MAAME,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,4BAA4B;IAAEC,IAAI,EAAE,QAAQ;IAAEN,IAAI,EAAE;EAAY,CAAC,EACvG;IAAEI,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,6BAA6B;IAAEC,IAAI,EAAE,kBAAkB;IAAEN,IAAI,EAAE;EAAU,CAAC,EACnH;IAAEI,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,2BAA2B;IAAEC,IAAI,EAAE,cAAc;IAAEN,IAAI,EAAE;EAAc,CAAC,EAC9G;IAAEI,KAAK,EAAE,cAAc;IAAEC,WAAW,EAAE,qBAAqB;IAAEC,IAAI,EAAE,QAAQ;IAAEN,IAAI,EAAE;EAAsB,CAAC,CAC3G;EAED,oBACEhC,OAAA,CAACR,SAAS;IAAC+C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACzBxC,OAAA;MAAKuC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBxC,OAAA;QAAIuC,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5C5C,OAAA;QAAGuC,SAAS,EAAC,MAAM;QAAAC,QAAA,GAAC,gBACJ,eAAAxC,OAAA;UAAMuC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAErC,IAAI,CAAC0C;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,kCAE7E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5C,OAAA,CAACP,GAAG;MAAC8C,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBX,QAAQ,CAACiB,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACtBhD,OAAA,CAACN,GAAG;QAACuD,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChBxC,OAAA,CAACL,IAAI;UAAC4C,SAAS,EAAE,yBAAyBQ,IAAI,CAACd,KAAK,mBAAoB;UAAAO,QAAA,eACtExC,OAAA,CAACL,IAAI,CAACwD,IAAI;YAACZ,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBACtExC,OAAA;cAAAwC,QAAA,gBACExC,OAAA;gBAAIuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEO,IAAI,CAACjB;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjD5C,OAAA;gBAAIuC,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEO,IAAI,CAAChB;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN5C,OAAA;cAAGuC,SAAS,EAAE,MAAMQ,IAAI,CAACf,IAAI;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GATeI,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5C,OAAA,CAACP,GAAG;MAAA+C,QAAA,eACFxC,OAAA,CAACN,GAAG;QAAA8C,QAAA,eACFxC,OAAA;UAAIuC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5C,OAAA,CAACP,GAAG;MAAC8C,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBL,YAAY,CAACW,GAAG,CAAC,CAACM,MAAM,EAAEJ,GAAG,kBAC5BhD,OAAA,CAACN,GAAG;QAACuD,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChBxC,OAAA,CAACL,IAAI;UAAC4C,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACxCxC,OAAA,CAACL,IAAI,CAACwD,IAAI;YAACZ,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAChCxC,OAAA;cAAGuC,SAAS,EAAE,MAAMa,MAAM,CAACpB,IAAI;YAA0B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D5C,OAAA;cAAIuC,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEY,MAAM,CAAChB;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3C5C,OAAA;cAAGuC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEY,MAAM,CAACf;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD5C,OAAA,CAACJ,MAAM;cAACyD,OAAO,EAAC,SAAS;cAACC,IAAI,EAAEF,MAAM,CAACd,IAAK;cAACC,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GAVeI,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5C,OAAA,CAACP,GAAG;MAAA+C,QAAA,gBACFxC,OAAA,CAACN,GAAG;QAACuD,EAAE,EAAE,CAAE;QAAAT,QAAA,eACTxC,OAAA,CAACL,IAAI;UAAC4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzBxC,OAAA,CAACL,IAAI,CAAC4D,MAAM;YAAAf,QAAA,eACVxC,OAAA;cAAIuC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACd5C,OAAA,CAACL,IAAI,CAACwD,IAAI;YAAAX,QAAA,eACRxC,OAAA;cAAKuC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxC,OAAA;gBAAKuC,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChFxC,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIuC,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/C5C,OAAA;oBAAOuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACN5C,OAAA;kBAAOuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChFxC,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIuC,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzC5C,OAAA;oBAAOuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACN5C,OAAA;kBAAOuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,gBAChFxC,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAIuC,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjD5C,OAAA;oBAAOuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACN5C,OAAA;kBAAOuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5C,OAAA,CAACN,GAAG;QAACuD,EAAE,EAAE,CAAE;QAAAT,QAAA,eACTxC,OAAA,CAACL,IAAI;UAAC4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACzBxC,OAAA,CAACL,IAAI,CAAC4D,MAAM;YAAAf,QAAA,eACVxC,OAAA;cAAIuC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACd5C,OAAA,CAACL,IAAI,CAACwD,IAAI;YAAAX,QAAA,gBACRxC,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBxC,OAAA;gBAAKuC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7CxC,OAAA;kBAAAwC,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1B5C,OAAA;kBAAMuC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBxC,OAAA;gBAAKuC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7CxC,OAAA;kBAAAwC,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrB5C,OAAA;kBAAMuC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBxC,OAAA;gBAAKuC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7CxC,OAAA;kBAAAwC,QAAA,EAAM;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1B5C,OAAA;kBAAMuC,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBxC,OAAA;gBAAKuC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7CxC,OAAA;kBAAAwC,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B5C,OAAA;kBAAMuC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEpC,KAAK,CAACa;gBAAY;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC1C,EAAA,CAxMID,cAAc;EAAA,QACDJ,OAAO;AAAA;AAAA2D,EAAA,GADpBvD,cAAc;AA0MpB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}