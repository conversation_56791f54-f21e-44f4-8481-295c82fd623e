import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import MainLayout from './layout/MainLayout';

import Dashboard from './Pages/Dashboard';
import Categories from './Pages/Categories';
import SubCategories from './Pages/SubCategories';
import TicketCategories from './Pages/TicketCategories';
import InsurancePolicy from './Pages/InsurancePolicy';
import Staff from './Pages/Staff';
import Users from './Pages/Users';
import PolicyHolder from './Pages/PolicyHolder';
import SupportTicket from './Pages/SupportTicket';
import ReportTool from './Pages/ReportTool';
import SystemSettings from './Pages/SystemSettings';

function App() {
  return (
    <BrowserRouter>
    
      <Routes>
        
        <Route path="/" element={<MainLayout />}>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="categories" element={<Categories />} />
          <Route path="subcategories" element={<SubCategories />} />
          <Route path="ticketcategories" element={<TicketCategories />} />
          <Route path="insurance-policy" element={<InsurancePolicy />} />
          <Route path="staff" element={<Staff />} />
          <Route path="users" element={<Users />} />
          <Route path="policy-holder" element={<PolicyHolder />} />
          <Route path="support-ticket" element={<SupportTicket />} />
          <Route path="report-tool" element={<ReportTool />} />
          <Route path="system-settings" element={<SystemSettings />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
