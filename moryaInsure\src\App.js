import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import MainLayout from './layout/MainLayout';
import ProtectedRoute from './components/ProtectedRoute';

import Dashboard from './Pages/Dashboard';
import Categories from './Pages/Categories';
import SubCategories from './Pages/SubCategories';
import TicketCategories from './Pages/TicketCategories';
import InsurancePolicy from './Pages/InsurancePolicy';
import Staff from './Pages/Staff';
import Users from './Pages/Users';
import PolicyHolder from './Pages/PolicyHolder';
import SupportTicket from './Pages/SupportTicket';
import ReportTool from './Pages/ReportTool';
import SystemSettings from './Pages/SystemSettings';
import About from './Pages/About';
import Services from './Pages/Services';
import Contact from './Pages/Contact';
import Login from './Pages/Login';
import Register from './Pages/Register';
import VerifyOTP from './Pages/VerifyOTP';
import Profile from './Pages/Profile';
import Claims from './Pages/Claims';
import ContactSupport from './Pages/ContactSupport';
import TaskManagement from './Pages/TaskManagement';
import MyTasks from './Pages/MyTasks';
import Settings from './Pages/Settings';

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/verify-otp" element={<VerifyOTP />} />

          {/* Protected routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }>
            <Route index element={<Dashboard />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="profile" element={<Profile />} />
            <Route path="settings" element={<Settings />} />
            <Route path="about" element={<About />} />
            <Route path="services" element={<Services />} />
            <Route path="contact" element={<Contact />} />

            {/* Admin only routes */}
            <Route path="categories" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <Categories />
              </ProtectedRoute>
            } />
            <Route path="subcategories" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <SubCategories />
              </ProtectedRoute>
            } />
            <Route path="ticketcategories" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <TicketCategories />
              </ProtectedRoute>
            } />
            <Route path="staff" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <Staff />
              </ProtectedRoute>
            } />
            <Route path="users" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <Users />
              </ProtectedRoute>
            } />
            <Route path="system-settings" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <SystemSettings />
              </ProtectedRoute>
            } />

            {/* Admin and Employee routes */}
            <Route path="insurance-policy" element={
              <ProtectedRoute requiredRoles={['admin', 'employee']}>
                <InsurancePolicy />
              </ProtectedRoute>
            } />
            <Route path="policy-holder" element={
              <ProtectedRoute requiredRoles={['admin', 'employee']}>
                <PolicyHolder />
              </ProtectedRoute>
            } />
            <Route path="support-ticket" element={
              <ProtectedRoute requiredRoles={['admin', 'employee']}>
                <SupportTicket />
              </ProtectedRoute>
            } />
            <Route path="report-tool" element={
              <ProtectedRoute requiredRoles={['admin', 'employee']}>
                <ReportTool />
              </ProtectedRoute>
            } />

            {/* Task Management routes */}
            <Route path="task-management" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <TaskManagement />
              </ProtectedRoute>
            } />
            <Route path="my-tasks" element={
              <ProtectedRoute requiredRoles={['employee']}>
                <MyTasks />
              </ProtectedRoute>
            } />

            {/* Customer routes */}
            <Route path="claims" element={
              <ProtectedRoute requiredRoles={['customer']}>
                <Claims />
              </ProtectedRoute>
            } />
            <Route path="contact-support" element={
              <ProtectedRoute requiredRoles={['customer', 'admin', 'employee']}>
                <ContactSupport />
              </ProtectedRoute>
            } />
          </Route>
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}

export default App;
