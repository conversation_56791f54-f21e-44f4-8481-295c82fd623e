const mongoose = require('mongoose');

const systemSettingsSchema = new mongoose.Schema({
  // Organization Information
  organizationName: {
    type: String,
    required: [true, 'Organization name is required'],
    trim: true,
    maxlength: [100, 'Organization name cannot exceed 100 characters']
  },
  organizationLogo: {
    type: String,
    default: null
  },
  
  // Contact Information
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^\+?[\d\s-()]+$/, 'Please provide a valid phone number']
  },
  website: {
    type: String,
    trim: true
  },
  
  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    zipCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      required: [true, 'Country is required'],
      default: 'India',
      trim: true
    }
  },
  
  // Business Settings
  businessHours: {
    monday: { start: String, end: String, closed: { type: Boolean, default: false } },
    tuesday: { start: String, end: String, closed: { type: Boolean, default: false } },
    wednesday: { start: String, end: String, closed: { type: Boolean, default: false } },
    thursday: { start: String, end: String, closed: { type: Boolean, default: false } },
    friday: { start: String, end: String, closed: { type: Boolean, default: false } },
    saturday: { start: String, end: String, closed: { type: Boolean, default: false } },
    sunday: { start: String, end: String, closed: { type: Boolean, default: true } }
  },
  
  // System Configuration
  systemConfig: {
    maintenanceMode: {
      type: Boolean,
      default: false
    },
    allowRegistration: {
      type: Boolean,
      default: true
    },
    requireEmailVerification: {
      type: Boolean,
      default: true
    },
    maxFileUploadSize: {
      type: Number,
      default: 10485760 // 10MB in bytes
    },
    allowedFileTypes: {
      type: [String],
      default: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']
    }
  },
  
  // Email Configuration
  emailConfig: {
    smtpHost: String,
    smtpPort: Number,
    smtpUser: String,
    smtpPassword: String,
    fromEmail: String,
    fromName: String
  },
  
  // Notification Settings
  notificationSettings: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    smsNotifications: {
      type: Boolean,
      default: false
    },
    pushNotifications: {
      type: Boolean,
      default: true
    }
  },
  
  // Security Settings
  securitySettings: {
    passwordMinLength: {
      type: Number,
      default: 6
    },
    passwordRequireSpecialChar: {
      type: Boolean,
      default: false
    },
    sessionTimeout: {
      type: Number,
      default: 3600 // 1 hour in seconds
    },
    maxLoginAttempts: {
      type: Number,
      default: 5
    },
    lockoutDuration: {
      type: Number,
      default: 7200 // 2 hours in seconds
    }
  },
  
  // Theme and Branding
  theme: {
    primaryColor: {
      type: String,
      default: '#007bff'
    },
    secondaryColor: {
      type: String,
      default: '#6c757d'
    },
    logoUrl: String,
    faviconUrl: String
  },
  
  // Currency and Localization
  localization: {
    currency: {
      type: String,
      default: 'INR'
    },
    currencySymbol: {
      type: String,
      default: '₹'
    },
    dateFormat: {
      type: String,
      default: 'DD/MM/YYYY'
    },
    timeFormat: {
      type: String,
      default: '24h'
    },
    timezone: {
      type: String,
      default: 'Asia/Kolkata'
    },
    language: {
      type: String,
      default: 'en'
    }
  },
  
  // Insurance Specific Settings
  insuranceSettings: {
    defaultPolicyTerm: {
      type: Number,
      default: 12 // months
    },
    gracePeriod: {
      type: Number,
      default: 30 // days
    },
    claimProcessingTime: {
      type: Number,
      default: 15 // days
    },
    autoRenewal: {
      type: Boolean,
      default: false
    }
  },
  
  // Backup and Maintenance
  backupSettings: {
    autoBackup: {
      type: Boolean,
      default: true
    },
    backupFrequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly'
    },
    retentionPeriod: {
      type: Number,
      default: 90 // days
    }
  },
  
  // Audit Trail
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastUpdatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Ensure only one settings document exists
systemSettingsSchema.index({}, { unique: true });

// Static method to get settings (creates default if none exist)
systemSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  
  if (!settings) {
    // Create default settings
    settings = await this.create({
      organizationName: 'Morya Insurance',
      email: '<EMAIL>',
      phone: '+91-9876543210',
      address: {
        street: '123 Business District',
        city: 'Mumbai',
        state: 'Maharashtra',
        zipCode: '400001',
        country: 'India'
      }
    });
  }
  
  return settings;
};

// Method to update settings
systemSettingsSchema.statics.updateSettings = async function(updates, updatedBy) {
  const settings = await this.getSettings();
  
  Object.keys(updates).forEach(key => {
    if (updates[key] !== undefined) {
      settings[key] = updates[key];
    }
  });
  
  settings.lastUpdatedBy = updatedBy;
  settings.lastUpdatedAt = new Date();
  
  return await settings.save();
};

module.exports = mongoose.model('SystemSettings', systemSettingsSchema);
