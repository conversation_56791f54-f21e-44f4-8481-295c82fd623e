import React, { useState, useEffect } from 'react';
import { Button, Table, Form, Modal } from 'react-bootstrap';
import { FaUser, FaEdit, FaTrash } from 'react-icons/fa';

const TicketCategories = () => {
  const [username] = useState('Sushil'); // Replace with dynamic login username
  const [ticketCategories, setTicketCategories] = useState([]);
  const [search, setSearch] = useState('');

  // New Category Modal
  const [showNewModal, setShowNewModal] = useState(false);
  const [newCategory, setNewCategory] = useState({
    insuranceType: '',
    ticketCategoryName: '',
    status: 'active',
  });

  // Import Modal
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState(null);

  useEffect(() => {
    // Dummy data to simulate API
    const dummy = [
    //   { id: 1, category: 'Health Insurance', name: 'Travel Cancellation', status: 'Active' },
    //   { id: 2, category: 'Car Insurance', name: 'Vehicle Damage', status: 'Active' },
     ];
    setTicketCategories(dummy);
  }, []);

  const handleNewCategoryChange = (e) => {
    setNewCategory({ ...newCategory, [e.target.name]: e.target.value });
  };

  const handleSaveCategory = () => {
    const newEntry = {
      id: ticketCategories.length + 1,
      category: newCategory.insuranceType,
      name: newCategory.ticketCategoryName,
      status: newCategory.status === 'active' ? 'Active' : 'Inactive',
    };
    setTicketCategories([...ticketCategories, newEntry]);
    setShowNewModal(false);
  };

  const filteredData = ticketCategories.filter((cat) =>
    cat.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="container-fluid p-3">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3 className="fw-bold text-uppercase">Ticket Categories</h3>
      </div>

      {/* Action Buttons */}
      <div className="mb-3 d-flex gap-2">
        <Button variant="primary" onClick={() => setShowNewModal(true)}>+ New</Button>
        <Button variant="secondary" onClick={() => setShowImportModal(true)}>Import</Button>
      </div>

      {/* Export Buttons */}
      <div className="mb-3 d-flex flex-wrap gap-2">
        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      {/* Search */}
      <div className="mb-3 w-100 w-md-50">
        <Form.Control
          type="text"
          placeholder="Search ticket categories..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {/* Table */}
      <Table bordered hover responsive className="shadow-sm">
        <thead className="table-primary">
          <tr>
            <th>Category Name</th>
            <th>Ticket Category Name</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filteredData.length > 0 ? (
            filteredData.map((cat) => (
              <tr key={cat.id}>
                <td>{cat.category}</td>
                <td>{cat.name}</td>
                <td>
                  <span className={`badge ${cat.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>
                    {cat.status}
                  </span>
                </td>
                <td>
                  <Button variant="primary" size="sm" className="me-2"><FaEdit /></Button>
                  <Button variant="danger" size="sm"><FaTrash /></Button>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan="4" className="text-center">No data available in database</td>
            </tr>
          )}
        </tbody>
      </Table>

      {/* Footer */}
      <div className="mt-2">
        <small className="text-muted">Showing  {filteredData.length} of {ticketCategories.length} entries</small>
      </div>

      {/* Add New Modal */}
      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Add Ticket Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Insurance Category</Form.Label>
            <Form.Select name="insuranceType" onChange={handleNewCategoryChange}>
              <option value="">Select</option>
              {/* <option value="Health Insurance">Health Insurance</option>
              <option value="Car Insurance">Car Insurance</option>
              <option value="Property Insurance">Property Insurance</option>
              <option value="Life Insurance">Life Insurance</option> */}
            </Form.Select>
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Ticket Category Name</Form.Label>
            <Form.Control
              type="text"
              name="ticketCategoryName"
              onChange={handleNewCategoryChange}
              placeholder="Enter ticket category name"
            />
          </Form.Group>
          <Form.Group>
            <Form.Label>Status</Form.Label>
            <div>
              <Form.Check
                inline
                label="Active"
                name="status"
                type="radio"
                value="active"
                checked={newCategory.status === 'active'}
                onChange={handleNewCategoryChange}
              />
              <Form.Check
                inline
                label="Inactive"
                name="status"
                type="radio"
                value="inactive"
                checked={newCategory.status === 'inactive'}
                onChange={handleNewCategoryChange}
              />
            </div>
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNewModal(false)}>Close</Button>
          <Button variant="success" onClick={handleSaveCategory}>Save Changes</Button>
        </Modal.Footer>
      </Modal>

      {/* Import Modal */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Import Ticket Categories</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group>
            <Form.Label>Choose File</Form.Label>
            <Form.Control type="file" onChange={(e) => setImportFile(e.target.files[0])} />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>Close</Button>
          <Button variant="primary" onClick={() => {
            console.log('Importing file:', importFile);
            setShowImportModal(false);
          }}>Upload</Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default TicketCategories;
