import React from 'react';
import { Button } from 'react-bootstrap';
import { FaSun, FaMoon } from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ className = '', style = {} }) => {
  const { theme, toggleTheme, isLoading } = useTheme();

  if (isLoading) {
    return null;
  }

  return (
    <Button
      variant="outline-secondary"
      onClick={toggleTheme}
      className={`theme-toggle ${className}`}
      style={style}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <FaMoon size={20} />
      ) : (
        <FaSun size={20} />
      )}
    </Button>
  );
};

export default ThemeToggle;
