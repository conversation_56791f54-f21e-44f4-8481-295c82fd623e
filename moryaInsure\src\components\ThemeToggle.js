import React from 'react';
import { But<PERSON> } from 'react-bootstrap';
import { FaSun, FaMoon } from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ className = '', style = {} }) => {
  const { theme, toggleTheme, isLoading } = useTheme();

  if (isLoading) {
    return null;
  }

  return (
    <button
      onClick={toggleTheme}
      className={`theme-toggle ${className}`}
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1050,
        border: 'none',
        borderRadius: '50%',
        width: '50px',
        height: '50px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'var(--card-bg)',
        color: 'var(--text-primary)',
        boxShadow: '0 2px 10px var(--card-shadow)',
        transition: 'all 0.3s ease',
        cursor: 'pointer',
        ...style
      }}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      onMouseEnter={(e) => {
        e.target.style.transform = 'scale(1.1)';
        e.target.style.boxShadow = '0 4px 15px var(--card-shadow)';
      }}
      onMouseLeave={(e) => {
        e.target.style.transform = 'scale(1)';
        e.target.style.boxShadow = '0 2px 10px var(--card-shadow)';
      }}
    >
      {theme === 'light' ? (
        <FaMoon size={18} />
      ) : (
        <FaSun size={18} />
      )}
    </button>
  );
};

export default ThemeToggle;
