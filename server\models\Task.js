const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Task title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Task description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  type: {
    type: String,
    enum: ['policy_review', 'claim_processing', 'customer_support', 'document_verification', 'follow_up', 'investigation', 'other'],
    required: [true, 'Task type is required']
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'],
    default: 'pending'
  },
  
  // Assignment details
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Task must be assigned to someone']
  },
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Task must have an assigner']
  },
  assignedAt: {
    type: Date,
    default: Date.now
  },
  
  // Due dates
  dueDate: {
    type: Date,
    required: [true, 'Due date is required']
  },
  estimatedHours: {
    type: Number,
    min: [0.5, 'Estimated hours must be at least 0.5'],
    max: [40, 'Estimated hours cannot exceed 40']
  },
  
  // Progress tracking
  progress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  actualHours: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // Related entities
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['policy', 'claim', 'ticket', 'user', 'policyholder', 'none'],
      default: 'none'
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'relatedEntity.entityType'
    },
    entityReference: String // For display purposes (policy number, claim number, etc.)
  },
  
  // Task completion
  completedAt: Date,
  completionNotes: {
    type: String,
    maxlength: [1000, 'Completion notes cannot exceed 1000 characters']
  },
  
  // Comments and updates
  comments: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: [500, 'Comment cannot exceed 500 characters']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Attachments
  attachments: [{
    filename: String,
    originalName: String,
    path: String,
    mimetype: String,
    size: Number,
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Time tracking
  timeEntries: [{
    startTime: Date,
    endTime: Date,
    duration: Number, // in minutes
    description: String,
    loggedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    loggedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Reminders and notifications
  reminders: [{
    reminderDate: Date,
    message: String,
    sent: {
      type: Boolean,
      default: false
    }
  }],
  
  // Tags for categorization
  tags: [{
    type: String,
    trim: true
  }],
  
  // Metadata
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: Date,
  deletedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
taskSchema.index({ assignedTo: 1, status: 1 });
taskSchema.index({ assignedBy: 1 });
taskSchema.index({ dueDate: 1 });
taskSchema.index({ priority: 1, status: 1 });
taskSchema.index({ type: 1 });
taskSchema.index({ createdAt: -1 });
taskSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });

// Virtual for overdue status
taskSchema.virtual('isOverdue').get(function() {
  return this.dueDate < new Date() && this.status !== 'completed' && this.status !== 'cancelled';
});

// Virtual for days until due
taskSchema.virtual('daysUntilDue').get(function() {
  const diffTime = this.dueDate - new Date();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for total time spent
taskSchema.virtual('totalTimeSpent').get(function() {
  return this.timeEntries.reduce((total, entry) => total + (entry.duration || 0), 0);
});

// Virtual for completion percentage based on time
taskSchema.virtual('timeBasedProgress').get(function() {
  if (!this.estimatedHours) return this.progress;
  const estimatedMinutes = this.estimatedHours * 60;
  const actualMinutes = this.totalTimeSpent;
  return Math.min(100, Math.round((actualMinutes / estimatedMinutes) * 100));
});

// Pre-save middleware
taskSchema.pre('save', function(next) {
  // Set completion date when status changes to completed
  if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
    this.progress = 100;
  }
  
  // Clear completion date if status changes from completed
  if (this.isModified('status') && this.status !== 'completed' && this.completedAt) {
    this.completedAt = undefined;
  }
  
  next();
});

// Static method to get tasks by assignee
taskSchema.statics.getByAssignee = function(assigneeId, options = {}) {
  const query = { 
    assignedTo: assigneeId, 
    isDeleted: false 
  };
  
  if (options.status) {
    query.status = options.status;
  }
  
  return this.find(query)
    .populate('assignedBy', 'firstName lastName email')
    .populate('assignedTo', 'firstName lastName email')
    .sort(options.sort || { dueDate: 1, priority: -1 })
    .limit(options.limit || 50);
};

// Static method to get tasks by assigner
taskSchema.statics.getByAssigner = function(assignerId, options = {}) {
  const query = { 
    assignedBy: assignerId, 
    isDeleted: false 
  };
  
  if (options.status) {
    query.status = options.status;
  }
  
  return this.find(query)
    .populate('assignedBy', 'firstName lastName email')
    .populate('assignedTo', 'firstName lastName email')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 50);
};

// Static method to get overdue tasks
taskSchema.statics.getOverdueTasks = function(assigneeId = null) {
  const query = {
    dueDate: { $lt: new Date() },
    status: { $nin: ['completed', 'cancelled'] },
    isDeleted: false
  };
  
  if (assigneeId) {
    query.assignedTo = assigneeId;
  }
  
  return this.find(query)
    .populate('assignedBy', 'firstName lastName email')
    .populate('assignedTo', 'firstName lastName email')
    .sort({ dueDate: 1 });
};

// Static method for task statistics
taskSchema.statics.getTaskStats = function(userId, role) {
  const matchQuery = { isDeleted: false };
  
  if (role === 'employee') {
    matchQuery.assignedTo = userId;
  } else if (role === 'admin') {
    // Admin can see all tasks or filter by assignedBy
    // matchQuery.assignedBy = userId; // Uncomment to filter by assigned by admin
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgProgress: { $avg: '$progress' }
      }
    }
  ]);
};

// Method to add comment
taskSchema.methods.addComment = function(authorId, content) {
  this.comments.push({
    author: authorId,
    content: content
  });
  return this.save();
};

// Method to log time
taskSchema.methods.logTime = function(userId, startTime, endTime, description = '') {
  const duration = Math.round((endTime - startTime) / (1000 * 60)); // minutes
  
  this.timeEntries.push({
    startTime,
    endTime,
    duration,
    description,
    loggedBy: userId
  });
  
  // Update actual hours
  this.actualHours = this.totalTimeSpent / 60;
  
  return this.save();
};

// Method to update progress
taskSchema.methods.updateProgress = function(progress, userId, notes = '') {
  this.progress = Math.max(0, Math.min(100, progress));
  
  if (notes) {
    this.addComment(userId, `Progress updated to ${progress}%: ${notes}`);
  }
  
  // Auto-complete if progress reaches 100%
  if (this.progress === 100 && this.status !== 'completed') {
    this.status = 'completed';
    this.completedAt = new Date();
  }
  
  return this.save();
};

module.exports = mongoose.model('Task', taskSchema);
