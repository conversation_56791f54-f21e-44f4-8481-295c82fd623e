const nodemailer = require('nodemailer');
const crypto = require('crypto');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: process.env.EMAIL_PORT || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify connection configuration
    this.verifyConnection();
  }

  async verifyConnection() {
    try {
      await this.transporter.verify();
      console.log('✓ Email service is ready');
    } catch (error) {
      console.error('✗ Email service error:', error.message);
    }
  }

  // Generate verification token
  generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Send email verification
  async sendVerificationEmail(email, token, userName) {
    const verificationUrl = `${process.env.CLIENT_URL}/verify-email?token=${token}`;
    
    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Verify Your Email Address - Morya Insurance',
      html: this.getVerificationEmailTemplate(userName, verificationUrl)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Verification email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending verification email:', error);
      return { success: false, error: error.message };
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(email, token, userName) {
    const resetUrl = `${process.env.CLIENT_URL}/reset-password?token=${token}`;
    
    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Password Reset Request - Morya Insurance',
      html: this.getPasswordResetEmailTemplate(userName, resetUrl)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Password reset email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, error: error.message };
    }
  }

  // Send welcome email
  async sendWelcomeEmail(email, userName, role) {
    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Welcome to Morya Insurance!',
      html: this.getWelcomeEmailTemplate(userName, role)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Welcome email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return { success: false, error: error.message };
    }
  }

  // Send policy notification
  async sendPolicyNotification(email, userName, policyData, type) {
    let subject, template;
    
    switch (type) {
      case 'approved':
        subject = 'Policy Approved - Morya Insurance';
        template = this.getPolicyApprovedTemplate(userName, policyData);
        break;
      case 'rejected':
        subject = 'Policy Update - Morya Insurance';
        template = this.getPolicyRejectedTemplate(userName, policyData);
        break;
      case 'renewal':
        subject = 'Policy Renewal Reminder - Morya Insurance';
        template = this.getPolicyRenewalTemplate(userName, policyData);
        break;
      default:
        subject = 'Policy Update - Morya Insurance';
        template = this.getGenericPolicyTemplate(userName, policyData, type);
    }

    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject,
      html: template
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Policy notification sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending policy notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send ticket notification
  async sendTicketNotification(email, userName, ticketData, type) {
    let subject, template;
    
    switch (type) {
      case 'created':
        subject = 'Support Ticket Created - Morya Insurance';
        template = this.getTicketCreatedTemplate(userName, ticketData);
        break;
      case 'updated':
        subject = 'Support Ticket Updated - Morya Insurance';
        template = this.getTicketUpdatedTemplate(userName, ticketData);
        break;
      case 'resolved':
        subject = 'Support Ticket Resolved - Morya Insurance';
        template = this.getTicketResolvedTemplate(userName, ticketData);
        break;
      default:
        subject = 'Support Ticket Update - Morya Insurance';
        template = this.getGenericTicketTemplate(userName, ticketData, type);
    }

    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject,
      html: template
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Ticket notification sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending ticket notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Email verification template
  getVerificationEmailTemplate(userName, verificationUrl) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .button { display: inline-block; padding: 12px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Morya Insurance</h1>
            </div>
            <div class="content">
                <h2>Welcome ${userName}!</h2>
                <p>Thank you for registering with Morya Insurance. To complete your registration, please verify your email address by clicking the button below:</p>
                <div style="text-align: center;">
                    <a href="${verificationUrl}" class="button">Verify Email Address</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #007bff;">${verificationUrl}</p>
                <p>This verification link will expire in 24 hours for security reasons.</p>
                <p>If you didn't create an account with us, please ignore this email.</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Morya Insurance. All rights reserved.</p>
                <p>This is an automated email. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Password reset template
  getPasswordResetEmailTemplate(userName, resetUrl) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .button { display: inline-block; padding: 12px 30px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Password Reset Request</h1>
            </div>
            <div class="content">
                <h2>Hello ${userName},</h2>
                <p>We received a request to reset your password for your Morya Insurance account.</p>
                <p>Click the button below to reset your password:</p>
                <div style="text-align: center;">
                    <a href="${resetUrl}" class="button">Reset Password</a>
                </div>
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #dc3545;">${resetUrl}</p>
                <p><strong>This link will expire in 1 hour for security reasons.</strong></p>
                <p>If you didn't request a password reset, please ignore this email or contact our support team if you have concerns.</p>
            </div>
            <div class="footer">
                <p>&copy; 2025 Morya Insurance. All rights reserved.</p>
                <p>This is an automated email. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Welcome email template
  getWelcomeEmailTemplate(userName, role) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Morya Insurance</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .button { display: inline-block; padding: 12px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Welcome to Morya Insurance!</h1>
            </div>
            <div class="content">
                <h2>Hello ${userName},</h2>
                <p>Welcome to Morya Insurance! Your account has been successfully created with the role of <strong>${role}</strong>.</p>
                <p>You can now access our platform and start managing your insurance needs.</p>
                <div style="text-align: center;">
                    <a href="${process.env.CLIENT_URL}/login" class="button">Login to Your Account</a>
                </div>
                <p>If you have any questions or need assistance, our support team is here to help.</p>
                <p>Thank you for choosing Morya Insurance!</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Morya Insurance. All rights reserved.</p>
                <p>Contact us: <EMAIL> | 1-800-MORYA-INS</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Policy approved template
  getPolicyApprovedTemplate(userName, policyData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Policy Approved</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .policy-details { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Policy Approved!</h1>
            </div>
            <div class="content">
                <h2>Congratulations ${userName}!</h2>
                <p>We're pleased to inform you that your insurance policy has been approved.</p>
                <div class="policy-details">
                    <h3>Policy Details:</h3>
                    <p><strong>Policy Number:</strong> ${policyData.policyNumber}</p>
                    <p><strong>Policy Type:</strong> ${policyData.type}</p>
                    <p><strong>Coverage Amount:</strong> $${policyData.coverageAmount?.toLocaleString()}</p>
                    <p><strong>Premium:</strong> $${policyData.premiumAmount}</p>
                    <p><strong>Start Date:</strong> ${new Date(policyData.startDate).toLocaleDateString()}</p>
                </div>
                <p>Your policy is now active and you're covered! You can view your policy details and download documents from your dashboard.</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Morya Insurance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Ticket created template
  getTicketCreatedTemplate(userName, ticketData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Support Ticket Created</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .ticket-details { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Support Ticket Created</h1>
            </div>
            <div class="content">
                <h2>Hello ${userName},</h2>
                <p>Your support ticket has been successfully created. Our team will review it and respond as soon as possible.</p>
                <div class="ticket-details">
                    <h3>Ticket Details:</h3>
                    <p><strong>Ticket Number:</strong> ${ticketData.ticketNumber}</p>
                    <p><strong>Subject:</strong> ${ticketData.title}</p>
                    <p><strong>Priority:</strong> ${ticketData.priority}</p>
                    <p><strong>Status:</strong> ${ticketData.status}</p>
                    <p><strong>Created:</strong> ${new Date(ticketData.createdAt).toLocaleString()}</p>
                </div>
                <p>You can track the progress of your ticket in your dashboard.</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Morya Insurance. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Send generic notification
  async sendGenericNotification(email, userName, notificationData) {
    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: notificationData.title,
      html: this.getGenericNotificationTemplate(userName, notificationData)
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('Generic notification sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending generic notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Generic notification template
  getGenericNotificationTemplate(userName, data) {
    const priorityColors = {
      low: '#6c757d',
      medium: '#007bff',
      high: '#fd7e14',
      urgent: '#dc3545'
    };

    const color = priorityColors[data.priority] || '#007bff';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.title}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: ${color}; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px; background: #f8f9fa; }
            .button { display: inline-block; padding: 12px 30px; background: ${color}; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .priority { display: inline-block; padding: 4px 8px; background: ${color}; color: white; border-radius: 3px; font-size: 12px; text-transform: uppercase; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${data.title}</h1>
                <span class="priority">${data.priority} Priority</span>
            </div>
            <div class="content">
                <h2>Hello ${userName},</h2>
                <p>${data.message}</p>
                ${data.actionUrl && data.actionText ? `
                <div style="text-align: center;">
                    <a href="${data.actionUrl}" class="button">${data.actionText}</a>
                </div>
                ` : ''}
                <p>You can manage your account and view all updates in your dashboard.</p>
            </div>
            <div class="footer">
                <p>&copy; 2024 Morya Insurance. All rights reserved.</p>
                <p>This is an automated notification. Please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Generic templates for other notifications
  getPolicyRejectedTemplate(userName, policyData) {
    return this.getGenericPolicyTemplate(userName, policyData, 'rejected');
  }

  getPolicyRenewalTemplate(userName, policyData) {
    return this.getGenericPolicyTemplate(userName, policyData, 'renewal');
  }

  getTicketUpdatedTemplate(userName, ticketData) {
    return this.getGenericTicketTemplate(userName, ticketData, 'updated');
  }

  getTicketResolvedTemplate(userName, ticketData) {
    return this.getGenericTicketTemplate(userName, ticketData, 'resolved');
  }

  getGenericPolicyTemplate(userName, policyData, type) {
    return `<p>Policy ${type} notification for ${userName}</p>`;
  }

  getGenericTicketTemplate(userName, ticketData, type) {
    return `<p>Ticket ${type} notification for ${userName}</p>`;
  }

  // Send OTP email
  async sendOTPEmail(email, otp, firstName, purpose = 'registration') {
    let subject, template;

    if (purpose === 'registration') {
      subject = 'Verify Your Email - Insurance Portal Registration';
      template = this.getOTPEmailTemplate(firstName, otp, purpose);
    } else if (purpose === 'login') {
      subject = 'Login Verification - Insurance Portal';
      template = this.getOTPEmailTemplate(firstName, otp, purpose);
    }

    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: subject,
      html: template
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log(`OTP email sent to ${email}:`, info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending OTP email:', error);
      return { success: false, error: error.message };
    }
  }

  // Send welcome email
  async sendWelcomeEmail(email, firstName, role) {
    const subject = `Welcome to Insurance Portal - ${role.charAt(0).toUpperCase() + role.slice(1)} Account`;
    const template = this.getWelcomeEmailTemplate(firstName, role);

    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: subject,
      html: template
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log(`Welcome email sent to ${email}:`, info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return { success: false, error: error.message };
    }
  }

  // Send role change notification
  async sendRoleChangeEmail(email, firstName, oldRole, newRole, changedBy) {
    const subject = 'Account Role Updated - Insurance Portal';
    const template = this.getRoleChangeEmailTemplate(firstName, oldRole, newRole, changedBy);

    const mailOptions = {
      from: `"Morya Insurance" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: subject,
      html: template
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log(`Role change email sent to ${email}:`, info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Error sending role change email:', error);
      return { success: false, error: error.message };
    }
  }

  // OTP Email Template
  getOTPEmailTemplate(firstName, otp, purpose) {
    const isRegistration = purpose === 'registration';
    const headerColor = isRegistration ? '#007bff' : '#28a745';
    const title = isRegistration ? 'Welcome to Insurance Portal' : 'Login Verification';
    const message = isRegistration
      ? 'Thank you for registering with our Insurance Portal. To complete your registration, please verify your email address using the OTP below:'
      : 'Someone is trying to log in to your Insurance Portal account. Please use the OTP below to complete your login:';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: ${headerColor}; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 30px; background: #f9f9f9; border-radius: 0 0 8px 8px; }
          .otp-box { background: #fff; border: 2px solid ${headerColor}; padding: 25px; text-align: center; margin: 25px 0; border-radius: 8px; }
          .otp-code { font-size: 36px; font-weight: bold; color: ${headerColor}; letter-spacing: 8px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${title}</h1>
          </div>
          <div class="content">
            <h2>Hello ${firstName}!</h2>
            <p>${message}</p>

            <div class="otp-box">
              <p style="margin: 0; font-size: 18px;">Your verification code is:</p>
              <div class="otp-code">${otp}</div>
              <p style="margin: 0; color: #666;"><small>This code will expire in 10 minutes</small></p>
            </div>

            ${!isRegistration ? `
              <div class="warning">
                <strong>⚠️ Security Notice:</strong> If this wasn't you, please secure your account immediately by changing your password.
              </div>
            ` : ''}

            <p>If you didn't request this ${isRegistration ? 'registration' : 'login'}, please ignore this email.</p>

            <p>Best regards,<br><strong>Morya Insurance Team</strong></p>
          </div>
          <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>© 2024 Morya Insurance. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Welcome Email Template
  getWelcomeEmailTemplate(firstName, role) {
    let welcomeMessage = '';
    let features = '';

    switch (role) {
      case 'admin':
        welcomeMessage = 'You have full administrative access to manage the insurance portal, users, policies, and system settings.';
        features = `
          <div class="feature-item">🏢 Manage organization settings and system configuration</div>
          <div class="feature-item">👥 Manage users and assign roles</div>
          <div class="feature-item">📊 View comprehensive reports and analytics</div>
          <div class="feature-item">🎯 Assign tasks to employees</div>
          <div class="feature-item">💰 Monitor revenue and profitability</div>
        `;
        break;
      case 'employee':
        welcomeMessage = 'You can now manage assigned policies, handle customer support tickets, and access employee tools.';
        features = `
          <div class="feature-item">📋 Manage assigned policies and applications</div>
          <div class="feature-item">🎫 Handle customer support tickets</div>
          <div class="feature-item">👥 Manage assigned policy holders</div>
          <div class="feature-item">📈 Generate performance reports</div>
          <div class="feature-item">📊 Track your productivity metrics</div>
        `;
        break;
      case 'customer':
        welcomeMessage = 'You can now apply for insurance policies, track your applications, and manage your account.';
        features = `
          <div class="feature-item">🛡️ Apply for insurance policies</div>
          <div class="feature-item">📄 Track your policy applications</div>
          <div class="feature-item">💬 Submit support tickets</div>
          <div class="feature-item">👤 Manage your profile and preferences</div>
          <div class="feature-item">📞 Contact customer support</div>
        `;
        break;
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #17a2b8; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 30px; background: #f9f9f9; border-radius: 0 0 8px 8px; }
          .welcome-box { background: #fff; border-left: 4px solid #17a2b8; padding: 25px; margin: 25px 0; border-radius: 0 8px 8px 0; }
          .features { background: #fff; padding: 25px; margin: 25px 0; border-radius: 8px; }
          .feature-item { margin: 12px 0; padding: 12px; background: #e9ecef; border-radius: 6px; border-left: 3px solid #17a2b8; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
          .cta-button { display: inline-block; background: #17a2b8; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to Morya Insurance!</h1>
          </div>
          <div class="content">
            <div class="welcome-box">
              <h2>Hello ${firstName}!</h2>
              <p><strong>Congratulations!</strong> Your ${role.charAt(0).toUpperCase() + role.slice(1)} account has been successfully activated.</p>
              <p>${welcomeMessage}</p>
            </div>

            <div class="features">
              <h3>🚀 What you can do now:</h3>
              ${features}
            </div>

            <div style="text-align: center;">
              <a href="${process.env.CLIENT_URL}/login" class="cta-button">Login to Your Account</a>
            </div>

            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

            <p>Best regards,<br><strong>Morya Insurance Team</strong></p>
          </div>
          <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>© 2024 Morya Insurance. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Role Change Email Template
  getRoleChangeEmailTemplate(firstName, oldRole, newRole, changedBy) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ffc107; color: #212529; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 30px; background: #f9f9f9; border-radius: 0 0 8px 8px; }
          .role-change-box { background: #fff; border: 2px solid #ffc107; padding: 25px; text-align: center; margin: 25px 0; border-radius: 8px; }
          .role-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; margin: 8px; font-weight: bold; }
          .old-role { background: #dc3545; color: white; }
          .new-role { background: #28a745; color: white; }
          .arrow { font-size: 24px; margin: 0 10px; color: #ffc107; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
          .employee-welcome { background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 25px 0; }
          .cta-button { display: inline-block; background: #ffc107; color: #212529; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔄 Account Role Updated</h1>
          </div>
          <div class="content">
            <h2>Hello ${firstName}!</h2>
            <p>Your account role has been updated by an administrator.</p>

            <div class="role-change-box">
              <p style="font-size: 18px; margin-bottom: 20px;"><strong>Role Change Details:</strong></p>
              <div>
                <span class="role-badge old-role">${oldRole.charAt(0).toUpperCase() + oldRole.slice(1)}</span>
                <span class="arrow">→</span>
                <span class="role-badge new-role">${newRole.charAt(0).toUpperCase() + newRole.slice(1)}</span>
              </div>
              <p style="margin-top: 20px; color: #666;"><small>Changed by: ${changedBy}</small></p>
            </div>

            ${newRole === 'employee' ? `
              <div class="employee-welcome">
                <h3 style="color: #155724; margin-top: 0;">🎉 Welcome to the Team!</h3>
                <p style="color: #155724; margin-bottom: 15px;"><strong>You are now an employee!</strong> You have access to:</p>
                <ul style="color: #155724; margin: 0;">
                  <li>Employee dashboard with assigned tasks</li>
                  <li>Policy management tools</li>
                  <li>Customer support ticket system</li>
                  <li>Performance reporting tools</li>
                  <li>Task assignment and tracking</li>
                </ul>
              </div>
            ` : ''}

            <div style="text-align: center;">
              <a href="${process.env.CLIENT_URL}/login" class="cta-button">Login to Access New Features</a>
            </div>

            <p>If you have any questions about this change, please contact your administrator.</p>

            <p>Best regards,<br><strong>Morya Insurance Team</strong></p>
          </div>
          <div class="footer">
            <p>This is an automated email. Please do not reply to this message.</p>
            <p>© 2024 Morya Insurance. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new EmailService();
