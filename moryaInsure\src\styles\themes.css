/* ===== THEME VARIABLES ===== */

/* Light Theme */
:root.theme-light {
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-accent: #f1f3f4;
  
  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --text-inverse: #ffffff;
  
  /* Border Colors */
  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
  --border-accent: #ced4da;
  
  /* Card & Component Colors */
  --card-bg: #ffffff;
  --card-border: #dee2e6;
  --card-shadow: rgba(0, 0, 0, 0.1);
  
  /* Sidebar Colors */
  --sidebar-bg: #343a40;
  --sidebar-text: #ffffff;
  --sidebar-hover: #495057;
  --sidebar-active: #007bff;
  
  /* Header Colors */
  --header-bg: #ffffff;
  --header-text: #212529;
  --header-border: #dee2e6;
  
  /* Button Colors */
  --btn-primary-bg: #007bff;
  --btn-primary-border: #007bff;
  --btn-secondary-bg: #6c757d;
  --btn-secondary-border: #6c757d;
  
  /* Form Colors */
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --input-focus-border: #80bdff;
  --input-text: #495057;
  
  /* Table Colors */
  --table-bg: #ffffff;
  --table-stripe: #f8f9fa;
  --table-hover: #f5f5f5;
  --table-border: #dee2e6;
  
  /* Modal Colors */
  --modal-bg: #ffffff;
  --modal-backdrop: rgba(0, 0, 0, 0.5);
  
  /* Status Colors */
  --success-bg: #d4edda;
  --success-text: #155724;
  --warning-bg: #fff3cd;
  --warning-text: #856404;
  --danger-bg: #f8d7da;
  --danger-text: #721c24;
  --info-bg: #d1ecf1;
  --info-text: #0c5460;
}

/* Dark Theme */
:root.theme-dark {
  /* Background Colors */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --bg-accent: #404040;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --text-inverse: #000000;
  
  /* Border Colors */
  --border-primary: #404040;
  --border-secondary: #4a4a4a;
  --border-accent: #555555;
  
  /* Card & Component Colors */
  --card-bg: #2d2d2d;
  --card-border: #404040;
  --card-shadow: rgba(0, 0, 0, 0.3);
  
  /* Sidebar Colors */
  --sidebar-bg: #1e1e1e;
  --sidebar-text: #ffffff;
  --sidebar-hover: #333333;
  --sidebar-active: #0d6efd;
  
  /* Header Colors */
  --header-bg: #2d2d2d;
  --header-text: #ffffff;
  --header-border: #404040;
  
  /* Button Colors */
  --btn-primary-bg: #0d6efd;
  --btn-primary-border: #0d6efd;
  --btn-secondary-bg: #6c757d;
  --btn-secondary-border: #6c757d;
  
  /* Form Colors */
  --input-bg: #3a3a3a;
  --input-border: #555555;
  --input-focus-border: #86b7fe;
  --input-text: #ffffff;
  
  /* Table Colors */
  --table-bg: #2d2d2d;
  --table-stripe: #3a3a3a;
  --table-hover: #404040;
  --table-border: #404040;
  
  /* Modal Colors */
  --modal-bg: #2d2d2d;
  --modal-backdrop: rgba(0, 0, 0, 0.7);
  
  /* Status Colors */
  --success-bg: #0f2419;
  --success-text: #75dd88;
  --warning-bg: #2b1f0d;
  --warning-text: #ffda6a;
  --danger-bg: #2c0b0e;
  --danger-text: #ea868f;
  --info-bg: #055160;
  --info-text: #6edff6;
}

/* ===== GLOBAL THEME STYLES ===== */

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Cards */
.card {
  background-color: var(--card-bg) !important;
  border-color: var(--card-border) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 0.125rem 0.25rem var(--card-shadow) !important;
}

.card-header {
  background-color: var(--bg-secondary) !important;
  border-bottom-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.card-body {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
}

/* Tables */
.table {
  --bs-table-bg: var(--table-bg);
  --bs-table-color: var(--text-primary);
  --bs-table-border-color: var(--table-border);
  --bs-table-striped-bg: var(--table-stripe);
  --bs-table-hover-bg: var(--table-hover);
}

.table thead th {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

/* Forms */
.form-control {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--input-text) !important;
}

.form-control:focus {
  background-color: var(--input-bg) !important;
  border-color: var(--input-focus-border) !important;
  color: var(--input-text) !important;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.form-select {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--input-text) !important;
}

.form-label {
  color: var(--text-primary) !important;
}

/* Modals */
.modal-content {
  background-color: var(--modal-bg) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-primary) !important;
}

.modal-header {
  border-bottom-color: var(--border-primary) !important;
}

.modal-footer {
  border-top-color: var(--border-primary) !important;
}

.modal-backdrop {
  background-color: var(--modal-backdrop) !important;
}

/* Sidebar */
.sidebar {
  background-color: var(--sidebar-bg) !important;
  color: var(--sidebar-text) !important;
}

.sidebar .nav-link {
  color: var(--sidebar-text) !important;
}

.sidebar .nav-link:hover {
  background-color: var(--sidebar-hover) !important;
}

.sidebar .nav-link.active {
  background-color: var(--sidebar-active) !important;
}

/* Header */
.navbar {
  background-color: var(--header-bg) !important;
  border-bottom: 1px solid var(--header-border) !important;
}

.navbar-brand,
.navbar-nav .nav-link {
  color: var(--header-text) !important;
}

/* Alerts */
.alert-success {
  background-color: var(--success-bg) !important;
  color: var(--success-text) !important;
  border-color: var(--success-text) !important;
}

.alert-warning {
  background-color: var(--warning-bg) !important;
  color: var(--warning-text) !important;
  border-color: var(--warning-text) !important;
}

.alert-danger {
  background-color: var(--danger-bg) !important;
  color: var(--danger-text) !important;
  border-color: var(--danger-text) !important;
}

.alert-info {
  background-color: var(--info-bg) !important;
  color: var(--info-text) !important;
  border-color: var(--info-text) !important;
}

/* Dropdowns */
.dropdown-menu {
  background-color: var(--card-bg) !important;
  border-color: var(--border-primary) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Badges */
.badge {
  color: var(--text-inverse) !important;
}

/* Borders */
.border {
  border-color: var(--border-primary) !important;
}

.border-top {
  border-top-color: var(--border-primary) !important;
}

.border-bottom {
  border-bottom-color: var(--border-primary) !important;
}

/* Text Colors */
.text-muted {
  color: var(--text-muted) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

/* Theme Toggle Button */
.theme-toggle {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 1050 !important;
  border: none !important;
  border-radius: 50% !important;
  width: 50px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 2px 10px var(--card-shadow) !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  outline: none !important;
}

.theme-toggle:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 15px var(--card-shadow) !important;
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
}

.theme-toggle:focus {
  outline: 2px solid var(--btn-primary-bg) !important;
  outline-offset: 2px !important;
}

.theme-toggle:active {
  transform: scale(0.95) !important;
}

/* Theme toggle animation */
.theme-toggle svg {
  transition: transform 0.3s ease !important;
}

.theme-toggle:hover svg {
  transform: rotate(15deg) !important;
}

/* Real-time Connection Status */
.connection-status {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 1040 !important;
  padding: 8px 12px !important;
  border-radius: 20px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  background-color: var(--card-bg) !important;
  box-shadow: 0 2px 8px var(--card-shadow) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  white-space: nowrap !important;
}

.connection-status.connected {
  color: var(--success-text) !important;
  border: 1px solid var(--success-text) !important;
}

.connection-status.disconnected {
  color: var(--danger-text) !important;
  border: 1px solid var(--danger-text) !important;
}

.connection-status:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px var(--card-shadow) !important;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
