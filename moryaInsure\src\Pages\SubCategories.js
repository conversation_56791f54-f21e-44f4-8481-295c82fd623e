import React, { useEffect, useState } from 'react';
import {
  Button, Table, Form, Modal, Dropdown,
} from 'react-bootstrap';
import { FaUser, FaEdit, FaTrash } from 'react-icons/fa';

const SubCategories = () => {
  const [username] = useState('Sushil');
  const [subCategories, setSubCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Modal states
  const [showNewModal, setShowNewModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);

  // New form state
  const [newSubCategory, setNewSubCategory] = useState({
    name: '',
    category: '',
    status: 'Active',
  });

   const categoriesList = [];
  //  ['Health Insurance', 'Car Insurance', 'Life Insurance', 'Property Insurance']

  useEffect(() => {
    const dummyData = [
    //   { id: 1, name: 'Medic Care', category: 'Health Insurance', status: 'Active' },
    //   { id: 2, name: 'Term Life Insurance', category: 'Life Insurance', status: 'Active' },
    //   { id: 3, name: 'Auto Premium', category: 'Car Insurance', status: 'Active' },
    //   { id: 4, name: 'Homeowners Insurance', category: 'Property Insurance', status: 'Active' },
    //   { id: 5, name: 'Comprehensive Coverage', category: 'Car Insurance', status: 'Active' },
     ];
    setSubCategories(dummyData);
  }, []);

  const filteredSubCategories = subCategories.filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase()) &&
    (selectedCategory === 'All' || item.category === selectedCategory)
  );

  const handleNewInputChange = (e) => {
    const { name, value } = e.target;
    setNewSubCategory((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddSubCategory = () => {
    const newEntry = {
      id: subCategories.length + 1,
      ...newSubCategory,
    };
    setSubCategories([...subCategories, newEntry]);
    setShowNewModal(false);
    setNewSubCategory({ name: '', category: '', status: 'Active' });
  };

  const handleFileUpload = () => {
    if (selectedFile) {
      alert(`File uploaded: ${selectedFile.name}`);
      setShowImportModal(false);
      setSelectedFile(null);
    } else {
      alert('Please select a file first.');
    }
  };

  return (
    <div className="container-fluid p-3">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3 className="fw-bold text-uppercase">Insurance Sub Categories</h3>
      </div>

      {/* Action buttons */}
      <div className="mb-3 d-flex gap-2">
        <Button variant="primary" onClick={() => setShowNewModal(true)}>+ New</Button>
        <Button variant="secondary" onClick={() => setShowImportModal(true)}>Import</Button>
      </div>

      {/* Export + Filter */}
      <div className="mb-3 d-flex flex-wrap gap-2 align-items-center">
        <Dropdown onSelect={(val) => setSelectedCategory(val)}>
          <Dropdown.Toggle variant="outline-dark" size="sm">
            {selectedCategory}
          </Dropdown.Toggle>
          <Dropdown.Menu>
            <Dropdown.Item eventKey="All">All</Dropdown.Item>
            {categoriesList.map((cat, idx) => (
              <Dropdown.Item key={idx} eventKey={cat}>{cat}</Dropdown.Item>
            ))}
          </Dropdown.Menu>
        </Dropdown>

        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      {/* Search */}
      <div className="mb-3" style={{ maxWidth: '300px' }}>
        <Form.Control
          type="text"
          placeholder="Search SubCategories..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {/* Table */}
      <Table bordered hover responsive className="shadow-sm">
        <thead className="table-primary">
          <tr>
            <th>Sub Category Name</th>
            <th>Category</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filteredSubCategories.map((sub) => (
            <tr key={sub.id}>
              <td>{sub.name}</td>
              <td>{sub.category}</td>
              <td><span className="badge bg-success">{sub.status}</span></td>
              <td>
                <Button variant="primary" size="sm" className="me-2">
                  <FaEdit />
                </Button>
                <Button variant="danger" size="sm">
                  <FaTrash />
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      {/* Modal - New SubCategory */}
      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Add New Sub Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Sub Category Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={newSubCategory.name}
                onChange={handleNewInputChange}
                placeholder="Enter subcategory name"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Category</Form.Label>
              <Form.Select
                name="category"
                value={newSubCategory.category}
                onChange={handleNewInputChange}
              >
                <option value="">-- Select Category --</option>
                {categoriesList.map((cat, idx) => (
                  <option key={idx} value={cat}>{cat}</option>
                ))}
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Status</Form.Label>
              <Form.Select
                name="status"
                value={newSubCategory.status}
                onChange={handleNewInputChange}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </Form.Select>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNewModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleAddSubCategory}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal - Import File */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Import SubCategories</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group>
            <Form.Label>Select file</Form.Label>
            <Form.Control
              type="file"
              accept=".csv, .xlsx"
              onChange={(e) => setSelectedFile(e.target.files[0])}
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleFileUpload}>
            Upload
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SubCategories;
