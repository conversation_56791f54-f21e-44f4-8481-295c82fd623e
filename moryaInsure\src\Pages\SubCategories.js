import React, { useEffect, useState } from 'react';
import {
  Button, Table, Form, Modal, Container, Row, Col, Card, Alert, Spinner
} from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';
import { subCategoriesAPI, categoriesAPI } from '../services/api';
import { useRealtime } from '../contexts/RealtimeContext';

const SubCategories = () => {
  const [subCategories, setSubCategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Real-time updates
  const { subscribeToUpdates } = useRealtime();

  // Modal states
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  // Form states
  const [newSubCategory, setNewSubCategory] = useState({
    name: '',
    description: '',
    category: '',
    code: '',
    isActive: true,
  });
  const [editSubCategory, setEditSubCategory] = useState(null);

  useEffect(() => {
    fetchSubCategories();
    fetchCategories();

    // Subscribe to real-time updates
    const unsubscribeSubCategories = subscribeToUpdates('subcategory', (updateData) => {
      const { type, data } = updateData;

      setSubCategories(prev => {
        switch (type) {
          case 'created':
            return [...prev, data];
          case 'updated':
            return prev.map(item => item._id === data._id ? { ...item, ...data } : item);
          case 'deleted':
            return prev.filter(item => item._id !== data._id);
          default:
            return prev;
        }
      });

      // Show success message for real-time updates
      if (type === 'created') {
        setSuccess('New subcategory added!');
      } else if (type === 'updated') {
        setSuccess('Subcategory updated!');
      } else if (type === 'deleted') {
        setSuccess('Subcategory deleted!');
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);
    });

    const unsubscribeCategories = subscribeToUpdates('category', (updateData) => {
      const { type, data } = updateData;

      setCategories(prev => {
        switch (type) {
          case 'created':
            return [...prev, data];
          case 'updated':
            return prev.map(item => item._id === data._id ? { ...item, ...data } : item);
          case 'deleted':
            return prev.filter(item => item._id !== data._id);
          default:
            return prev;
        }
      });
    });

    return () => {
      unsubscribeSubCategories();
      unsubscribeCategories();
    };
  }, [subscribeToUpdates]);

  const fetchSubCategories = async () => {
    try {
      setLoading(true);
      const response = await subCategoriesAPI.getSubCategories();
      if (response.success) {
        setSubCategories(response.data.subcategories || []);
      } else {
        setError('Failed to fetch subcategories');
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      setError('Failed to fetch subcategories');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesAPI.getCategories();
      if (response.success) {
        setCategories(response.data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const filteredSubCategories = subCategories.filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase()) &&
    (selectedCategory === 'All' || item.category?.name === selectedCategory)
  );

  const handleNewInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewSubCategory((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddSubCategory = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await subCategoriesAPI.createSubCategory(newSubCategory);
      if (response.success) {
        setSuccess('Subcategory created successfully!');
        setNewSubCategory({ name: '', description: '', category: '', code: '', isActive: true });
        setShowNewModal(false);
        fetchSubCategories();
      } else {
        setError(response.message || 'Failed to create subcategory');
      }
    } catch (error) {
      console.error('Error creating subcategory:', error);
      setError('Failed to create subcategory');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditSubCategory = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await subCategoriesAPI.updateSubCategory(editSubCategory._id, editSubCategory);
      if (response.success) {
        setSuccess('Subcategory updated successfully!');
        setShowEditModal(false);
        setEditSubCategory(null);
        fetchSubCategories();
      } else {
        setError(response.message || 'Failed to update subcategory');
      }
    } catch (error) {
      console.error('Error updating subcategory:', error);
      setError('Failed to update subcategory');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteSubCategory = async (subCategoryId) => {
    if (window.confirm('Are you sure you want to delete this subcategory?')) {
      try {
        const response = await subCategoriesAPI.deleteSubCategory(subCategoryId);
        if (response.success) {
          setSuccess('Subcategory deleted successfully!');
          fetchSubCategories();
        } else {
          setError(response.message || 'Failed to delete subcategory');
        }
      } catch (error) {
        console.error('Error deleting subcategory:', error);
        setError('Failed to delete subcategory');
      }
    }
  };

  const openEditModal = (subCategory) => {
    setEditSubCategory({ ...subCategory });
    setShowEditModal(true);
  };

  const handleFileUpload = () => {
    if (selectedFile) {
      alert(`File uploaded: ${selectedFile.name}`);
      setShowImportModal(false);
      setSelectedFile(null);
    } else {
      alert('Please select a file first.');
    }
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Sub Categories Management</h2>
              <p className="text-muted">Manage insurance sub categories</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="primary" onClick={() => setShowNewModal(true)}>
                <FaPlus className="me-2" />
                New Sub Category
              </Button>
              <Button variant="secondary" onClick={() => setShowImportModal(true)}>
                <FaFileImport className="me-2" />
                Import
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Row className="mb-3">
        <Col md={6}>
          <div className="position-relative">
            <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
            <Form.Control
              type="text"
              placeholder="Search sub categories..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="ps-5"
            />
          </div>
        </Col>
        <Col md={3}>
          <Form.Select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="All">All Categories</option>
            {categories.map((category) => (
              <option key={category._id} value={category.name}>
                {category.name}
              </option>
            ))}
          </Form.Select>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading sub categories...</p>
                </div>
              ) : filteredSubCategories.length === 0 ? (
                <div className="text-center py-4">
                  <FaPlus size={48} className="text-muted mb-3" />
                  <h5>No Sub Categories Found</h5>
                  <p className="text-muted">
                    {search ? 'No sub categories match your search.' : 'Start by creating your first sub category.'}
                  </p>
                  {!search && (
                    <Button variant="primary" onClick={() => setShowNewModal(true)}>
                      <FaPlus className="me-2" />
                      Create First Sub Category
                    </Button>
                  )}
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Code</th>
                      <th>Category</th>
                      <th>Description</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredSubCategories.map((subCategory) => (
                      <tr key={subCategory._id}>
                        <td>
                          <strong>{subCategory.name}</strong>
                        </td>
                        <td>
                          <code>{subCategory.code}</code>
                        </td>
                        <td>
                          <span className="badge bg-info">
                            {subCategory.category?.name || 'N/A'}
                          </span>
                        </td>
                        <td>{subCategory.description || '-'}</td>
                        <td>
                          <span className={`badge ${subCategory.isActive ? 'bg-success' : 'bg-secondary'}`}>
                            {subCategory.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td>{new Date(subCategory.createdAt).toLocaleDateString()}</td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-warning"
                              size="sm"
                              onClick={() => openEditModal(subCategory)}
                              title="Edit Sub Category"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDeleteSubCategory(subCategory._id)}
                              title="Delete Sub Category"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Modal - New SubCategory */}
      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Sub Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Sub Category Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={newSubCategory.name}
                    onChange={handleNewInputChange}
                    placeholder="Enter subcategory name"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code *</Form.Label>
                  <Form.Control
                    type="text"
                    name="code"
                    value={newSubCategory.code}
                    onChange={handleNewInputChange}
                    placeholder="Enter unique code"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Category *</Form.Label>
              <Form.Select
                name="category"
                value={newSubCategory.category}
                onChange={handleNewInputChange}
                required
              >
                <option value="">-- Select Category --</option>
                {categories.map((category) => (
                  <option key={category._id} value={category._id}>
                    {category.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={newSubCategory.description}
                onChange={handleNewInputChange}
                placeholder="Enter description"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="isActive"
                label="Active"
                checked={newSubCategory.isActive}
                onChange={handleNewInputChange}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNewModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddSubCategory}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Creating...
              </>
            ) : (
              'Create Sub Category'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal - Edit SubCategory */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Sub Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {editSubCategory && (
            <Form>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Sub Category Name *</Form.Label>
                    <Form.Control
                      type="text"
                      name="name"
                      value={editSubCategory.name}
                      onChange={(e) => setEditSubCategory({...editSubCategory, name: e.target.value})}
                      placeholder="Enter subcategory name"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Code *</Form.Label>
                    <Form.Control
                      type="text"
                      name="code"
                      value={editSubCategory.code}
                      onChange={(e) => setEditSubCategory({...editSubCategory, code: e.target.value})}
                      placeholder="Enter unique code"
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Form.Group className="mb-3">
                <Form.Label>Category *</Form.Label>
                <Form.Select
                  name="category"
                  value={editSubCategory.category?._id || editSubCategory.category}
                  onChange={(e) => setEditSubCategory({...editSubCategory, category: e.target.value})}
                  required
                >
                  <option value="">-- Select Category --</option>
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={editSubCategory.description || ''}
                  onChange={(e) => setEditSubCategory({...editSubCategory, description: e.target.value})}
                  placeholder="Enter description"
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  name="isActive"
                  label="Active"
                  checked={editSubCategory.isActive}
                  onChange={(e) => setEditSubCategory({...editSubCategory, isActive: e.target.checked})}
                />
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleEditSubCategory}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              'Update Sub Category'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default SubCategories;
