const express = require('express');
const { body, query, validationResult } = require('express-validator');
const TicketCategory = require('../models/TicketCategory');
const Category = require('../models/Category');
const { authenticate, adminOnly, adminOrEmployee } = require('../middleware/auth');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/temp/' });

// @route   GET /api/ticket-categories
// @desc    Get all ticket categories with search and pagination
// @access  Private
router.get('/', authenticate, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('category').optional().isMongoId().withMessage('Invalid category ID'),
  query('status').optional().isIn(['active', 'inactive', 'all']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { search, category, status } = req.query;
    
    let query = {};
    
    // Status filter
    if (status === 'active') {
      query.isActive = true;
    } else if (status === 'inactive') {
      query.isActive = false;
    }
    
    if (category) {
      query.category = category;
    }

    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    const ticketCategories = await TicketCategory.find(query)
      .populate('category', 'name type')
      .populate('defaultAssignee', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .sort({ sortOrder: 1, name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await TicketCategory.countDocuments(query);

    res.json({
      success: true,
      data: {
        ticketCategories,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Get ticket categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching ticket categories'
    });
  }
});

// @route   GET /api/ticket-categories/:id
// @desc    Get ticket category by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const ticketCategory = await TicketCategory.findById(req.params.id)
      .populate('category', 'name type description')
      .populate('defaultAssignee', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    if (!ticketCategory) {
      return res.status(404).json({
        success: false,
        message: 'Ticket category not found'
      });
    }

    res.json({
      success: true,
      data: { ticketCategory }
    });
  } catch (error) {
    console.error('Get ticket category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching ticket category'
    });
  }
});

// @route   POST /api/ticket-categories
// @desc    Create new ticket category
// @access  Private (Admin/Employee)
router.post('/', authenticate, adminOrEmployee, [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('category')
    .optional()
    .isMongoId()
    .withMessage('Valid category ID is required'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority level')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { 
      name, 
      description, 
      category, 
      isActive, 
      color, 
      icon, 
      priority, 
      autoAssignment, 
      defaultAssignee, 
      slaHours, 
      escalationHours, 
      tags, 
      sortOrder 
    } = req.body;

    // Check if category exists (if provided)
    if (category) {
      const parentCategory = await Category.findById(category);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
    }

    // Check for duplicate name within the same category
    const existingTicketCategory = await TicketCategory.findOne({ 
      name: name.trim(),
      category: category || null
    });
    
    if (existingTicketCategory) {
      return res.status(400).json({
        success: false,
        message: 'Ticket category with this name already exists in the selected category'
      });
    }

    const ticketCategory = new TicketCategory({
      name: name.trim(),
      description: description?.trim(),
      category,
      isActive,
      color,
      icon,
      priority,
      autoAssignment,
      defaultAssignee,
      slaHours,
      escalationHours,
      tags,
      sortOrder,
      createdBy: req.user._id
    });

    await ticketCategory.save();
    await ticketCategory.populate('category', 'name type');
    await ticketCategory.populate('defaultAssignee', 'firstName lastName');

    res.status(201).json({
      success: true,
      message: 'Ticket category created successfully',
      data: { ticketCategory }
    });
  } catch (error) {
    console.error('Create ticket category error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Ticket category with this name already exists'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while creating ticket category'
    });
  }
});

// @route   PUT /api/ticket-categories/:id
// @desc    Update ticket category
// @access  Private (Admin/Employee)
router.put('/:id', authenticate, adminOrEmployee, [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('category')
    .optional()
    .isMongoId()
    .withMessage('Valid category ID is required'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const ticketCategory = await TicketCategory.findById(req.params.id);
    if (!ticketCategory) {
      return res.status(404).json({
        success: false,
        message: 'Ticket category not found'
      });
    }

    const { 
      name, 
      description, 
      category, 
      isActive, 
      color, 
      icon, 
      priority, 
      autoAssignment, 
      defaultAssignee, 
      slaHours, 
      escalationHours, 
      tags, 
      sortOrder 
    } = req.body;

    // Check if category exists (if being updated)
    if (category && category !== ticketCategory.category?.toString()) {
      const parentCategory = await Category.findById(category);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: 'Parent category not found'
        });
      }
    }

    // Check for duplicate name within the same category (if name or category is being updated)
    if (name || category !== undefined) {
      const checkName = name || ticketCategory.name;
      const checkCategory = category !== undefined ? category : ticketCategory.category;
      
      const existingTicketCategory = await TicketCategory.findOne({ 
        name: checkName.trim(),
        category: checkCategory || null,
        _id: { $ne: req.params.id }
      });
      
      if (existingTicketCategory) {
        return res.status(400).json({
          success: false,
          message: 'Ticket category with this name already exists in the selected category'
        });
      }
    }

    // Update fields
    if (name) ticketCategory.name = name.trim();
    if (description !== undefined) ticketCategory.description = description?.trim();
    if (category !== undefined) ticketCategory.category = category;
    if (isActive !== undefined) ticketCategory.isActive = isActive;
    if (color) ticketCategory.color = color;
    if (icon) ticketCategory.icon = icon;
    if (priority) ticketCategory.priority = priority;
    if (autoAssignment !== undefined) ticketCategory.autoAssignment = autoAssignment;
    if (defaultAssignee !== undefined) ticketCategory.defaultAssignee = defaultAssignee;
    if (slaHours !== undefined) ticketCategory.slaHours = slaHours;
    if (escalationHours !== undefined) ticketCategory.escalationHours = escalationHours;
    if (tags) ticketCategory.tags = tags;
    if (sortOrder !== undefined) ticketCategory.sortOrder = sortOrder;
    
    ticketCategory.updatedBy = req.user._id;

    await ticketCategory.save();
    await ticketCategory.populate('category', 'name type');
    await ticketCategory.populate('defaultAssignee', 'firstName lastName');

    res.json({
      success: true,
      message: 'Ticket category updated successfully',
      data: { ticketCategory }
    });
  } catch (error) {
    console.error('Update ticket category error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Ticket category with this name already exists'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error while updating ticket category'
    });
  }
});

// @route   DELETE /api/ticket-categories/:id
// @desc    Delete ticket category
// @access  Private (Admin only)
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const ticketCategory = await TicketCategory.findById(req.params.id);
    if (!ticketCategory) {
      return res.status(404).json({
        success: false,
        message: 'Ticket category not found'
      });
    }

    // Check if ticket category is being used by any tickets
    const Ticket = require('../models/Ticket');
    const ticketsCount = await Ticket.countDocuments({ category: req.params.id });
    
    if (ticketsCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete ticket category. It is being used by ${ticketsCount} tickets.`
      });
    }

    await TicketCategory.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Ticket category deleted successfully'
    });
  } catch (error) {
    console.error('Delete ticket category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting ticket category'
    });
  }
});

module.exports = router;
