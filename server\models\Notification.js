const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Recipient is required']
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String,
    enum: [
      'policy_approved', 'policy_rejected', 'policy_renewal', 'policy_expiring',
      'claim_submitted', 'claim_approved', 'claim_rejected', 'claim_settled',
      'ticket_created', 'ticket_updated', 'ticket_resolved', 'ticket_assigned',
      'payment_due', 'payment_received', 'payment_overdue',
      'document_uploaded', 'document_required',
      'system_maintenance', 'security_alert',
      'welcome', 'verification_required', 'password_changed',
      'general', 'reminder', 'alert'
    ],
    required: [true, 'Notification type is required']
  },
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  message: {
    type: String,
    required: [true, 'Notification message is required'],
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['unread', 'read', 'archived'],
    default: 'unread'
  },
  category: {
    type: String,
    enum: ['policy', 'claim', 'ticket', 'payment', 'system', 'security', 'general'],
    required: [true, 'Category is required']
  },
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['Policy', 'Claim', 'Ticket', 'User', 'Payment']
    },
    entityId: mongoose.Schema.Types.ObjectId
  },
  actionUrl: {
    type: String,
    validate: {
      validator: function(v) {
        return !v || /^\/[a-zA-Z0-9\-_\/]*$/.test(v);
      },
      message: 'Action URL must be a valid relative path'
    }
  },
  actionText: {
    type: String,
    maxlength: [50, 'Action text cannot exceed 50 characters']
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  channels: {
    inApp: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: false
    },
    sms: {
      type: Boolean,
      default: false
    },
    push: {
      type: Boolean,
      default: false
    }
  },
  deliveryStatus: {
    inApp: {
      status: {
        type: String,
        enum: ['pending', 'delivered', 'failed'],
        default: 'pending'
      },
      deliveredAt: Date,
      error: String
    },
    email: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed'],
        default: 'pending'
      },
      sentAt: Date,
      deliveredAt: Date,
      messageId: String,
      error: String
    },
    sms: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed'],
        default: 'pending'
      },
      sentAt: Date,
      deliveredAt: Date,
      messageId: String,
      error: String
    },
    push: {
      status: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed'],
        default: 'pending'
      },
      sentAt: Date,
      deliveredAt: Date,
      messageId: String,
      error: String
    }
  },
  readAt: Date,
  archivedAt: Date,
  expiresAt: Date,
  isSystem: {
    type: Boolean,
    default: false
  },
  batchId: String, // For grouping related notifications
  templateId: String, // Reference to email template used
  variables: mongoose.Schema.Types.Mixed // Template variables
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for notification age
notificationSchema.virtual('age').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  return Math.floor((now - created) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Virtual for delivery summary
notificationSchema.virtual('deliverySummary').get(function() {
  const channels = ['inApp', 'email', 'sms', 'push'];
  const summary = {
    total: 0,
    delivered: 0,
    failed: 0,
    pending: 0
  };
  
  channels.forEach(channel => {
    if (this.channels[channel]) {
      summary.total++;
      const status = this.deliveryStatus[channel].status;
      if (status === 'delivered') summary.delivered++;
      else if (status === 'failed') summary.failed++;
      else summary.pending++;
    }
  });
  
  return summary;
});

// Index for better query performance
notificationSchema.index({ recipient: 1, status: 1 });
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ category: 1 });
notificationSchema.index({ priority: 1 });
notificationSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });

// TTL index for automatic cleanup of expired notifications (this also serves as regular index)
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Static method to find unread notifications for user
notificationSchema.statics.findUnreadForUser = function(userId, limit = 20) {
  return this.find({
    recipient: userId,
    status: 'unread',
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  })
  .populate('sender', 'firstName lastName email')
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Static method to get notification counts by category
notificationSchema.statics.getCountsByCategory = function(userId) {
  return this.aggregate([
    {
      $match: {
        recipient: new mongoose.Types.ObjectId(userId),
        status: 'unread',
        $or: [
          { expiresAt: { $exists: false } },
          { expiresAt: { $gt: new Date() } }
        ]
      }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Static method to mark notifications as read
notificationSchema.statics.markAsRead = function(userId, notificationIds) {
  return this.updateMany(
    {
      recipient: userId,
      _id: { $in: notificationIds }
    },
    {
      $set: {
        status: 'read',
        readAt: new Date()
      }
    }
  );
};

// Static method to create notification
notificationSchema.statics.createNotification = async function(data) {
  const notification = new this(data);
  
  // Set default expiration if not provided
  if (!notification.expiresAt) {
    const expirationDays = {
      'urgent': 7,
      'high': 14,
      'medium': 30,
      'low': 60
    };
    
    const days = expirationDays[notification.priority] || 30;
    notification.expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }
  
  // Mark in-app delivery as delivered immediately
  if (notification.channels.inApp) {
    notification.deliveryStatus.inApp.status = 'delivered';
    notification.deliveryStatus.inApp.deliveredAt = new Date();
  }
  
  await notification.save();
  return notification;
};

// Method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.status = 'read';
  this.readAt = new Date();
  return this.save();
};

// Method to archive
notificationSchema.methods.archive = function() {
  this.status = 'archived';
  this.archivedAt = new Date();
  return this.save();
};

// Method to update delivery status
notificationSchema.methods.updateDeliveryStatus = function(channel, status, messageId, error) {
  if (this.deliveryStatus[channel]) {
    this.deliveryStatus[channel].status = status;
    this.deliveryStatus[channel].messageId = messageId;
    this.deliveryStatus[channel].error = error;
    
    if (status === 'sent') {
      this.deliveryStatus[channel].sentAt = new Date();
    } else if (status === 'delivered') {
      this.deliveryStatus[channel].deliveredAt = new Date();
    }
  }
  
  return this.save();
};

module.exports = mongoose.model('Notification', notificationSchema);
