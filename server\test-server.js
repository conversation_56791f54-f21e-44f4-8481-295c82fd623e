const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Test server is running',
    timestamp: new Date().toISOString()
  });
});

// Simple auth route
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Mock authentication
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({
      success: true,
      data: {
        user: {
          _id: '1',
          firstName: 'Admin',
          lastName: 'User',
          email: email,
          role: 'admin',
          fullName: 'Admin User'
        },
        token: 'mock-jwt-token-' + Date.now()
      }
    });
  } else if (email === '<EMAIL>' && password === 'emp123') {
    res.json({
      success: true,
      data: {
        user: {
          _id: '2',
          firstName: 'Employee',
          lastName: 'User',
          email: email,
          role: 'employee',
          fullName: 'Employee User'
        },
        token: 'mock-jwt-token-' + Date.now()
      }
    });
  } else if (email === '<EMAIL>' && password === 'cust123') {
    res.json({
      success: true,
      data: {
        user: {
          _id: '3',
          firstName: 'Customer',
          lastName: 'User',
          email: email,
          role: 'customer',
          fullName: 'Customer User'
        },
        token: 'mock-jwt-token-' + Date.now()
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Mock dashboard stats
app.get('/api/reports/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      overview: {
        totalPolicies: 1250,
        activePolicies: 980,
        pendingPolicies: 45,
        totalTickets: 180,
        openTickets: 35,
        totalUsers: 1200,
        totalCustomers: 1150,
        totalEmployees: 25,
        monthlyRevenue: 125000
      }
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal Server Error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
