{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\SupportTicket.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupportTickets = () => {\n  _s();\n  const [tickets, setTickets] = useState([\n    // {\n    //   id: 101,\n    //   customerName: '<PERSON>',\n    //   contact: '9876543210',\n    //   subject: 'Login Failure',\n    //   description: 'Unable to login to the portal',\n    //   category: 'Technical Issue',\n    //   openDate: '2025-06-22',\n    //   status: 'Open',\n    //   staff: '<PERSON><PERSON>',\n    //   remark: 'Checking login error logs'\n    // }\n  ]);\n  const [showModal, setShowModal] = useState(false);\n  const [form, setForm] = useState({\n    customerName: '',\n    contact: '',\n    subject: '',\n    description: '',\n    category: '',\n    openDate: '',\n    status: 'Open',\n    staff: '',\n    remark: ''\n  });\n  const [search, setSearch] = useState('');\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSave = () => {\n    setTickets([...tickets, {\n      ...form,\n      id: tickets.length + 101\n    }]);\n    setShowModal(false);\n    setForm({\n      customerName: '',\n      contact: '',\n      subject: '',\n      description: '',\n      category: '',\n      openDate: '',\n      status: 'Open',\n      staff: '',\n      remark: ''\n    });\n  };\n  const filtered = tickets.filter(t => t.customerName.toLowerCase().includes(search.toLowerCase()) || t.subject.toLowerCase().includes(search.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Support Tickets\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setShowModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex justify-content-between\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Select, {\n        style: {\n          width: '80px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        style: {\n          width: '300px'\n        },\n        placeholder: \"Search...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ticket ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Customer Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Customer Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ticket Subject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ticket Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ticket Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Open Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Assign Staff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Remark\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filtered.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: /*#__PURE__*/_jsxDEV(\"td\", {\n            colSpan: \"11\",\n            className: \"text-center\",\n            children: \"No data available in database\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this) : filtered.map(ticket => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.customerName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.contact\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.subject\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.openDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge bg-${ticket.status === 'Open' ? 'success' : 'warning'}`,\n              children: ticket.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.staff\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: ticket.remark\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 17\n          }, this)]\n        }, ticket.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"New Support Ticket\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Customer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"customerName\",\n                value: form.customerName,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"contact\",\n                value: form.contact,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"subject\",\n              value: form.subject,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              name: \"description\",\n              value: form.description,\n              onChange: handleChange,\n              as: \"textarea\",\n              rows: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                name: \"category\",\n                value: form.category,\n                onChange: handleChange,\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Open Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"date\",\n                name: \"openDate\",\n                value: form.openDate,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Assign Staff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"staff\",\n                value: form.staff,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Remark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                name: \"remark\",\n                value: form.remark,\n                onChange: handleChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              name: \"status\",\n              value: form.status,\n              onChange: handleChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Open\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Closed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(SupportTickets, \"V2yE0GLLSc9MOKZXbNTHO9UWPCg=\");\n_c = SupportTickets;\nexport default SupportTickets;\nvar _c;\n$RefreshReg$(_c, \"SupportTickets\");", "map": {"version": 3, "names": ["React", "useState", "Table", "<PERSON><PERSON>", "Modal", "Form", "Row", "Col", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "SupportTickets", "_s", "tickets", "setTickets", "showModal", "setShowModal", "form", "setForm", "customerName", "contact", "subject", "description", "category", "openDate", "status", "staff", "remark", "search", "setSearch", "handleChange", "e", "target", "name", "value", "handleSave", "id", "length", "filtered", "filter", "t", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "size", "Select", "style", "width", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "colSpan", "map", "ticket", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Label", "Group", "as", "rows", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/SupportTicket.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';\r\nimport { FaEdit, FaTrash } from 'react-icons/fa';\r\n\r\nconst SupportTickets = () => {\r\n  const [tickets, setTickets] = useState([\r\n    // {\r\n    //   id: 101,\r\n    //   customerName: '<PERSON>',\r\n    //   contact: '9876543210',\r\n    //   subject: 'Login Failure',\r\n    //   description: 'Unable to login to the portal',\r\n    //   category: 'Technical Issue',\r\n    //   openDate: '2025-06-22',\r\n    //   status: 'Open',\r\n    //   staff: '<PERSON><PERSON>',\r\n    //   remark: 'Checking login error logs'\r\n    // }\r\n  ]);\r\n\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [form, setForm] = useState({\r\n    customerName: '',\r\n    contact: '',\r\n    subject: '',\r\n    description: '',\r\n    category: '',\r\n    openDate: '',\r\n    status: 'Open',\r\n    staff: '',\r\n    remark: ''\r\n  });\r\n  const [search, setSearch] = useState('');\r\n\r\n  const handleChange = (e) => {\r\n    setForm({ ...form, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSave = () => {\r\n    setTickets([...tickets, { ...form, id: tickets.length + 101 }]);\r\n    setShowModal(false);\r\n    setForm({\r\n      customerName: '', contact: '', subject: '', description: '', category: '', openDate: '',\r\n      status: 'Open', staff: '', remark: ''\r\n    });\r\n  };\r\n\r\n  const filtered = tickets.filter(t =>\r\n    t.customerName.toLowerCase().includes(search.toLowerCase()) ||\r\n    t.subject.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h4 className=\"fw-bold text-uppercase\">Support Tickets</h4>\r\n        <Button onClick={() => setShowModal(true)}>+ New</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-2 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      <div className=\"mb-3 d-flex justify-content-between\">\r\n        <Form.Select style={{ width: '80px' }}>\r\n          <option>10</option>\r\n          <option>25</option>\r\n          <option>50</option>\r\n        </Form.Select>\r\n        <Form.Control\r\n          type=\"text\"\r\n          style={{ width: '300px' }}\r\n          placeholder=\"Search...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      <Table bordered hover responsive>\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Ticket ID</th>\r\n            <th>Customer Name</th>\r\n            <th>Customer Contact</th>\r\n            <th>Ticket Subject</th>\r\n            <th>Ticket Description</th>\r\n            <th>Ticket Category</th>\r\n            <th>Open Date</th>\r\n            <th>Status</th>\r\n            <th>Assign Staff</th>\r\n            <th>Remark</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filtered.length === 0 ? (\r\n            <tr><td colSpan=\"11\" className=\"text-center\">No data available in database</td></tr>\r\n          ) : (\r\n            filtered.map(ticket => (\r\n              <tr key={ticket.id}>\r\n                <td>{ticket.id}</td>\r\n                <td>{ticket.customerName}</td>\r\n                <td>{ticket.contact}</td>\r\n                <td>{ticket.subject}</td>\r\n                <td>{ticket.description}</td>\r\n                <td>{ticket.category}</td>\r\n                <td>{ticket.openDate}</td>\r\n                <td><span className={`badge bg-${ticket.status === 'Open' ? 'success' : 'warning'}`}>{ticket.status}</span></td>\r\n                <td>{ticket.staff}</td>\r\n                <td>{ticket.remark}</td>\r\n                <td>\r\n                  <Button size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                  <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n                </td>\r\n              </tr>\r\n            ))\r\n          )}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>New Support Ticket</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form>\r\n            <Row className=\"mb-2\">\r\n              <Col><Form.Label>Customer Name</Form.Label>\r\n              <Form.Control name=\"customerName\" value={form.customerName} onChange={handleChange} /></Col>\r\n              <Col><Form.Label>Contact</Form.Label>\r\n              <Form.Control name=\"contact\" value={form.contact} onChange={handleChange} /></Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-2\">\r\n              <Form.Label>Subject</Form.Label>\r\n              <Form.Control name=\"subject\" value={form.subject} onChange={handleChange} />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-2\">\r\n              <Form.Label>Description</Form.Label>\r\n              <Form.Control name=\"description\" value={form.description} onChange={handleChange} as=\"textarea\" rows={2} />\r\n            </Form.Group>\r\n\r\n            <Row className=\"mb-2\">\r\n              <Col>\r\n                <Form.Label>Category</Form.Label>\r\n                <Form.Select name=\"category\" value={form.category} onChange={handleChange}>\r\n                  <option value=\"\">Select</option>\r\n                  {/* <option>Technical Issue</option>\r\n                  <option>Policy Request</option>\r\n                  <option>Account Issue</option> */}\r\n                </Form.Select>\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Open Date</Form.Label>\r\n                <Form.Control type=\"date\" name=\"openDate\" value={form.openDate} onChange={handleChange} />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"mb-2\">\r\n              <Col>\r\n                <Form.Label>Assign Staff</Form.Label>\r\n                <Form.Control name=\"staff\" value={form.staff} onChange={handleChange} />\r\n              </Col>\r\n              <Col>\r\n                <Form.Label>Remark</Form.Label>\r\n                <Form.Control name=\"remark\" value={form.remark} onChange={handleChange} />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Status</Form.Label>\r\n              <Form.Select name=\"status\" value={form.status} onChange={handleChange}>\r\n                <option>Open</option>\r\n                <option>Pending</option>\r\n                <option>Closed</option>\r\n              </Form.Select>\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={handleSave}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupportTickets;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACtE,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD,CAAC;EAEF,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC;IAC/BmB,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAM8B,YAAY,GAAIC,CAAC,IAAK;IAC1Bb,OAAO,CAAC;MAAE,GAAGD,IAAI;MAAE,CAACc,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBrB,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE;MAAE,GAAGI,IAAI;MAAEmB,EAAE,EAAEvB,OAAO,CAACwB,MAAM,GAAG;IAAI,CAAC,CAAC,CAAC;IAC/DrB,YAAY,CAAC,KAAK,CAAC;IACnBE,OAAO,CAAC;MACNC,YAAY,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MACvFC,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IACrC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,QAAQ,GAAGzB,OAAO,CAAC0B,MAAM,CAACC,CAAC,IAC/BA,CAAC,CAACrB,YAAY,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,MAAM,CAACa,WAAW,CAAC,CAAC,CAAC,IAC3DD,CAAC,CAACnB,OAAO,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,MAAM,CAACa,WAAW,CAAC,CAAC,CACvD,CAAC;EAED,oBACE/B,OAAA;IAAKiC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClClC,OAAA;MAAKiC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrElC,OAAA;QAAIiC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DtC,OAAA,CAACR,MAAM;QAAC+C,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,IAAI,CAAE;QAAA4B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAENtC,OAAA;MAAKiC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1ClC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACC,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DtC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACC,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DtC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACC,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DtC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACC,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DtC,OAAA,CAACR,MAAM;QAACgD,OAAO,EAAC,mBAAmB;QAACC,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENtC,OAAA;MAAKiC,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClDlC,OAAA,CAACN,IAAI,CAACgD,MAAM;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACpClC,OAAA;UAAAkC,QAAA,EAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnBtC,OAAA;UAAAkC,QAAA,EAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnBtC,OAAA;UAAAkC,QAAA,EAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACdtC,OAAA,CAACN,IAAI,CAACmD,OAAO;QACXC,IAAI,EAAC,MAAM;QACXH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAC1BG,WAAW,EAAC,WAAW;QACvBvB,KAAK,EAAEN,MAAO;QACd8B,QAAQ,EAAG3B,CAAC,IAAKF,SAAS,CAACE,CAAC,CAACC,MAAM,CAACE,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtC,OAAA,CAACT,KAAK;MAAC0D,QAAQ;MAACC,KAAK;MAACC,UAAU;MAAAjB,QAAA,gBAC9BlC,OAAA;QAAOiC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BlC,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAAkC,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBtC,OAAA;YAAAkC,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBtC,OAAA;YAAAkC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBtC,OAAA;YAAAkC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBtC,OAAA;YAAAkC,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BtC,OAAA;YAAAkC,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBtC,OAAA;YAAAkC,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBtC,OAAA;YAAAkC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACftC,OAAA;YAAAkC,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBtC,OAAA;YAAAkC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACftC,OAAA;YAAAkC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRtC,OAAA;QAAAkC,QAAA,EACGN,QAAQ,CAACD,MAAM,KAAK,CAAC,gBACpB3B,OAAA;UAAAkC,QAAA,eAAIlC,OAAA;YAAIoD,OAAO,EAAC,IAAI;YAACnB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,GAEpFV,QAAQ,CAACyB,GAAG,CAACC,MAAM,iBACjBtD,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAAC5B;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAAC7C;UAAY;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9BtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAAC5C;UAAO;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzBtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAAC3C;UAAO;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzBtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAAC1C;UAAW;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7BtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAACzC;UAAQ;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAACxC;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1BtC,OAAA;YAAAkC,QAAA,eAAIlC,OAAA;cAAMiC,SAAS,EAAE,YAAYqB,MAAM,CAACvC,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;cAAAmB,QAAA,EAAEoB,MAAM,CAACvC;YAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChHtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAACtC;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBtC,OAAA;YAAAkC,QAAA,EAAKoB,MAAM,CAACrC;UAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBtC,OAAA;YAAAkC,QAAA,gBACElC,OAAA,CAACR,MAAM;cAACiD,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAAClC,OAAA,CAACH,MAAM;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtDtC,OAAA,CAACR,MAAM;cAACgD,OAAO,EAAC,QAAQ;cAACC,IAAI,EAAC,IAAI;cAAAP,QAAA,eAAClC,OAAA,CAACF,OAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAdEgB,MAAM,CAAC5B,EAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAed,CACL;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRtC,OAAA,CAACP,KAAK;MAAC8D,IAAI,EAAElD,SAAU;MAACmD,MAAM,EAAEA,CAAA,KAAMlD,YAAY,CAAC,KAAK,CAAE;MAACmD,QAAQ;MAAAvB,QAAA,gBACjElC,OAAA,CAACP,KAAK,CAACiE,MAAM;QAACC,WAAW;QAAAzB,QAAA,eACvBlC,OAAA,CAACP,KAAK,CAACmE,KAAK;UAAA1B,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACftC,OAAA,CAACP,KAAK,CAACoE,IAAI;QAAA3B,QAAA,eACTlC,OAAA,CAACN,IAAI;UAAAwC,QAAA,gBACHlC,OAAA,CAACL,GAAG;YAACsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBAAClC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3CtC,OAAA,CAACN,IAAI,CAACmD,OAAO;gBAACtB,IAAI,EAAC,cAAc;gBAACC,KAAK,EAAEjB,IAAI,CAACE,YAAa;gBAACuC,QAAQ,EAAE5B;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5FtC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBAAClC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCtC,OAAA,CAACN,IAAI,CAACmD,OAAO;gBAACtB,IAAI,EAAC,SAAS;gBAACC,KAAK,EAAEjB,IAAI,CAACG,OAAQ;gBAACsC,QAAQ,EAAE5B;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAENtC,OAAA,CAACN,IAAI,CAACqE,KAAK;YAAC9B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BlC,OAAA,CAACN,IAAI,CAACoE,KAAK;cAAA5B,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCtC,OAAA,CAACN,IAAI,CAACmD,OAAO;cAACtB,IAAI,EAAC,SAAS;cAACC,KAAK,EAAEjB,IAAI,CAACI,OAAQ;cAACqC,QAAQ,EAAE5B;YAAa;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAEbtC,OAAA,CAACN,IAAI,CAACqE,KAAK;YAAC9B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BlC,OAAA,CAACN,IAAI,CAACoE,KAAK;cAAA5B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCtC,OAAA,CAACN,IAAI,CAACmD,OAAO;cAACtB,IAAI,EAAC,aAAa;cAACC,KAAK,EAAEjB,IAAI,CAACK,WAAY;cAACoC,QAAQ,EAAE5B,YAAa;cAAC4C,EAAE,EAAC,UAAU;cAACC,IAAI,EAAE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC,eAEbtC,OAAA,CAACL,GAAG;YAACsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBACFlC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjCtC,OAAA,CAACN,IAAI,CAACgD,MAAM;gBAACnB,IAAI,EAAC,UAAU;gBAACC,KAAK,EAAEjB,IAAI,CAACM,QAAS;gBAACmC,QAAQ,EAAE5B,YAAa;gBAAAc,QAAA,eACxElC,OAAA;kBAAQwB,KAAK,EAAC,EAAE;kBAAAU,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNtC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBACFlC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCtC,OAAA,CAACN,IAAI,CAACmD,OAAO;gBAACC,IAAI,EAAC,MAAM;gBAACvB,IAAI,EAAC,UAAU;gBAACC,KAAK,EAAEjB,IAAI,CAACO,QAAS;gBAACkC,QAAQ,EAAE5B;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA,CAACL,GAAG;YAACsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBACFlC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCtC,OAAA,CAACN,IAAI,CAACmD,OAAO;gBAACtB,IAAI,EAAC,OAAO;gBAACC,KAAK,EAAEjB,IAAI,CAACS,KAAM;gBAACgC,QAAQ,EAAE5B;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACNtC,OAAA,CAACJ,GAAG;cAAAsC,QAAA,gBACFlC,OAAA,CAACN,IAAI,CAACoE,KAAK;gBAAA5B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BtC,OAAA,CAACN,IAAI,CAACmD,OAAO;gBAACtB,IAAI,EAAC,QAAQ;gBAACC,KAAK,EAAEjB,IAAI,CAACU,MAAO;gBAAC+B,QAAQ,EAAE5B;cAAa;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA,CAACN,IAAI,CAACqE,KAAK;YAAC9B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC1BlC,OAAA,CAACN,IAAI,CAACoE,KAAK;cAAA5B,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BtC,OAAA,CAACN,IAAI,CAACgD,MAAM;cAACnB,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAEjB,IAAI,CAACQ,MAAO;cAACiC,QAAQ,EAAE5B,YAAa;cAAAc,QAAA,gBACpElC,OAAA;gBAAAkC,QAAA,EAAQ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrBtC,OAAA;gBAAAkC,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxBtC,OAAA;gBAAAkC,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbtC,OAAA,CAACP,KAAK,CAACyE,MAAM;QAAAhC,QAAA,gBACXlC,OAAA,CAACR,MAAM;UAACgD,OAAO,EAAC,WAAW;UAACD,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC,KAAK,CAAE;UAAA4B,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EtC,OAAA,CAACR,MAAM;UAACgD,OAAO,EAAC,SAAS;UAACD,OAAO,EAAEd,UAAW;UAAAS,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpC,EAAA,CA5LID,cAAc;AAAAkE,EAAA,GAAdlE,cAAc;AA8LpB,eAAeA,cAAc;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}