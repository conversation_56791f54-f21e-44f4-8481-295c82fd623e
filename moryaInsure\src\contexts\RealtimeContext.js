import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import socketService from '../services/socketService';
import { useAuth } from '../context/AuthContext';

const RealtimeContext = createContext();

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

export const RealtimeProvider = ({ children }) => {
  const { user, token } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [realtimeData, setRealtimeData] = useState({
    tasks: [],
    policies: [],
    users: [],
    categories: [],
    subCategories: [],
    policyHolders: [],
    tickets: [],
    claims: []
  });

  // Connect to socket when user is authenticated
  useEffect(() => {
    if (user && token) {
      console.log('🔌 Connecting to WebSocket...');
      socketService.connect(token);
      
      // Listen for connection status
      socketService.on('connect', () => {
        setIsConnected(true);
        console.log('✅ Real-time connection established');
      });

      socketService.on('disconnect', () => {
        setIsConnected(false);
        console.log('❌ Real-time connection lost');
      });

      // Join user-specific room
      socketService.joinRoom(`user_${user._id}`);
      
      // Join role-specific room
      socketService.joinRoom(`role_${user.role}`);

      return () => {
        socketService.disconnect();
        setIsConnected(false);
      };
    }
  }, [user, token]);

  // Real-time event handlers
  useEffect(() => {
    if (!isConnected) return;

    // Task updates
    const handleTaskUpdate = (data) => {
      console.log('📋 Task update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        tasks: updateArrayData(prev.tasks, data)
      }));
    };

    // Policy updates
    const handlePolicyUpdate = (data) => {
      console.log('📄 Policy update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        policies: updateArrayData(prev.policies, data)
      }));
    };

    // User updates
    const handleUserUpdate = (data) => {
      console.log('👤 User update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        users: updateArrayData(prev.users, data)
      }));
    };

    // Category updates
    const handleCategoryUpdate = (data) => {
      console.log('📂 Category update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        categories: updateArrayData(prev.categories, data)
      }));
    };

    // SubCategory updates
    const handleSubCategoryUpdate = (data) => {
      console.log('📁 SubCategory update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        subCategories: updateArrayData(prev.subCategories, data)
      }));
    };

    // Policy Holder updates
    const handlePolicyHolderUpdate = (data) => {
      console.log('👥 Policy Holder update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        policyHolders: updateArrayData(prev.policyHolders, data)
      }));
    };

    // Ticket updates
    const handleTicketUpdate = (data) => {
      console.log('🎫 Ticket update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        tickets: updateArrayData(prev.tickets, data)
      }));
    };

    // Claim updates
    const handleClaimUpdate = (data) => {
      console.log('💰 Claim update received:', data);
      setRealtimeData(prev => ({
        ...prev,
        claims: updateArrayData(prev.claims, data)
      }));
    };

    // Notification handler
    const handleNotification = (notification) => {
      console.log('🔔 Notification received:', notification);
      setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50
    };

    // Register all event listeners
    socketService.onTaskUpdate(handleTaskUpdate);
    socketService.onPolicyUpdate(handlePolicyUpdate);
    socketService.onUserUpdate(handleUserUpdate);
    socketService.onCategoryUpdate(handleCategoryUpdate);
    socketService.onSubCategoryUpdate(handleSubCategoryUpdate);
    socketService.onPolicyHolderUpdate(handlePolicyHolderUpdate);
    socketService.onTicketUpdate(handleTicketUpdate);
    socketService.onClaimUpdate(handleClaimUpdate);
    socketService.onNotification(handleNotification);

    return () => {
      socketService.removeAllListeners();
    };
  }, [isConnected]);

  // Helper function to update array data
  const updateArrayData = (currentArray, updateData) => {
    const { type, data } = updateData;
    
    switch (type) {
      case 'created':
        return [...currentArray, data];
      
      case 'updated':
        return currentArray.map(item => 
          item._id === data._id ? { ...item, ...data } : item
        );
      
      case 'deleted':
        return currentArray.filter(item => item._id !== data._id);
      
      default:
        return currentArray;
    }
  };

  // Methods to trigger real-time updates
  const triggerRefresh = useCallback((dataType) => {
    socketService.emit('request_refresh', { dataType });
  }, []);

  const markNotificationAsRead = useCallback((notificationId) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Subscribe to specific data updates
  const subscribeToUpdates = useCallback((dataType, callback) => {
    const eventName = `${dataType}_updated`;
    socketService.on(eventName, callback);
    
    return () => {
      socketService.off(eventName, callback);
    };
  }, []);

  const value = {
    isConnected,
    realtimeData,
    notifications,
    triggerRefresh,
    markNotificationAsRead,
    clearNotifications,
    subscribeToUpdates,
    socketService
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
};

export default RealtimeContext;
