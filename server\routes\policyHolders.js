const express = require('express');
const { body, query, validationResult } = require('express-validator');
const PolicyHolder = require('../models/PolicyHolder');
const Policy = require('../models/Policy');
const Category = require('../models/Category');
const SubCategory = require('../models/SubCategory');
const { authenticate, adminOrEmployee } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/policy-holders
// @desc    Get all policy holders with search and pagination
// @access  Private
router.get('/', authenticate, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('status').optional().isIn(['active', 'inactive', 'suspended', 'pending']).withMessage('Invalid status'),
  query('assignedAgent').optional().isMongoId().withMessage('Invalid agent ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const { search, status, assignedAgent } = req.query;
    
    let query = {};
    
    // Role-based filtering
    if (req.user.role === 'employee') {
      query.assignedAgent = req.user._id;
    }
    
    if (status) {
      query.status = status;
    }
    
    if (assignedAgent && req.user.role === 'admin') {
      query.assignedAgent = assignedAgent;
    }

    // Search functionality
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    const policyHolders = await PolicyHolder.find(query)
      .populate('assignedAgent', 'firstName lastName email')
      .populate('policies', 'policyNumber type status premiumAmount coverageAmount')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await PolicyHolder.countDocuments(query);

    res.json({
      success: true,
      data: {
        policyHolders,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Get policy holders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching policy holders'
    });
  }
});

// @route   GET /api/policy-holders/:id
// @desc    Get policy holder by ID
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    let query = { _id: req.params.id };
    
    // Role-based filtering
    if (req.user.role === 'employee') {
      query.assignedAgent = req.user._id;
    }

    const policyHolder = await PolicyHolder.findOne(query)
      .populate('assignedAgent', 'firstName lastName email phone')
      .populate('policies', 'policyNumber type status premiumAmount coverageAmount category subCategory')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName')
      .populate('verifiedBy', 'firstName lastName');

    if (!policyHolder) {
      return res.status(404).json({
        success: false,
        message: 'Policy holder not found'
      });
    }

    res.json({
      success: true,
      data: { policyHolder }
    });
  } catch (error) {
    console.error('Get policy holder error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching policy holder'
    });
  }
});

// @route   POST /api/policy-holders
// @desc    Create new policy holder with policy
// @access  Private
router.post('/', authenticate, adminOrEmployee, [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('dateOfBirth')
    .isISO8601()
    .withMessage('Please provide a valid date of birth'),
  body('gender')
    .isIn(['male', 'female', 'other'])
    .withMessage('Invalid gender'),
  body('address.street')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Street address is required'),
  body('address.city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('City is required'),
  body('address.state')
    .trim()
    .isLength({ min: 1 })
    .withMessage('State is required'),
  body('address.zipCode')
    .trim()
    .isLength({ min: 1 })
    .withMessage('ZIP code is required'),
  // Policy fields
  body('policy.type')
    .isIn(['life', 'health', 'auto', 'home', 'business', 'travel'])
    .withMessage('Invalid policy type'),
  body('policy.category')
    .isMongoId()
    .withMessage('Valid category ID is required'),
  body('policy.coverageAmount')
    .isNumeric()
    .withMessage('Coverage amount must be a number'),
  body('policy.premiumAmount')
    .isNumeric()
    .withMessage('Premium amount must be a number'),
  body('policy.tenure')
    .isInt({ min: 1 })
    .withMessage('Tenure must be a positive integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      occupation,
      annualIncome,
      emergencyContact,
      policy
    } = req.body;

    // Check if email already exists
    const existingPolicyHolder = await PolicyHolder.findByEmail(email);
    if (existingPolicyHolder) {
      return res.status(400).json({
        success: false,
        message: 'Policy holder with this email already exists'
      });
    }

    // Verify category exists
    const category = await Category.findById(policy.category);
    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Verify subcategory exists (if provided)
    if (policy.subCategory) {
      const subCategory = await SubCategory.findById(policy.subCategory);
      if (!subCategory) {
        return res.status(400).json({
          success: false,
          message: 'Subcategory not found'
        });
      }
    }

    // Create policy holder
    const policyHolder = new PolicyHolder({
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      gender,
      address,
      occupation,
      annualIncome,
      emergencyContact,
      assignedAgent: req.user.role === 'employee' ? req.user._id : policy.assignedAgent,
      createdBy: req.user._id
    });

    await policyHolder.save();

    // Create associated policy
    const newPolicy = new Policy({
      policyHolder: policyHolder._id,
      type: policy.type,
      category: policy.category,
      subCategory: policy.subCategory,
      coverageAmount: policy.coverageAmount,
      premiumAmount: policy.premiumAmount,
      tenure: policy.tenure,
      description: policy.description,
      assignedAgent: req.user.role === 'employee' ? req.user._id : policy.assignedAgent,
      createdBy: req.user._id
    });

    await newPolicy.save();

    // Add policy to policy holder
    policyHolder.policies.push(newPolicy._id);
    await policyHolder.save();

    // Populate the response
    await policyHolder.populate('assignedAgent', 'firstName lastName email');
    await policyHolder.populate('policies', 'policyNumber type status premiumAmount coverageAmount');

    res.status(201).json({
      success: true,
      message: 'Policy holder and policy created successfully',
      data: { policyHolder }
    });
  } catch (error) {
    console.error('Create policy holder error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating policy holder'
    });
  }
});

// @route   PUT /api/policy-holders/:id
// @desc    Update policy holder
// @access  Private
router.put('/:id', authenticate, adminOrEmployee, [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let query = { _id: req.params.id };
    
    // Role-based filtering
    if (req.user.role === 'employee') {
      query.assignedAgent = req.user._id;
    }

    const policyHolder = await PolicyHolder.findOne(query);
    if (!policyHolder) {
      return res.status(404).json({
        success: false,
        message: 'Policy holder not found'
      });
    }

    // Check if email is being changed and if it already exists
    if (req.body.email && req.body.email !== policyHolder.email) {
      const existingPolicyHolder = await PolicyHolder.findByEmail(req.body.email);
      if (existingPolicyHolder) {
        return res.status(400).json({
          success: false,
          message: 'Policy holder with this email already exists'
        });
      }
    }

    // Update fields
    const updateFields = [
      'firstName', 'lastName', 'email', 'phone', 'alternatePhone',
      'dateOfBirth', 'gender', 'maritalStatus', 'occupation', 'annualIncome',
      'address', 'emergencyContact', 'status'
    ];
    
    updateFields.forEach(field => {
      if (req.body[field] !== undefined) {
        policyHolder[field] = req.body[field];
      }
    });

    policyHolder.updatedBy = req.user._id;
    await policyHolder.save();

    await policyHolder.populate('assignedAgent', 'firstName lastName email');
    await policyHolder.populate('policies', 'policyNumber type status premiumAmount coverageAmount');

    res.json({
      success: true,
      message: 'Policy holder updated successfully',
      data: { policyHolder }
    });
  } catch (error) {
    console.error('Update policy holder error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating policy holder'
    });
  }
});

// @route   DELETE /api/policy-holders/:id
// @desc    Delete policy holder
// @access  Private (Admin only)
router.delete('/:id', authenticate, async (req, res) => {
  try {
    // Only admin can delete policy holders
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const policyHolder = await PolicyHolder.findById(req.params.id);
    if (!policyHolder) {
      return res.status(404).json({
        success: false,
        message: 'Policy holder not found'
      });
    }

    // Check if policy holder has active policies
    const activePolicies = await Policy.countDocuments({
      policyHolder: req.params.id,
      status: 'active'
    });

    if (activePolicies > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete policy holder. They have ${activePolicies} active policies.`
      });
    }

    await PolicyHolder.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Policy holder deleted successfully'
    });
  } catch (error) {
    console.error('Delete policy holder error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting policy holder'
    });
  }
});

module.exports = router;
