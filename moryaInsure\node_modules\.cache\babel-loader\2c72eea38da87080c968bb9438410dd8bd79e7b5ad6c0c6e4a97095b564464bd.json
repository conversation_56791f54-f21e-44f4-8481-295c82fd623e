{"ast": null, "code": "import io from 'socket.io-client';\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.listeners = new Map();\n  }\n  connect(token) {\n    if (this.socket && this.isConnected) {\n      return this.socket;\n    }\n    const serverUrl = process.env.REACT_APP_SERVER_URL || 'http://localhost:5002';\n    this.socket = io(serverUrl, {\n      auth: {\n        token: token\n      },\n      transports: ['websocket', 'polling']\n    });\n    this.socket.on('connect', () => {\n      console.log('✅ Connected to server via WebSocket');\n      this.isConnected = true;\n    });\n    this.socket.on('disconnect', () => {\n      console.log('❌ Disconnected from server');\n      this.isConnected = false;\n    });\n    this.socket.on('connect_error', error => {\n      console.error('❌ Socket connection error:', error);\n      this.isConnected = false;\n    });\n    return this.socket;\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n      this.listeners.clear();\n    }\n  }\n\n  // Real-time event listeners\n  on(event, callback) {\n    if (this.socket) {\n      this.socket.on(event, callback);\n\n      // Store listener for cleanup\n      if (!this.listeners.has(event)) {\n        this.listeners.set(event, []);\n      }\n      this.listeners.get(event).push(callback);\n    }\n  }\n  off(event, callback) {\n    if (this.socket) {\n      this.socket.off(event, callback);\n\n      // Remove from stored listeners\n      if (this.listeners.has(event)) {\n        const callbacks = this.listeners.get(event);\n        const index = callbacks.indexOf(callback);\n        if (index > -1) {\n          callbacks.splice(index, 1);\n        }\n      }\n    }\n  }\n  emit(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Join specific rooms for targeted updates\n  joinRoom(room) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join_room', room);\n    }\n  }\n  leaveRoom(room) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('leave_room', room);\n    }\n  }\n\n  // Specific real-time event handlers\n  onTaskUpdate(callback) {\n    this.on('task_updated', callback);\n    this.on('task_created', callback);\n    this.on('task_deleted', callback);\n    this.on('task_assigned', callback);\n  }\n  onPolicyUpdate(callback) {\n    this.on('policy_updated', callback);\n    this.on('policy_created', callback);\n    this.on('policy_deleted', callback);\n  }\n  onUserUpdate(callback) {\n    this.on('user_updated', callback);\n    this.on('user_created', callback);\n    this.on('user_deleted', callback);\n  }\n  onCategoryUpdate(callback) {\n    this.on('category_updated', callback);\n    this.on('category_created', callback);\n    this.on('category_deleted', callback);\n  }\n  onSubCategoryUpdate(callback) {\n    this.on('subcategory_updated', callback);\n    this.on('subcategory_created', callback);\n    this.on('subcategory_deleted', callback);\n  }\n  onPolicyHolderUpdate(callback) {\n    this.on('policyholder_updated', callback);\n    this.on('policyholder_created', callback);\n    this.on('policyholder_deleted', callback);\n  }\n  onTicketUpdate(callback) {\n    this.on('ticket_updated', callback);\n    this.on('ticket_created', callback);\n    this.on('ticket_deleted', callback);\n  }\n  onClaimUpdate(callback) {\n    this.on('claim_updated', callback);\n    this.on('claim_created', callback);\n    this.on('claim_deleted', callback);\n  }\n  onNotification(callback) {\n    this.on('notification', callback);\n  }\n  onSystemUpdate(callback) {\n    this.on('system_update', callback);\n  }\n\n  // Cleanup all listeners\n  removeAllListeners() {\n    if (this.socket) {\n      this.listeners.forEach((callbacks, event) => {\n        callbacks.forEach(callback => {\n          this.socket.off(event, callback);\n        });\n      });\n      this.listeners.clear();\n    }\n  }\n\n  // Get connection status\n  getConnectionStatus() {\n    var _this$socket;\n    return {\n      isConnected: this.isConnected,\n      socketId: ((_this$socket = this.socket) === null || _this$socket === void 0 ? void 0 : _this$socket.id) || null\n    };\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\nexport default socketService;", "map": {"version": 3, "names": ["io", "SocketService", "constructor", "socket", "isConnected", "listeners", "Map", "connect", "token", "serverUrl", "process", "env", "REACT_APP_SERVER_URL", "auth", "transports", "on", "console", "log", "error", "disconnect", "clear", "event", "callback", "has", "set", "get", "push", "off", "callbacks", "index", "indexOf", "splice", "emit", "data", "joinRoom", "room", "leaveRoom", "onTaskUpdate", "onPolicyUpdate", "onUserUpdate", "onCategoryUpdate", "onSubCategoryUpdate", "onPolicyHolderUpdate", "onTicketUpdate", "onClaimUpdate", "onNotification", "onSystemUpdate", "removeAllListeners", "for<PERSON>ach", "getConnectionStatus", "_this$socket", "socketId", "id", "socketService"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/services/socketService.js"], "sourcesContent": ["import io from 'socket.io-client';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.listeners = new Map();\n  }\n\n  connect(token) {\n    if (this.socket && this.isConnected) {\n      return this.socket;\n    }\n\n    const serverUrl = process.env.REACT_APP_SERVER_URL || 'http://localhost:5002';\n    \n    this.socket = io(serverUrl, {\n      auth: {\n        token: token\n      },\n      transports: ['websocket', 'polling']\n    });\n\n    this.socket.on('connect', () => {\n      console.log('✅ Connected to server via WebSocket');\n      this.isConnected = true;\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('❌ Disconnected from server');\n      this.isConnected = false;\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('❌ Socket connection error:', error);\n      this.isConnected = false;\n    });\n\n    return this.socket;\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n      this.listeners.clear();\n    }\n  }\n\n  // Real-time event listeners\n  on(event, callback) {\n    if (this.socket) {\n      this.socket.on(event, callback);\n      \n      // Store listener for cleanup\n      if (!this.listeners.has(event)) {\n        this.listeners.set(event, []);\n      }\n      this.listeners.get(event).push(callback);\n    }\n  }\n\n  off(event, callback) {\n    if (this.socket) {\n      this.socket.off(event, callback);\n      \n      // Remove from stored listeners\n      if (this.listeners.has(event)) {\n        const callbacks = this.listeners.get(event);\n        const index = callbacks.indexOf(callback);\n        if (index > -1) {\n          callbacks.splice(index, 1);\n        }\n      }\n    }\n  }\n\n  emit(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  // Join specific rooms for targeted updates\n  joinRoom(room) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('join_room', room);\n    }\n  }\n\n  leaveRoom(room) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('leave_room', room);\n    }\n  }\n\n  // Specific real-time event handlers\n  onTaskUpdate(callback) {\n    this.on('task_updated', callback);\n    this.on('task_created', callback);\n    this.on('task_deleted', callback);\n    this.on('task_assigned', callback);\n  }\n\n  onPolicyUpdate(callback) {\n    this.on('policy_updated', callback);\n    this.on('policy_created', callback);\n    this.on('policy_deleted', callback);\n  }\n\n  onUserUpdate(callback) {\n    this.on('user_updated', callback);\n    this.on('user_created', callback);\n    this.on('user_deleted', callback);\n  }\n\n  onCategoryUpdate(callback) {\n    this.on('category_updated', callback);\n    this.on('category_created', callback);\n    this.on('category_deleted', callback);\n  }\n\n  onSubCategoryUpdate(callback) {\n    this.on('subcategory_updated', callback);\n    this.on('subcategory_created', callback);\n    this.on('subcategory_deleted', callback);\n  }\n\n  onPolicyHolderUpdate(callback) {\n    this.on('policyholder_updated', callback);\n    this.on('policyholder_created', callback);\n    this.on('policyholder_deleted', callback);\n  }\n\n  onTicketUpdate(callback) {\n    this.on('ticket_updated', callback);\n    this.on('ticket_created', callback);\n    this.on('ticket_deleted', callback);\n  }\n\n  onClaimUpdate(callback) {\n    this.on('claim_updated', callback);\n    this.on('claim_created', callback);\n    this.on('claim_deleted', callback);\n  }\n\n  onNotification(callback) {\n    this.on('notification', callback);\n  }\n\n  onSystemUpdate(callback) {\n    this.on('system_update', callback);\n  }\n\n  // Cleanup all listeners\n  removeAllListeners() {\n    if (this.socket) {\n      this.listeners.forEach((callbacks, event) => {\n        callbacks.forEach(callback => {\n          this.socket.off(event, callback);\n        });\n      });\n      this.listeners.clear();\n    }\n  }\n\n  // Get connection status\n  getConnectionStatus() {\n    return {\n      isConnected: this.isConnected,\n      socketId: this.socket?.id || null\n    };\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,kBAAkB;AAEjC,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;EAEAC,OAAOA,CAACC,KAAK,EAAE;IACb,IAAI,IAAI,CAACL,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,OAAO,IAAI,CAACD,MAAM;IACpB;IAEA,MAAMM,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB;IAE7E,IAAI,CAACT,MAAM,GAAGH,EAAE,CAACS,SAAS,EAAE;MAC1BI,IAAI,EAAE;QACJL,KAAK,EAAEA;MACT,CAAC;MACDM,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACX,MAAM,CAACY,EAAE,CAAC,SAAS,EAAE,MAAM;MAC9BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAACb,WAAW,GAAG,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACD,MAAM,CAACY,EAAE,CAAC,YAAY,EAAE,MAAM;MACjCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,IAAI,CAACb,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACD,MAAM,CAACY,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;MACzCF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAACd,WAAW,GAAG,KAAK;IAC1B,CAAC,CAAC;IAEF,OAAO,IAAI,CAACD,MAAM;EACpB;EAEAgB,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACgB,UAAU,CAAC,CAAC;MACxB,IAAI,CAAChB,MAAM,GAAG,IAAI;MAClB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,SAAS,CAACe,KAAK,CAAC,CAAC;IACxB;EACF;;EAEA;EACAL,EAAEA,CAACM,KAAK,EAAEC,QAAQ,EAAE;IAClB,IAAI,IAAI,CAACnB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACY,EAAE,CAACM,KAAK,EAAEC,QAAQ,CAAC;;MAE/B;MACA,IAAI,CAAC,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAACF,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAChB,SAAS,CAACmB,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;MAC/B;MACA,IAAI,CAAChB,SAAS,CAACoB,GAAG,CAACJ,KAAK,CAAC,CAACK,IAAI,CAACJ,QAAQ,CAAC;IAC1C;EACF;EAEAK,GAAGA,CAACN,KAAK,EAAEC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACnB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACwB,GAAG,CAACN,KAAK,EAAEC,QAAQ,CAAC;;MAEhC;MACA,IAAI,IAAI,CAACjB,SAAS,CAACkB,GAAG,CAACF,KAAK,CAAC,EAAE;QAC7B,MAAMO,SAAS,GAAG,IAAI,CAACvB,SAAS,CAACoB,GAAG,CAACJ,KAAK,CAAC;QAC3C,MAAMQ,KAAK,GAAGD,SAAS,CAACE,OAAO,CAACR,QAAQ,CAAC;QACzC,IAAIO,KAAK,GAAG,CAAC,CAAC,EAAE;UACdD,SAAS,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC5B;MACF;IACF;EACF;EAEAG,IAAIA,CAACX,KAAK,EAAEY,IAAI,EAAE;IAChB,IAAI,IAAI,CAAC9B,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAAC6B,IAAI,CAACX,KAAK,EAAEY,IAAI,CAAC;IAC/B;EACF;;EAEA;EACAC,QAAQA,CAACC,IAAI,EAAE;IACb,IAAI,IAAI,CAAChC,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAAC6B,IAAI,CAAC,WAAW,EAAEG,IAAI,CAAC;IACrC;EACF;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,IAAI,CAAChC,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAAC6B,IAAI,CAAC,YAAY,EAAEG,IAAI,CAAC;IACtC;EACF;;EAEA;EACAE,YAAYA,CAACf,QAAQ,EAAE;IACrB,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACP,EAAE,CAAC,eAAe,EAAEO,QAAQ,CAAC;EACpC;EAEAgB,cAAcA,CAAChB,QAAQ,EAAE;IACvB,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;IACnC,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;IACnC,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;EACrC;EAEAiB,YAAYA,CAACjB,QAAQ,EAAE;IACrB,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;IACjC,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;EACnC;EAEAkB,gBAAgBA,CAAClB,QAAQ,EAAE;IACzB,IAAI,CAACP,EAAE,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;IACrC,IAAI,CAACP,EAAE,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;IACrC,IAAI,CAACP,EAAE,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;EACvC;EAEAmB,mBAAmBA,CAACnB,QAAQ,EAAE;IAC5B,IAAI,CAACP,EAAE,CAAC,qBAAqB,EAAEO,QAAQ,CAAC;IACxC,IAAI,CAACP,EAAE,CAAC,qBAAqB,EAAEO,QAAQ,CAAC;IACxC,IAAI,CAACP,EAAE,CAAC,qBAAqB,EAAEO,QAAQ,CAAC;EAC1C;EAEAoB,oBAAoBA,CAACpB,QAAQ,EAAE;IAC7B,IAAI,CAACP,EAAE,CAAC,sBAAsB,EAAEO,QAAQ,CAAC;IACzC,IAAI,CAACP,EAAE,CAAC,sBAAsB,EAAEO,QAAQ,CAAC;IACzC,IAAI,CAACP,EAAE,CAAC,sBAAsB,EAAEO,QAAQ,CAAC;EAC3C;EAEAqB,cAAcA,CAACrB,QAAQ,EAAE;IACvB,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;IACnC,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;IACnC,IAAI,CAACP,EAAE,CAAC,gBAAgB,EAAEO,QAAQ,CAAC;EACrC;EAEAsB,aAAaA,CAACtB,QAAQ,EAAE;IACtB,IAAI,CAACP,EAAE,CAAC,eAAe,EAAEO,QAAQ,CAAC;IAClC,IAAI,CAACP,EAAE,CAAC,eAAe,EAAEO,QAAQ,CAAC;IAClC,IAAI,CAACP,EAAE,CAAC,eAAe,EAAEO,QAAQ,CAAC;EACpC;EAEAuB,cAAcA,CAACvB,QAAQ,EAAE;IACvB,IAAI,CAACP,EAAE,CAAC,cAAc,EAAEO,QAAQ,CAAC;EACnC;EAEAwB,cAAcA,CAACxB,QAAQ,EAAE;IACvB,IAAI,CAACP,EAAE,CAAC,eAAe,EAAEO,QAAQ,CAAC;EACpC;;EAEA;EACAyB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5C,MAAM,EAAE;MACf,IAAI,CAACE,SAAS,CAAC2C,OAAO,CAAC,CAACpB,SAAS,EAAEP,KAAK,KAAK;QAC3CO,SAAS,CAACoB,OAAO,CAAC1B,QAAQ,IAAI;UAC5B,IAAI,CAACnB,MAAM,CAACwB,GAAG,CAACN,KAAK,EAAEC,QAAQ,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACjB,SAAS,CAACe,KAAK,CAAC,CAAC;IACxB;EACF;;EAEA;EACA6B,mBAAmBA,CAAA,EAAG;IAAA,IAAAC,YAAA;IACpB,OAAO;MACL9C,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B+C,QAAQ,EAAE,EAAAD,YAAA,OAAI,CAAC/C,MAAM,cAAA+C,YAAA,uBAAXA,YAAA,CAAaE,EAAE,KAAI;IAC/B,CAAC;EACH;AACF;;AAEA;AACA,MAAMC,aAAa,GAAG,IAAIpD,aAAa,CAAC,CAAC;AAEzC,eAAeoD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}