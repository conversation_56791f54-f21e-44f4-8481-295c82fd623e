import React from 'react';
import { NavLink } from 'react-router-dom';
// import './Sidebar.css';

const Sidebar = () => {
  const menu = [
    { to: '/dashboard', label: 'Dashboard', icon: 'fas fa-chart-line' },
    { to: '/categories', label: 'Categories', icon: 'fas fa-folder' },
    { to: '/subcategories', label: 'SubCategories', icon: 'fas fa-folder-open' },
    { to: '/ticketcategories', label: 'Ticket Categories', icon: 'fas fa-ticket-alt' },
    { to: '/insurance-policy', label: 'Insurance Policy', icon: 'fas fa-file-signature' },
    { to: '/staff', label: 'Staff', icon: 'fas fa-user-tie' },
    { to: '/users', label: 'Users', icon: 'fas fa-users' },
    { to: '/policy-holder', label: 'Policy Holder', icon: 'fas fa-id-card' },
    { to: '/support-ticket', label: 'Support Ticket', icon: 'fas fa-headset' },
    { to: '/report-tool', label: 'Report Tool', icon: 'fas fa-chart-bar' },
    { to: '/system-settings', label: 'System Settings', icon: 'fas fa-cogs' },
  ];

  return (
    <div
      className="sidebar d-flex flex-column bg-primary text-white vh-100 p-3"
      style={{ minWidth: '260px', maxWidth: '300px' }}
    >
      <h4 className="text-center mb-4 fw-bold">Morya Insurance</h4>
      {menu.map((item, idx) => (
        <NavLink
          key={idx}
          to={item.to}
          className={({ isActive }) =>
            `text-white py-2 px-3 sidebar-link d-block ${isActive ? 'active-link' : ''}`
          }
        >
          <i className={`${item.icon} me-2`}></i> {item.label}
        </NavLink>
      ))}
    </div>
  );
};

export default Sidebar;
