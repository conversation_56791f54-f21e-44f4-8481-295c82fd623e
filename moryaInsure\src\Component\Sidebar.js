import React from 'react';
import { NavLink } from 'react-router-dom';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';

const Sidebar = ({ collapsed = false }) => {
  const { user } = useAuth();

  // Define menu items with role-based access
  const allMenuItems = [
    { to: '/dashboard', label: 'Dashboard', icon: 'fas fa-chart-line', roles: ['admin', 'employee', 'customer'] },
   // Admin only
    { to: '/categories', label: 'Categories', icon: 'fas fa-folder', roles: ['admin'] },
    { to: '/subcategories', label: 'SubCategories', icon: 'fas fa-folder-open', roles: ['admin'] },
    { to: '/ticketcategories', label: 'Ticket Categories', icon: 'fas fa-ticket-alt', roles: ['admin'] },
    { to: '/staff', label: 'Staff', icon: 'fas fa-user-tie', roles: ['admin'] },
    { to: '/users', label: 'Users', icon: 'fas fa-users', roles: ['admin'] },
    { to: '/system-settings', label: 'System Settings', icon: 'fas fa-cogs', roles: ['admin'] },

    // Admin and Employee
    { to: '/insurance-policy', label: 'Insurance Policy', icon: 'fas fa-file-signature', roles: ['admin', 'employee'] },
    { to: '/policy-holder', label: 'Policy Holder', icon: 'fas fa-id-card', roles: ['admin', 'employee'] },
    { to: '/support-ticket', label: 'Support Ticket', icon: 'fas fa-headset', roles: ['admin', 'employee'] },
    { to: '/report-tool', label: 'Report Tool', icon: 'fas fa-chart-bar', roles: ['admin', 'employee'] },

    // Task Management
    { to: '/task-management', label: 'Task Management', icon: 'fas fa-tasks', roles: ['admin'] },
    { to: '/my-tasks', label: 'My Tasks', icon: 'fas fa-clipboard-list', roles: ['employee'] },

    // Customer only
    { to: '/claims', label: 'My Claims', icon: 'fas fa-file-medical', roles: ['customer'] },
    { to: '/contact-support', label: 'Contact Support', icon: 'fas fa-life-ring', roles: ['customer'] },
  ];

  // Filter menu items based on user role
  const menu = allMenuItems.filter(item => {
    if (!user) return false;
    return item.roles.includes(user.role);
  });

  const renderMenuItem = (item, idx) => {
    const linkContent = (
      <NavLink
        key={idx}
        to={item.to}
        className={({ isActive }) =>
          `text-white py-2 px-3 sidebar-link d-block text-decoration-none ${
            isActive ? 'active-link bg-light bg-opacity-25 rounded' : ''
          }`
        }
        style={{
          transition: 'all 0.3s ease',
          whiteSpace: 'nowrap',
          overflow: 'hidden'
        }}
      >
        <i className={`${item.icon} ${collapsed ? '' : 'me-2'}`}></i>
        {!collapsed && <span>{item.label}</span>}
      </NavLink>
    );

    if (collapsed) {
      return (
        <OverlayTrigger
          key={idx}
          placement="right"
          overlay={<Tooltip>{item.label}</Tooltip>}
        >
          {linkContent}
        </OverlayTrigger>
      );
    }

    return linkContent;
  };

  return (
    <div
      className="sidebar d-flex flex-column bg-primary text-white position-fixed"
      style={{
        width: collapsed ? '70px' : '260px',
        height: '100vh',
        top: '56px', // Navbar height
        left: 0,
        transition: 'width 0.3s ease',
        zIndex: 999,
        overflowX: 'hidden',
        overflowY: 'auto'
      }}
    >
      <div className="flex-grow-1">
        {menu.map((item, idx) => renderMenuItem(item, idx))}
      </div>
    </div>
  );
};

export default Sidebar;
