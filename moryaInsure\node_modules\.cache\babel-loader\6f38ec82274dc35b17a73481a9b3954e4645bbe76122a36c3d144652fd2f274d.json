{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\layout\\\\MainLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Outlet } from \"react-router-dom\";\nimport Sidebar from \"../Component/Sidebar\";\nimport Navbar from \"../Component/Navbar\";\nimport Footer from \"../Component/Footer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainLayout = () => {\n  _s();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      toggleSidebar: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex flex-grow-1 position-relative\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        collapsed: sidebarCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-grow-1 p-4 main-content\",\n        style: {\n          marginLeft: sidebarCollapsed ? \"70px\" : \"260px\",\n          transition: \"margin-left 0.3s ease\",\n          paddingTop: \"20px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"RBWGVlQtqnwMoScoawcFwyZtGjQ=\");\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Sidebar", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "MainLayout", "_s", "sidebarCollapsed", "setSidebarCollapsed", "toggleSidebar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "collapsed", "style", "marginLeft", "transition", "paddingTop", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/layout/MainLayout.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Outlet } from \"react-router-dom\";\r\nimport Sidebar from \"../Component/Sidebar\";\r\nimport Navbar from \"../Component/Navbar\";\r\nimport Footer from \"../Component/Footer\";\r\n\r\nconst MainLayout = () => {\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebarCollapsed(!sidebarCollapsed);\r\n  };\r\n\r\n  return (\r\n    <div className=\"d-flex flex-column min-vh-100\">\r\n      <Navbar toggleSidebar={toggleSidebar} />\r\n      <div className=\"d-flex flex-grow-1 position-relative\">\r\n        <Sidebar collapsed={sidebarCollapsed} />\r\n        <div\r\n          className=\"flex-grow-1 p-4 main-content\"\r\n          style={{\r\n            marginLeft: sidebarCollapsed ? \"70px\" : \"260px\",\r\n            transition: \"margin-left 0.3s ease\",\r\n            paddingTop: \"20px\"\r\n          }}\r\n        >\r\n          <Outlet />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MainLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1BD,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACEH,OAAA;IAAKM,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAC5CP,OAAA,CAACH,MAAM;MAACQ,aAAa,EAAEA;IAAc;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxCX,OAAA;MAAKM,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDP,OAAA,CAACJ,OAAO;QAACgB,SAAS,EAAET;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxCX,OAAA;QACEM,SAAS,EAAC,8BAA8B;QACxCO,KAAK,EAAE;UACLC,UAAU,EAAEX,gBAAgB,GAAG,MAAM,GAAG,OAAO;UAC/CY,UAAU,EAAE,uBAAuB;UACnCC,UAAU,EAAE;QACd,CAAE;QAAAT,QAAA,eAEFP,OAAA,CAACL,MAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNX,OAAA,CAACF,MAAM;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACT,EAAA,CA1BID,UAAU;AAAAgB,EAAA,GAAVhB,UAAU;AA4BhB,eAAeA,UAAU;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}