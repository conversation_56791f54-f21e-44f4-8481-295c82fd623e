const mongoose = require('mongoose');

const policySchema = new mongoose.Schema({
  policyNumber: {
    type: String,
    unique: true,
    required: [true, 'Policy number is required']
  },
  policyHolder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Policy holder is required']
  },
  type: {
    type: String,
    enum: ['life', 'health', 'auto', 'home', 'business', 'travel'],
    required: [true, 'Policy type is required']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Policy category is required']
  },
  status: {
    type: String,
    enum: ['draft', 'pending', 'approved', 'active', 'suspended', 'cancelled', 'expired'],
    default: 'draft'
  },
  coverageAmount: {
    type: Number,
    required: [true, 'Coverage amount is required'],
    min: [0, 'Coverage amount must be positive']
  },
  premiumAmount: {
    type: Number,
    required: [true, 'Premium amount is required'],
    min: [0, 'Premium amount must be positive']
  },
  premiumFrequency: {
    type: String,
    enum: ['monthly', 'quarterly', 'semi-annual', 'annual'],
    default: 'monthly'
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  renewalDate: {
    type: Date
  },
  beneficiaries: [{
    name: {
      type: String,
      required: true
    },
    relationship: {
      type: String,
      required: true
    },
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    },
    contactInfo: {
      phone: String,
      email: String,
      address: String
    }
  }],
  terms: {
    deductible: {
      type: Number,
      default: 0
    },
    copayment: {
      type: Number,
      default: 0
    },
    waitingPeriod: {
      type: Number, // in days
      default: 0
    },
    exclusions: [String],
    conditions: [String]
  },
  documents: [{
    name: String,
    type: String,
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  assignedAgent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: {
    type: Date
  },
  notes: [{
    content: String,
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  paymentHistory: [{
    amount: Number,
    paymentDate: Date,
    paymentMethod: String,
    transactionId: String,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending'
    }
  }],
  claims: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Claim'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for policy age
policySchema.virtual('policyAge').get(function() {
  if (!this.startDate) return 0;
  const now = new Date();
  const start = new Date(this.startDate);
  return Math.floor((now - start) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for days until expiry
policySchema.virtual('daysUntilExpiry').get(function() {
  if (!this.endDate) return null;
  const now = new Date();
  const end = new Date(this.endDate);
  return Math.ceil((end - now) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for next premium due date
policySchema.virtual('nextPremiumDue').get(function() {
  if (!this.startDate || !this.premiumFrequency) return null;
  
  const start = new Date(this.startDate);
  const now = new Date();
  
  let monthsToAdd;
  switch (this.premiumFrequency) {
    case 'monthly': monthsToAdd = 1; break;
    case 'quarterly': monthsToAdd = 3; break;
    case 'semi-annual': monthsToAdd = 6; break;
    case 'annual': monthsToAdd = 12; break;
    default: monthsToAdd = 1;
  }
  
  let nextDue = new Date(start);
  while (nextDue <= now) {
    nextDue.setMonth(nextDue.getMonth() + monthsToAdd);
  }
  
  return nextDue;
});

// Index for better query performance
policySchema.index({ policyNumber: 1 });
policySchema.index({ policyHolder: 1 });
policySchema.index({ type: 1 });
policySchema.index({ status: 1 });
policySchema.index({ assignedAgent: 1 });
policySchema.index({ startDate: 1, endDate: 1 });

// Pre-save middleware to generate policy number
policySchema.pre('save', async function(next) {
  if (!this.policyNumber) {
    const typePrefix = this.type.toUpperCase().substring(0, 2);
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({ type: this.type });
    this.policyNumber = `${typePrefix}-${year}-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

// Static method to find policies by status
policySchema.statics.findByStatus = function(status) {
  return this.find({ status }).populate('policyHolder assignedAgent');
};

// Static method to find expiring policies
policySchema.statics.findExpiringSoon = function(days = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    endDate: { $lte: futureDate },
    status: { $in: ['active', 'approved'] }
  }).populate('policyHolder');
};

// Method to calculate total premiums paid
policySchema.methods.getTotalPremiumsPaid = function() {
  return this.paymentHistory
    .filter(payment => payment.status === 'completed')
    .reduce((total, payment) => total + payment.amount, 0);
};

module.exports = mongoose.model('Policy', policySchema);
