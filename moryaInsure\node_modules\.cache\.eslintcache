[{"D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js": "1", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js": "2", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js": "3", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js": "4", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js": "5", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js": "6", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js": "7", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js": "8", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js": "9", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js": "10", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js": "11", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js": "12", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js": "13", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js": "14", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js": "15", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js": "16", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js": "17", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js": "18", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js": "19", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js": "20", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js": "21", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js": "22", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js": "23", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js": "24", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js": "25", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js": "26", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js": "27", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js": "28", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js": "29", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js": "30", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js": "31", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js": "32", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js": "33", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js": "34", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js": "35", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js": "36"}, {"size": 861, "mtime": 1750664705717, "results": "37", "hashOfConfig": "38"}, {"size": 375, "mtime": 1750664705720, "results": "39", "hashOfConfig": "38"}, {"size": 4724, "mtime": 1751307309433, "results": "40", "hashOfConfig": "38"}, {"size": 6512, "mtime": 1751308012589, "results": "41", "hashOfConfig": "38"}, {"size": 1156, "mtime": 1750680083390, "results": "42", "hashOfConfig": "38"}, {"size": 6504, "mtime": 1751308316867, "results": "43", "hashOfConfig": "38"}, {"size": 7370, "mtime": 1751308117361, "results": "44", "hashOfConfig": "38"}, {"size": 14934, "mtime": 1751305766103, "results": "45", "hashOfConfig": "38"}, {"size": 1449, "mtime": 1751305387915, "results": "46", "hashOfConfig": "38"}, {"size": 15160, "mtime": 1751306101742, "results": "47", "hashOfConfig": "38"}, {"size": 7036, "mtime": 1751308353133, "results": "48", "hashOfConfig": "38"}, {"size": 7920, "mtime": 1751308292943, "results": "49", "hashOfConfig": "38"}, {"size": 3716, "mtime": 1750664705714, "results": "50", "hashOfConfig": "38"}, {"size": 7140, "mtime": 1751308435824, "results": "51", "hashOfConfig": "38"}, {"size": 1039, "mtime": 1750752443938, "results": "52", "hashOfConfig": "38"}, {"size": 3374, "mtime": 1751307537804, "results": "53", "hashOfConfig": "38"}, {"size": 3518, "mtime": 1751305611911, "results": "54", "hashOfConfig": "38"}, {"size": 4290, "mtime": 1750748793241, "results": "55", "hashOfConfig": "38"}, {"size": 6559, "mtime": 1750665607130, "results": "56", "hashOfConfig": "38"}, {"size": 3401, "mtime": 1750665567901, "results": "57", "hashOfConfig": "38"}, {"size": 4103, "mtime": 1750665584344, "results": "58", "hashOfConfig": "38"}, {"size": 5154, "mtime": 1751300690467, "results": "59", "hashOfConfig": "38"}, {"size": 10248, "mtime": 1751301038102, "results": "60", "hashOfConfig": "38"}, {"size": 1648, "mtime": 1750678907126, "results": "61", "hashOfConfig": "38"}, {"size": 6414, "mtime": 1751301017596, "results": "62", "hashOfConfig": "38"}, {"size": 29847, "mtime": 1751310631795, "results": "63", "hashOfConfig": "38"}, {"size": 9785, "mtime": 1750761152602, "results": "64", "hashOfConfig": "38"}, {"size": 14616, "mtime": 1751307505174, "results": "65", "hashOfConfig": "38"}, {"size": 7039, "mtime": 1751307261059, "results": "66", "hashOfConfig": "38"}, {"size": 8129, "mtime": 1750755972872, "results": "67", "hashOfConfig": "38"}, {"size": 6195, "mtime": 1750678949437, "results": "68", "hashOfConfig": "38"}, {"size": 9077, "mtime": 1750754399649, "results": "69", "hashOfConfig": "38"}, {"size": 1778, "mtime": 1750754365053, "results": "70", "hashOfConfig": "38"}, {"size": 9340, "mtime": 1751300946595, "results": "71", "hashOfConfig": "38"}, {"size": 12948, "mtime": 1751307182308, "results": "72", "hashOfConfig": "38"}, {"size": 17516, "mtime": 1751307243672, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yjkqrc", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js", ["182", "183"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js", ["184", "185"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js", ["186", "187"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js", ["188"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js", ["189"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js", ["190", "191", "192", "193", "194", "195"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js", ["196", "197"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js", ["198", "199"], [], {"ruleId": "200", "severity": 1, "message": "201", "line": 3, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 3, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "204", "line": 6, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 18}, {"ruleId": "200", "severity": 1, "message": "201", "line": 3, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 3, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "204", "line": 6, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 18}, {"ruleId": "200", "severity": 1, "message": "201", "line": 5, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 5, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "204", "line": 8, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 8, "endColumn": 18}, {"ruleId": "200", "severity": 1, "message": "205", "line": 5, "column": 35, "nodeType": "202", "messageId": "203", "endLine": 5, "endColumn": 40}, {"ruleId": "200", "severity": 1, "message": "206", "line": 20, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 20, "endColumn": 17}, {"ruleId": "200", "severity": 1, "message": "207", "line": 2, "column": 65, "nodeType": "202", "messageId": "203", "endLine": 2, "endColumn": 70}, {"ruleId": "200", "severity": 1, "message": "208", "line": 6, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 16}, {"ruleId": "200", "severity": 1, "message": "205", "line": 6, "column": 18, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 23}, {"ruleId": "200", "severity": 1, "message": "209", "line": 6, "column": 25, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 34}, {"ruleId": "200", "severity": 1, "message": "210", "line": 6, "column": 36, "nodeType": "202", "messageId": "203", "endLine": 6, "endColumn": 48}, {"ruleId": "211", "severity": 1, "message": "212", "line": 37, "column": 6, "nodeType": "213", "endLine": 37, "endColumn": 8, "suggestions": "214"}, {"ruleId": "200", "severity": 1, "message": "215", "line": 13, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 13, "endColumn": 23}, {"ruleId": "211", "severity": 1, "message": "216", "line": 29, "column": 6, "nodeType": "213", "endLine": 29, "endColumn": 8, "suggestions": "217"}, {"ruleId": "200", "severity": 1, "message": "218", "line": 8, "column": 11, "nodeType": "202", "messageId": "203", "endLine": 8, "endColumn": 15}, {"ruleId": "200", "severity": 1, "message": "219", "line": 10, "column": 10, "nodeType": "202", "messageId": "203", "endLine": 10, "endColumn": 20}, "no-unused-vars", "'FaUser' is defined but never used.", "Identifier", "unusedVar", "'username' is assigned a value but never used.", "'FaEye' is defined but never used.", "'loading' is assigned a value but never used.", "'Table' is defined but never used.", "'FaPlus' is defined but never used.", "'FaFileAlt' is defined but never used.", "'FaCreditCard' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["220"], "'selectedClaim' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPolicies'. Either include it or remove the dependency array.", ["221"], "'user' is assigned a value but never used.", "'categories' is assigned a value but never used.", {"desc": "222", "fix": "223"}, {"desc": "224", "fix": "225"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "226", "text": "227"}, "Update the dependencies array to be: [fetchPolicies]", {"range": "228", "text": "229"}, [1176, 1178], "[fetchDashboardData]", [1072, 1074], "[fetchPolicies]"]