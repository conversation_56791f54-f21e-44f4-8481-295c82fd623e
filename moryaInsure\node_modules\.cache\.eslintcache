[{"D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js": "1", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js": "2", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js": "3", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js": "4", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js": "5", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js": "6", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js": "7", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js": "8", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js": "9", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js": "10", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js": "11", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js": "12", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js": "13", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js": "14", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js": "15", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js": "16", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js": "17", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js": "18", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js": "19", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js": "20", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js": "21", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js": "22", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js": "23", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js": "24", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js": "25", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js": "26", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js": "27", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js": "28", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js": "29", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js": "30", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js": "31"}, {"size": 861, "mtime": 1750664705717, "results": "32", "hashOfConfig": "33"}, {"size": 375, "mtime": 1750664705720, "results": "34", "hashOfConfig": "33"}, {"size": 4079, "mtime": 1750678969806, "results": "35", "hashOfConfig": "33"}, {"size": 6661, "mtime": 1750664705705, "results": "36", "hashOfConfig": "33"}, {"size": 1181, "mtime": 1750665882854, "results": "37", "hashOfConfig": "33"}, {"size": 6428, "mtime": 1750664705709, "results": "38", "hashOfConfig": "33"}, {"size": 7511, "mtime": 1750664705715, "results": "39", "hashOfConfig": "33"}, {"size": 191, "mtime": 1750664705712, "results": "40", "hashOfConfig": "33"}, {"size": 1725, "mtime": 1750664705710, "results": "41", "hashOfConfig": "33"}, {"size": 3708, "mtime": 1750664705716, "results": "42", "hashOfConfig": "33"}, {"size": 6948, "mtime": 1750664705710, "results": "43", "hashOfConfig": "33"}, {"size": 8049, "mtime": 1750664705712, "results": "44", "hashOfConfig": "33"}, {"size": 3716, "mtime": 1750664705714, "results": "45", "hashOfConfig": "33"}, {"size": 7096, "mtime": 1750664705714, "results": "46", "hashOfConfig": "33"}, {"size": 1005, "mtime": 1750678800219, "results": "47", "hashOfConfig": "33"}, {"size": 3901, "mtime": 1750665837249, "results": "48", "hashOfConfig": "33"}, {"size": 3336, "mtime": 1750665785327, "results": "49", "hashOfConfig": "33"}, {"size": 4046, "mtime": 1750665523833, "results": "50", "hashOfConfig": "33"}, {"size": 6559, "mtime": 1750665607130, "results": "51", "hashOfConfig": "33"}, {"size": 3401, "mtime": 1750665567901, "results": "52", "hashOfConfig": "33"}, {"size": 4103, "mtime": 1750665584344, "results": "53", "hashOfConfig": "33"}, {"size": 3253, "mtime": 1750678833106, "results": "54", "hashOfConfig": "33"}, {"size": 10917, "mtime": 1750666749400, "results": "55", "hashOfConfig": "33"}, {"size": 1648, "mtime": 1750678907126, "results": "56", "hashOfConfig": "33"}, {"size": 6086, "mtime": 1750678922410, "results": "57", "hashOfConfig": "33"}, {"size": 8302, "mtime": 1750666789072, "results": "58", "hashOfConfig": "33"}, {"size": 7944, "mtime": 1750665951382, "results": "59", "hashOfConfig": "33"}, {"size": 9559, "mtime": 1750665989269, "results": "60", "hashOfConfig": "33"}, {"size": 3840, "mtime": 1750678417589, "results": "61", "hashOfConfig": "33"}, {"size": 7836, "mtime": 1750678874427, "results": "62", "hashOfConfig": "33"}, {"size": 6195, "mtime": 1750678949437, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ue8fkt", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js", ["157", "158"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js", ["159", "160"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js", ["161", "162", "163", "164"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js", ["165"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js", [], [], {"ruleId": "166", "severity": 1, "message": "167", "line": 1, "column": 17, "nodeType": "168", "messageId": "169", "endLine": 1, "endColumn": 25}, {"ruleId": "166", "severity": 1, "message": "170", "line": 1, "column": 27, "nodeType": "168", "messageId": "169", "endLine": 1, "endColumn": 36}, {"ruleId": "166", "severity": 1, "message": "171", "line": 7, "column": 17, "nodeType": "168", "messageId": "169", "endLine": 7, "endColumn": 24}, {"ruleId": "166", "severity": 1, "message": "172", "line": 7, "column": 26, "nodeType": "168", "messageId": "169", "endLine": 7, "endColumn": 36}, {"ruleId": "173", "severity": 1, "message": "174", "line": 19, "column": 15, "nodeType": "175", "endLine": 19, "endColumn": 50}, {"ruleId": "173", "severity": 1, "message": "174", "line": 22, "column": 15, "nodeType": "175", "endLine": 22, "endColumn": 50}, {"ruleId": "173", "severity": 1, "message": "174", "line": 25, "column": 15, "nodeType": "175", "endLine": 25, "endColumn": 50}, {"ruleId": "173", "severity": 1, "message": "174", "line": 28, "column": 15, "nodeType": "175", "endLine": 28, "endColumn": 50}, {"ruleId": "166", "severity": 1, "message": "176", "line": 2, "column": 52, "nodeType": "168", "messageId": "169", "endLine": 2, "endColumn": 63}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'hasRole' is assigned a value but never used.", "'hasAnyRole' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'ProgressBar' is defined but never used."]