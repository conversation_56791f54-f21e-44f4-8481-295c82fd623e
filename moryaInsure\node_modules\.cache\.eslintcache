[{"D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js": "1", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js": "2", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js": "3", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js": "4", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js": "5", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js": "6", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js": "7", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js": "8", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js": "9", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js": "10", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js": "11", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js": "12", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js": "13", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js": "14", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js": "15", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js": "16", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js": "17", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js": "18", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js": "19", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js": "20", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js": "21", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js": "22", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js": "23", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js": "24", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js": "25", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js": "26", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js": "27", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js": "28", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js": "29", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js": "30", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js": "31", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js": "32", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js": "33", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js": "34", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js": "35", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js": "36", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TaskManagement.js": "37", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\MyTasks.js": "38", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ConnectionStatus.js": "39", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ThemeToggle.js": "40", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\contexts\\ThemeContext.js": "41", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\contexts\\RealtimeContext.js": "42", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\socketService.js": "43"}, {"size": 861, "mtime": 1750664705717, "results": "44", "hashOfConfig": "45"}, {"size": 375, "mtime": 1750664705720, "results": "46", "hashOfConfig": "45"}, {"size": 5746, "mtime": 1751344509022, "results": "47", "hashOfConfig": "45"}, {"size": 15716, "mtime": 1751312421855, "results": "48", "hashOfConfig": "45"}, {"size": 1156, "mtime": 1750680083390, "results": "49", "hashOfConfig": "45"}, {"size": 26325, "mtime": 1751314989689, "results": "50", "hashOfConfig": "45"}, {"size": 7370, "mtime": 1751308117361, "results": "51", "hashOfConfig": "45"}, {"size": 14934, "mtime": 1751305766103, "results": "52", "hashOfConfig": "45"}, {"size": 1449, "mtime": 1751305387915, "results": "53", "hashOfConfig": "45"}, {"size": 15160, "mtime": 1751306101742, "results": "54", "hashOfConfig": "45"}, {"size": 18145, "mtime": 1751313489947, "results": "55", "hashOfConfig": "45"}, {"size": 19787, "mtime": 1751344679993, "results": "56", "hashOfConfig": "45"}, {"size": 18562, "mtime": 1751312979068, "results": "57", "hashOfConfig": "45"}, {"size": 7140, "mtime": 1751308435824, "results": "58", "hashOfConfig": "45"}, {"size": 1039, "mtime": 1750752443938, "results": "59", "hashOfConfig": "45"}, {"size": 3596, "mtime": 1751311677630, "results": "60", "hashOfConfig": "45"}, {"size": 3518, "mtime": 1751305611911, "results": "61", "hashOfConfig": "45"}, {"size": 4290, "mtime": 1750748793241, "results": "62", "hashOfConfig": "45"}, {"size": 6559, "mtime": 1750665607130, "results": "63", "hashOfConfig": "45"}, {"size": 3401, "mtime": 1750665567901, "results": "64", "hashOfConfig": "45"}, {"size": 4103, "mtime": 1750665584344, "results": "65", "hashOfConfig": "45"}, {"size": 5154, "mtime": 1751300690467, "results": "66", "hashOfConfig": "45"}, {"size": 10248, "mtime": 1751301038102, "results": "67", "hashOfConfig": "45"}, {"size": 1648, "mtime": 1750678907126, "results": "68", "hashOfConfig": "45"}, {"size": 6414, "mtime": 1751301017596, "results": "69", "hashOfConfig": "45"}, {"size": 29847, "mtime": 1751310631795, "results": "70", "hashOfConfig": "45"}, {"size": 9785, "mtime": 1750761152602, "results": "71", "hashOfConfig": "45"}, {"size": 14616, "mtime": 1751307505174, "results": "72", "hashOfConfig": "45"}, {"size": 9967, "mtime": 1751343420403, "results": "73", "hashOfConfig": "45"}, {"size": 8129, "mtime": 1750755972872, "results": "74", "hashOfConfig": "45"}, {"size": 6195, "mtime": 1750678949437, "results": "75", "hashOfConfig": "45"}, {"size": 9077, "mtime": 1750754399649, "results": "76", "hashOfConfig": "45"}, {"size": 1778, "mtime": 1750754365053, "results": "77", "hashOfConfig": "45"}, {"size": 9340, "mtime": 1751300946595, "results": "78", "hashOfConfig": "45"}, {"size": 12948, "mtime": 1751307182308, "results": "79", "hashOfConfig": "45"}, {"size": 17516, "mtime": 1751307243672, "results": "80", "hashOfConfig": "45"}, {"size": 22128, "mtime": 1751343400861, "results": "81", "hashOfConfig": "45"}, {"size": 24180, "mtime": 1751311632495, "results": "82", "hashOfConfig": "45"}, {"size": 592, "mtime": 1751344443858, "results": "83", "hashOfConfig": "45"}, {"size": 789, "mtime": 1751344433238, "results": "84", "hashOfConfig": "45"}, {"size": 2001, "mtime": 1751344372216, "results": "85", "hashOfConfig": "45"}, {"size": 6242, "mtime": 1751344355585, "results": "86", "hashOfConfig": "45"}, {"size": 4218, "mtime": 1751344325881, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yjkqrc", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js", ["217", "218", "219"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js", ["220"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js", ["221", "222"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js", ["223", "224", "225", "226"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js", ["227", "228"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js", ["229", "230"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js", ["231"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js", ["232"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js", ["233", "234", "235", "236", "237", "238"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js", ["239", "240"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js", ["241", "242"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TaskManagement.js", ["243", "244", "245"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\MyTasks.js", ["246", "247", "248", "249"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ConnectionStatus.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ThemeToggle.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\contexts\\ThemeContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\contexts\\RealtimeContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\socketService.js", [], [], {"ruleId": "250", "severity": 1, "message": "251", "line": 27, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 27, "endColumn": 25}, {"ruleId": "250", "severity": 1, "message": "254", "line": 132, "column": 9, "nodeType": "252", "messageId": "253", "endLine": 132, "endColumn": 25}, {"ruleId": "250", "severity": 1, "message": "255", "line": 136, "column": 9, "nodeType": "252", "messageId": "253", "endLine": 136, "endColumn": 25}, {"ruleId": "250", "severity": 1, "message": "256", "line": 21, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 21, "endColumn": 19}, {"ruleId": "250", "severity": 1, "message": "257", "line": 3, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 16}, {"ruleId": "250", "severity": 1, "message": "258", "line": 6, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 6, "endColumn": 18}, {"ruleId": "250", "severity": 1, "message": "259", "line": 8, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 8, "endColumn": 18}, {"ruleId": "250", "severity": 1, "message": "260", "line": 16, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 16, "endColumn": 23}, {"ruleId": "250", "severity": 1, "message": "261", "line": 17, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 17, "endColumn": 23}, {"ruleId": "250", "severity": 1, "message": "262", "line": 121, "column": 9, "nodeType": "252", "messageId": "253", "endLine": 121, "endColumn": 19}, {"ruleId": "250", "severity": 1, "message": "251", "line": 24, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 24, "endColumn": 25}, {"ruleId": "250", "severity": 1, "message": "255", "line": 202, "column": 9, "nodeType": "252", "messageId": "253", "endLine": 202, "endColumn": 25}, {"ruleId": "250", "severity": 1, "message": "263", "line": 3, "column": 26, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 36}, {"ruleId": "250", "severity": 1, "message": "264", "line": 3, "column": 38, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 46}, {"ruleId": "250", "severity": 1, "message": "265", "line": 5, "column": 35, "nodeType": "252", "messageId": "253", "endLine": 5, "endColumn": 40}, {"ruleId": "250", "severity": 1, "message": "266", "line": 20, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 20, "endColumn": 17}, {"ruleId": "250", "severity": 1, "message": "267", "line": 2, "column": 65, "nodeType": "252", "messageId": "253", "endLine": 2, "endColumn": 70}, {"ruleId": "250", "severity": 1, "message": "268", "line": 6, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 6, "endColumn": 16}, {"ruleId": "250", "severity": 1, "message": "265", "line": 6, "column": 18, "nodeType": "252", "messageId": "253", "endLine": 6, "endColumn": 23}, {"ruleId": "250", "severity": 1, "message": "269", "line": 6, "column": 25, "nodeType": "252", "messageId": "253", "endLine": 6, "endColumn": 34}, {"ruleId": "250", "severity": 1, "message": "270", "line": 6, "column": 36, "nodeType": "252", "messageId": "253", "endLine": 6, "endColumn": 48}, {"ruleId": "271", "severity": 1, "message": "272", "line": 37, "column": 6, "nodeType": "273", "endLine": 37, "endColumn": 8, "suggestions": "274"}, {"ruleId": "250", "severity": 1, "message": "275", "line": 13, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 13, "endColumn": 23}, {"ruleId": "271", "severity": 1, "message": "276", "line": 29, "column": 6, "nodeType": "273", "endLine": 29, "endColumn": 8, "suggestions": "277"}, {"ruleId": "250", "severity": 1, "message": "278", "line": 8, "column": 11, "nodeType": "252", "messageId": "253", "endLine": 8, "endColumn": 15}, {"ruleId": "250", "severity": 1, "message": "279", "line": 10, "column": 10, "nodeType": "252", "messageId": "253", "endLine": 10, "endColumn": 20}, {"ruleId": "250", "severity": 1, "message": "280", "line": 3, "column": 42, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 49}, {"ruleId": "250", "severity": 1, "message": "278", "line": 8, "column": 11, "nodeType": "252", "messageId": "253", "endLine": 8, "endColumn": 15}, {"ruleId": "271", "severity": 1, "message": "281", "line": 35, "column": 6, "nodeType": "273", "endLine": 35, "endColumn": 17, "suggestions": "282"}, {"ruleId": "250", "severity": 1, "message": "283", "line": 3, "column": 25, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 32}, {"ruleId": "250", "severity": 1, "message": "284", "line": 3, "column": 34, "nodeType": "252", "messageId": "253", "endLine": 3, "endColumn": 41}, {"ruleId": "250", "severity": 1, "message": "278", "line": 8, "column": 11, "nodeType": "252", "messageId": "253", "endLine": 8, "endColumn": 15}, {"ruleId": "271", "severity": 1, "message": "285", "line": 32, "column": 6, "nodeType": "273", "endLine": 32, "endColumn": 17, "suggestions": "286"}, "no-unused-vars", "'showImportModal' is assigned a value but never used.", "Identifier", "unusedVar", "'handleFileChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "'modalMode' is assigned a value but never used.", "'FaUser' is defined but never used.", "'username' is assigned a value but never used.", "'policies' is assigned a value but never used.", "'showEditModal' is assigned a value but never used.", "'showViewModal' is assigned a value but never used.", "'handleEdit' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaUpload' is defined but never used.", "'FaEye' is defined but never used.", "'loading' is assigned a value but never used.", "'Table' is defined but never used.", "'FaPlus' is defined but never used.", "'FaFileAlt' is defined but never used.", "'FaCreditCard' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["287"], "'selectedClaim' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPolicies'. Either include it or remove the dependency array.", ["288"], "'user' is assigned a value but never used.", "'categories' is assigned a value but never used.", "'FaClock' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["289"], "'FaPause' is defined but never used.", "'FaCheck' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMyTasks'. Either include it or remove the dependency array.", ["290"], {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [fetchPolicies]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [activeTab, fetchTasks]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [activeTab, fetchMyTasks]", {"range": "305", "text": "306"}, [1176, 1178], "[fetchDashboardData]", [1072, 1074], "[fetchPolicies]", [1333, 1344], "[activeTab, fetchTasks]", [1267, 1278], "[activeTab, fetchMyTasks]"]