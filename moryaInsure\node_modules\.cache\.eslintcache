[{"D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js": "1", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js": "2", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js": "3", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js": "4", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js": "5", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js": "6", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js": "7", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js": "8", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js": "9", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js": "10", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js": "11", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js": "12", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js": "13", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js": "14", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js": "15", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js": "16", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js": "17", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js": "18", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js": "19", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js": "20", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js": "21", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js": "22", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js": "23", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js": "24", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js": "25", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js": "26", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js": "27", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js": "28", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js": "29"}, {"size": 861, "mtime": 1750664705717, "results": "30", "hashOfConfig": "31"}, {"size": 375, "mtime": 1750664705720, "results": "32", "hashOfConfig": "31"}, {"size": 3875, "mtime": 1750665818382, "results": "33", "hashOfConfig": "31"}, {"size": 6661, "mtime": 1750664705705, "results": "34", "hashOfConfig": "31"}, {"size": 1181, "mtime": 1750665882854, "results": "35", "hashOfConfig": "31"}, {"size": 6428, "mtime": 1750664705709, "results": "36", "hashOfConfig": "31"}, {"size": 7511, "mtime": 1750664705715, "results": "37", "hashOfConfig": "31"}, {"size": 191, "mtime": 1750664705712, "results": "38", "hashOfConfig": "31"}, {"size": 1725, "mtime": 1750664705710, "results": "39", "hashOfConfig": "31"}, {"size": 3708, "mtime": 1750664705716, "results": "40", "hashOfConfig": "31"}, {"size": 6948, "mtime": 1750664705710, "results": "41", "hashOfConfig": "31"}, {"size": 8049, "mtime": 1750664705712, "results": "42", "hashOfConfig": "31"}, {"size": 3716, "mtime": 1750664705714, "results": "43", "hashOfConfig": "31"}, {"size": 7096, "mtime": 1750664705714, "results": "44", "hashOfConfig": "31"}, {"size": 940, "mtime": 1750665477412, "results": "45", "hashOfConfig": "31"}, {"size": 3901, "mtime": 1750665837249, "results": "46", "hashOfConfig": "31"}, {"size": 3336, "mtime": 1750665785327, "results": "47", "hashOfConfig": "31"}, {"size": 4046, "mtime": 1750665523833, "results": "48", "hashOfConfig": "31"}, {"size": 6559, "mtime": 1750665607130, "results": "49", "hashOfConfig": "31"}, {"size": 3401, "mtime": 1750665567901, "results": "50", "hashOfConfig": "31"}, {"size": 4103, "mtime": 1750665584344, "results": "51", "hashOfConfig": "31"}, {"size": 2774, "mtime": 1750666669606, "results": "52", "hashOfConfig": "31"}, {"size": 10917, "mtime": 1750666749400, "results": "53", "hashOfConfig": "31"}, {"size": 1552, "mtime": 1750665756872, "results": "54", "hashOfConfig": "31"}, {"size": 5796, "mtime": 1750666711482, "results": "55", "hashOfConfig": "31"}, {"size": 8302, "mtime": 1750666789072, "results": "56", "hashOfConfig": "31"}, {"size": 7944, "mtime": 1750665951382, "results": "57", "hashOfConfig": "31"}, {"size": 9559, "mtime": 1750665989269, "results": "58", "hashOfConfig": "31"}, {"size": 3840, "mtime": 1750666631821, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ue8fkt", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js", ["147", "148"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js", ["149", "150"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js", ["151", "152", "153", "154"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js", ["155"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js", [], [], {"ruleId": "156", "severity": 1, "message": "157", "line": 1, "column": 17, "nodeType": "158", "messageId": "159", "endLine": 1, "endColumn": 25}, {"ruleId": "156", "severity": 1, "message": "160", "line": 1, "column": 27, "nodeType": "158", "messageId": "159", "endLine": 1, "endColumn": 36}, {"ruleId": "156", "severity": 1, "message": "161", "line": 7, "column": 17, "nodeType": "158", "messageId": "159", "endLine": 7, "endColumn": 24}, {"ruleId": "156", "severity": 1, "message": "162", "line": 7, "column": 26, "nodeType": "158", "messageId": "159", "endLine": 7, "endColumn": 36}, {"ruleId": "163", "severity": 1, "message": "164", "line": 19, "column": 15, "nodeType": "165", "endLine": 19, "endColumn": 50}, {"ruleId": "163", "severity": 1, "message": "164", "line": 22, "column": 15, "nodeType": "165", "endLine": 22, "endColumn": 50}, {"ruleId": "163", "severity": 1, "message": "164", "line": 25, "column": 15, "nodeType": "165", "endLine": 25, "endColumn": 50}, {"ruleId": "163", "severity": 1, "message": "164", "line": 28, "column": 15, "nodeType": "165", "endLine": 28, "endColumn": 50}, {"ruleId": "156", "severity": 1, "message": "166", "line": 2, "column": 52, "nodeType": "158", "messageId": "159", "endLine": 2, "endColumn": 63}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'hasRole' is assigned a value but never used.", "'hasAnyRole' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'ProgressBar' is defined but never used."]