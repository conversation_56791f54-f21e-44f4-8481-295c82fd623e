[{"D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js": "1", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js": "2", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js": "3", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js": "4", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js": "5", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js": "6", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js": "7", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js": "8", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js": "9", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js": "10", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js": "11", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js": "12", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js": "13", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js": "14", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js": "15", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js": "16", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js": "17", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js": "18", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js": "19", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js": "20", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js": "21", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js": "22", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js": "23", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js": "24", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js": "25", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js": "26", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js": "27", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js": "28", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js": "29", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js": "30", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js": "31", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js": "32", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js": "33", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js": "34", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js": "35", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js": "36", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TaskManagement.js": "37", "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\MyTasks.js": "38"}, {"size": 861, "mtime": 1750664705717, "results": "39", "hashOfConfig": "40"}, {"size": 375, "mtime": 1750664705720, "results": "41", "hashOfConfig": "40"}, {"size": 5249, "mtime": 1751311658954, "results": "42", "hashOfConfig": "40"}, {"size": 6512, "mtime": 1751308012589, "results": "43", "hashOfConfig": "40"}, {"size": 1156, "mtime": 1750680083390, "results": "44", "hashOfConfig": "40"}, {"size": 6504, "mtime": 1751308316867, "results": "45", "hashOfConfig": "40"}, {"size": 7370, "mtime": 1751308117361, "results": "46", "hashOfConfig": "40"}, {"size": 14934, "mtime": 1751305766103, "results": "47", "hashOfConfig": "40"}, {"size": 1449, "mtime": 1751305387915, "results": "48", "hashOfConfig": "40"}, {"size": 15160, "mtime": 1751306101742, "results": "49", "hashOfConfig": "40"}, {"size": 7036, "mtime": 1751308353133, "results": "50", "hashOfConfig": "40"}, {"size": 7920, "mtime": 1751308292943, "results": "51", "hashOfConfig": "40"}, {"size": 3716, "mtime": 1750664705714, "results": "52", "hashOfConfig": "40"}, {"size": 7140, "mtime": 1751308435824, "results": "53", "hashOfConfig": "40"}, {"size": 1039, "mtime": 1750752443938, "results": "54", "hashOfConfig": "40"}, {"size": 3596, "mtime": 1751311677630, "results": "55", "hashOfConfig": "40"}, {"size": 3518, "mtime": 1751305611911, "results": "56", "hashOfConfig": "40"}, {"size": 4290, "mtime": 1750748793241, "results": "57", "hashOfConfig": "40"}, {"size": 6559, "mtime": 1750665607130, "results": "58", "hashOfConfig": "40"}, {"size": 3401, "mtime": 1750665567901, "results": "59", "hashOfConfig": "40"}, {"size": 4103, "mtime": 1750665584344, "results": "60", "hashOfConfig": "40"}, {"size": 5154, "mtime": 1751300690467, "results": "61", "hashOfConfig": "40"}, {"size": 10248, "mtime": 1751301038102, "results": "62", "hashOfConfig": "40"}, {"size": 1648, "mtime": 1750678907126, "results": "63", "hashOfConfig": "40"}, {"size": 6414, "mtime": 1751301017596, "results": "64", "hashOfConfig": "40"}, {"size": 29847, "mtime": 1751310631795, "results": "65", "hashOfConfig": "40"}, {"size": 9785, "mtime": 1750761152602, "results": "66", "hashOfConfig": "40"}, {"size": 14616, "mtime": 1751307505174, "results": "67", "hashOfConfig": "40"}, {"size": 7754, "mtime": 1751311485120, "results": "68", "hashOfConfig": "40"}, {"size": 8129, "mtime": 1750755972872, "results": "69", "hashOfConfig": "40"}, {"size": 6195, "mtime": 1750678949437, "results": "70", "hashOfConfig": "40"}, {"size": 9077, "mtime": 1750754399649, "results": "71", "hashOfConfig": "40"}, {"size": 1778, "mtime": 1750754365053, "results": "72", "hashOfConfig": "40"}, {"size": 9340, "mtime": 1751300946595, "results": "73", "hashOfConfig": "40"}, {"size": 12948, "mtime": 1751307182308, "results": "74", "hashOfConfig": "40"}, {"size": 17516, "mtime": 1751307243672, "results": "75", "hashOfConfig": "40"}, {"size": 21314, "mtime": 1751311556493, "results": "76", "hashOfConfig": "40"}, {"size": 24180, "mtime": 1751311632495, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yjkqrc", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\index.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\reportWebVitals.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\App.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Categories.js", ["192", "193"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Dashboard.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\InsurancePolicy.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TicketCategories.js", ["194", "195"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Staff.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ReportTool.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Users.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\PolicyHolder.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SubCategories.js", ["196", "197"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SystemSettings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\SupportTicket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\layout\\MainLayout.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Sidebar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Navbar.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Component\\Footer.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Contact.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\About.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Services.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\context\\AuthContext.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Register.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\ProtectedRoute.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Login.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\AdminDashboard.js", ["198"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\EmployeeDashboard.js", ["199"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\dashboards\\CustomerDashboard.js", ["200", "201", "202", "203", "204", "205"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\services\\api.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Profile.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Settings.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\components\\NotificationCenter.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\hooks\\useSocket.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\VerifyOTP.js", [], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\Claims.js", ["206", "207"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\ContactSupport.js", ["208", "209"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\TaskManagement.js", ["210", "211", "212"], [], "D:\\Wesync Softwares\\POC's\\Jun23\\moryaInsure\\src\\Pages\\MyTasks.js", ["213", "214", "215", "216"], [], {"ruleId": "217", "severity": 1, "message": "218", "line": 3, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 3, "endColumn": 16}, {"ruleId": "217", "severity": 1, "message": "221", "line": 6, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 18}, {"ruleId": "217", "severity": 1, "message": "218", "line": 3, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 3, "endColumn": 16}, {"ruleId": "217", "severity": 1, "message": "221", "line": 6, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 18}, {"ruleId": "217", "severity": 1, "message": "218", "line": 5, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 5, "endColumn": 16}, {"ruleId": "217", "severity": 1, "message": "221", "line": 8, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 8, "endColumn": 18}, {"ruleId": "217", "severity": 1, "message": "222", "line": 5, "column": 35, "nodeType": "219", "messageId": "220", "endLine": 5, "endColumn": 40}, {"ruleId": "217", "severity": 1, "message": "223", "line": 20, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 20, "endColumn": 17}, {"ruleId": "217", "severity": 1, "message": "224", "line": 2, "column": 65, "nodeType": "219", "messageId": "220", "endLine": 2, "endColumn": 70}, {"ruleId": "217", "severity": 1, "message": "225", "line": 6, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 16}, {"ruleId": "217", "severity": 1, "message": "222", "line": 6, "column": 18, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 23}, {"ruleId": "217", "severity": 1, "message": "226", "line": 6, "column": 25, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 34}, {"ruleId": "217", "severity": 1, "message": "227", "line": 6, "column": 36, "nodeType": "219", "messageId": "220", "endLine": 6, "endColumn": 48}, {"ruleId": "228", "severity": 1, "message": "229", "line": 37, "column": 6, "nodeType": "230", "endLine": 37, "endColumn": 8, "suggestions": "231"}, {"ruleId": "217", "severity": 1, "message": "232", "line": 13, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 13, "endColumn": 23}, {"ruleId": "228", "severity": 1, "message": "233", "line": 29, "column": 6, "nodeType": "230", "endLine": 29, "endColumn": 8, "suggestions": "234"}, {"ruleId": "217", "severity": 1, "message": "235", "line": 8, "column": 11, "nodeType": "219", "messageId": "220", "endLine": 8, "endColumn": 15}, {"ruleId": "217", "severity": 1, "message": "236", "line": 10, "column": 10, "nodeType": "219", "messageId": "220", "endLine": 10, "endColumn": 20}, {"ruleId": "217", "severity": 1, "message": "237", "line": 3, "column": 42, "nodeType": "219", "messageId": "220", "endLine": 3, "endColumn": 49}, {"ruleId": "217", "severity": 1, "message": "235", "line": 8, "column": 11, "nodeType": "219", "messageId": "220", "endLine": 8, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "238", "line": 35, "column": 6, "nodeType": "230", "endLine": 35, "endColumn": 17, "suggestions": "239"}, {"ruleId": "217", "severity": 1, "message": "240", "line": 3, "column": 25, "nodeType": "219", "messageId": "220", "endLine": 3, "endColumn": 32}, {"ruleId": "217", "severity": 1, "message": "241", "line": 3, "column": 34, "nodeType": "219", "messageId": "220", "endLine": 3, "endColumn": 41}, {"ruleId": "217", "severity": 1, "message": "235", "line": 8, "column": 11, "nodeType": "219", "messageId": "220", "endLine": 8, "endColumn": 15}, {"ruleId": "228", "severity": 1, "message": "242", "line": 32, "column": 6, "nodeType": "230", "endLine": 32, "endColumn": 17, "suggestions": "243"}, "no-unused-vars", "'FaUser' is defined but never used.", "Identifier", "unusedVar", "'username' is assigned a value but never used.", "'FaEye' is defined but never used.", "'loading' is assigned a value but never used.", "'Table' is defined but never used.", "'FaPlus' is defined but never used.", "'FaFileAlt' is defined but never used.", "'FaCreditCard' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["244"], "'selectedClaim' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPolicies'. Either include it or remove the dependency array.", ["245"], "'user' is assigned a value but never used.", "'categories' is assigned a value but never used.", "'FaClock' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["246"], "'FaPause' is defined but never used.", "'FaCheck' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMyTasks'. Either include it or remove the dependency array.", ["247"], {"desc": "248", "fix": "249"}, {"desc": "250", "fix": "251"}, {"desc": "252", "fix": "253"}, {"desc": "254", "fix": "255"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "256", "text": "257"}, "Update the dependencies array to be: [fetchPolicies]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [activeTab, fetchTasks]", {"range": "260", "text": "261"}, "Update the dependencies array to be: [activeTab, fetchMyTasks]", {"range": "262", "text": "263"}, [1176, 1178], "[fetchDashboardData]", [1072, 1074], "[fetchPolicies]", [1333, 1344], "[activeTab, fetchTasks]", [1267, 1278], "[activeTab, fetchMyTasks]"]