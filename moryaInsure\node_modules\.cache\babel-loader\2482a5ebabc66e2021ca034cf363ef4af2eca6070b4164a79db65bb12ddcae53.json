{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\TicketCategories.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Button, Table, Form, Modal } from 'react-bootstrap';\nimport { FaUser, FaEdit, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TicketCategories = () => {\n  _s();\n  const [username] = useState('Sushil'); // Replace with dynamic login username\n  const [ticketCategories, setTicketCategories] = useState([]);\n  const [search, setSearch] = useState('');\n\n  // New Category Modal\n  const [showNewModal, setShowNewModal] = useState(false);\n  const [newCategory, setNewCategory] = useState({\n    insuranceType: '',\n    ticketCategoryName: '',\n    status: 'active'\n  });\n\n  // Import Modal\n  const [showImportModal, setShowImportModal] = useState(false);\n  const [importFile, setImportFile] = useState(null);\n  useEffect(() => {\n    // Dummy data to simulate API\n    const dummy = [{\n      id: 1,\n      category: 'Health Insurance',\n      name: 'Travel Cancellation',\n      status: 'Active'\n    }, {\n      id: 2,\n      category: 'Car Insurance',\n      name: 'Vehicle Damage',\n      status: 'Active'\n    }];\n    setTicketCategories(dummy);\n  }, []);\n  const handleNewCategoryChange = e => {\n    setNewCategory({\n      ...newCategory,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSaveCategory = () => {\n    const newEntry = {\n      id: ticketCategories.length + 1,\n      category: newCategory.insuranceType,\n      name: newCategory.ticketCategoryName,\n      status: newCategory.status === 'active' ? 'Active' : 'Inactive'\n    };\n    setTicketCategories([...ticketCategories, newEntry]);\n    setShowNewModal(false);\n  };\n  const filteredData = ticketCategories.filter(cat => cat.name.toLowerCase().includes(search.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid p-3\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"fw-bold text-uppercase\",\n        children: \"Ticket Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: () => setShowNewModal(true),\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowImportModal(true),\n        children: \"Import\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 d-flex flex-wrap gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Copy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-secondary\",\n        size: \"sm\",\n        children: \"Print\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-3 w-100 w-md-50\",\n      children: /*#__PURE__*/_jsxDEV(Form.Control, {\n        type: \"text\",\n        placeholder: \"Search ticket categories...\",\n        value: search,\n        onChange: e => setSearch(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      bordered: true,\n      hover: true,\n      responsive: true,\n      className: \"shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"table-primary\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Ticket Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredData.length > 0 ? filteredData.map(cat => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: cat.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: cat.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `badge ${cat.status === 'Active' ? 'bg-success' : 'bg-secondary'}`,\n              children: cat.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2\",\n              children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 72\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              size: \"sm\",\n              children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 17\n          }, this)]\n        }, cat.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: /*#__PURE__*/_jsxDEV(\"td\", {\n            colSpan: \"4\",\n            className: \"text-center\",\n            children: \"No data available in database\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2\",\n      children: /*#__PURE__*/_jsxDEV(\"small\", {\n        className: \"text-muted\",\n        children: [\"Showing  \", filteredData.length, \" of \", ticketCategories.length, \" entries\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showNewModal,\n      onHide: () => setShowNewModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add Ticket Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Insurance Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            name: \"insuranceType\",\n            onChange: handleNewCategoryChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Health Insurance\",\n              children: \"Health Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Car Insurance\",\n              children: \"Car Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Property Insurance\",\n              children: \"Property Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Life Insurance\",\n              children: \"Life Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Ticket Category Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            name: \"ticketCategoryName\",\n            onChange: handleNewCategoryChange,\n            placeholder: \"Enter ticket category name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              inline: true,\n              label: \"Active\",\n              name: \"status\",\n              type: \"radio\",\n              value: \"active\",\n              checked: newCategory.status === 'active',\n              onChange: handleNewCategoryChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              inline: true,\n              label: \"Inactive\",\n              name: \"status\",\n              type: \"radio\",\n              value: \"inactive\",\n              checked: newCategory.status === 'inactive',\n              onChange: handleNewCategoryChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowNewModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: handleSaveCategory,\n          children: \"Save Changes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showImportModal,\n      onHide: () => setShowImportModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Import Ticket Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Choose File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"file\",\n            onChange: e => setImportFile(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowImportModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => {\n            console.log('Importing file:', importFile);\n            setShowImportModal(false);\n          },\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(TicketCategories, \"L/jcXtL92DoLXw6CKon2Q8LSyMU=\");\n_c = TicketCategories;\nexport default TicketCategories;\nvar _c;\n$RefreshReg$(_c, \"TicketCategories\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Table", "Form", "Modal", "FaUser", "FaEdit", "FaTrash", "jsxDEV", "_jsxDEV", "TicketCategories", "_s", "username", "ticketCategories", "setTicketCategories", "search", "setSearch", "showNewModal", "setShowNewModal", "newCategory", "setNewCategory", "insuranceType", "ticketCategoryName", "status", "showImportModal", "setShowImportModal", "importFile", "setImportFile", "dummy", "id", "category", "name", "handleNewCategoryChange", "e", "target", "value", "handleSaveCategory", "newEntry", "length", "filteredData", "filter", "cat", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "Control", "type", "placeholder", "onChange", "bordered", "hover", "responsive", "map", "colSpan", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "Group", "Label", "Select", "Check", "inline", "label", "checked", "Footer", "files", "console", "log", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/TicketCategories.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Button, Table, Form, Modal } from 'react-bootstrap';\r\nimport { FaUser, FaEdit, FaTrash } from 'react-icons/fa';\r\n\r\nconst TicketCategories = () => {\r\n  const [username] = useState('Sushil'); // Replace with dynamic login username\r\n  const [ticketCategories, setTicketCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n\r\n  // New Category Modal\r\n  const [showNewModal, setShowNewModal] = useState(false);\r\n  const [newCategory, setNewCategory] = useState({\r\n    insuranceType: '',\r\n    ticketCategoryName: '',\r\n    status: 'active',\r\n  });\r\n\r\n  // Import Modal\r\n  const [showImportModal, setShowImportModal] = useState(false);\r\n  const [importFile, setImportFile] = useState(null);\r\n\r\n  useEffect(() => {\r\n    // Dummy data to simulate API\r\n    const dummy = [\r\n      { id: 1, category: 'Health Insurance', name: 'Travel Cancellation', status: 'Active' },\r\n      { id: 2, category: 'Car Insurance', name: 'Vehicle Damage', status: 'Active' },\r\n    ];\r\n    setTicketCategories(dummy);\r\n  }, []);\r\n\r\n  const handleNewCategoryChange = (e) => {\r\n    setNewCategory({ ...newCategory, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const handleSaveCategory = () => {\r\n    const newEntry = {\r\n      id: ticketCategories.length + 1,\r\n      category: newCategory.insuranceType,\r\n      name: newCategory.ticketCategoryName,\r\n      status: newCategory.status === 'active' ? 'Active' : 'Inactive',\r\n    };\r\n    setTicketCategories([...ticketCategories, newEntry]);\r\n    setShowNewModal(false);\r\n  };\r\n\r\n  const filteredData = ticketCategories.filter((cat) =>\r\n    cat.name.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"container-fluid p-3\">\r\n      {/* Header */}\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h3 className=\"fw-bold text-uppercase\">Ticket Categories</h3>\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"mb-3 d-flex gap-2\">\r\n        <Button variant=\"primary\" onClick={() => setShowNewModal(true)}>+ New</Button>\r\n        <Button variant=\"secondary\" onClick={() => setShowImportModal(true)}>Import</Button>\r\n      </div>\r\n\r\n      {/* Export Buttons */}\r\n      <div className=\"mb-3 d-flex flex-wrap gap-2\">\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Copy</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">CSV</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Excel</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">PDF</Button>\r\n        <Button variant=\"outline-secondary\" size=\"sm\">Print</Button>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"mb-3 w-100 w-md-50\">\r\n        <Form.Control\r\n          type=\"text\"\r\n          placeholder=\"Search ticket categories...\"\r\n          value={search}\r\n          onChange={(e) => setSearch(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <Table bordered hover responsive className=\"shadow-sm\">\r\n        <thead className=\"table-primary\">\r\n          <tr>\r\n            <th>Category Name</th>\r\n            <th>Ticket Category Name</th>\r\n            <th>Status</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {filteredData.length > 0 ? (\r\n            filteredData.map((cat) => (\r\n              <tr key={cat.id}>\r\n                <td>{cat.category}</td>\r\n                <td>{cat.name}</td>\r\n                <td>\r\n                  <span className={`badge ${cat.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>\r\n                    {cat.status}\r\n                  </span>\r\n                </td>\r\n                <td>\r\n                  <Button variant=\"primary\" size=\"sm\" className=\"me-2\"><FaEdit /></Button>\r\n                  <Button variant=\"danger\" size=\"sm\"><FaTrash /></Button>\r\n                </td>\r\n              </tr>\r\n            ))\r\n          ) : (\r\n            <tr>\r\n              <td colSpan=\"4\" className=\"text-center\">No data available in database</td>\r\n            </tr>\r\n          )}\r\n        </tbody>\r\n      </Table>\r\n\r\n      {/* Footer */}\r\n      <div className=\"mt-2\">\r\n        <small className=\"text-muted\">Showing  {filteredData.length} of {ticketCategories.length} entries</small>\r\n      </div>\r\n\r\n      {/* Add New Modal */}\r\n      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add Ticket Category</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form.Group className=\"mb-3\">\r\n            <Form.Label>Insurance Category</Form.Label>\r\n            <Form.Select name=\"insuranceType\" onChange={handleNewCategoryChange}>\r\n              <option value=\"\">Select</option>\r\n              <option value=\"Health Insurance\">Health Insurance</option>\r\n              <option value=\"Car Insurance\">Car Insurance</option>\r\n              <option value=\"Property Insurance\">Property Insurance</option>\r\n              <option value=\"Life Insurance\">Life Insurance</option>\r\n            </Form.Select>\r\n          </Form.Group>\r\n          <Form.Group className=\"mb-3\">\r\n            <Form.Label>Ticket Category Name</Form.Label>\r\n            <Form.Control\r\n              type=\"text\"\r\n              name=\"ticketCategoryName\"\r\n              onChange={handleNewCategoryChange}\r\n              placeholder=\"Enter ticket category name\"\r\n            />\r\n          </Form.Group>\r\n          <Form.Group>\r\n            <Form.Label>Status</Form.Label>\r\n            <div>\r\n              <Form.Check\r\n                inline\r\n                label=\"Active\"\r\n                name=\"status\"\r\n                type=\"radio\"\r\n                value=\"active\"\r\n                checked={newCategory.status === 'active'}\r\n                onChange={handleNewCategoryChange}\r\n              />\r\n              <Form.Check\r\n                inline\r\n                label=\"Inactive\"\r\n                name=\"status\"\r\n                type=\"radio\"\r\n                value=\"inactive\"\r\n                checked={newCategory.status === 'inactive'}\r\n                onChange={handleNewCategoryChange}\r\n              />\r\n            </div>\r\n          </Form.Group>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowNewModal(false)}>Close</Button>\r\n          <Button variant=\"success\" onClick={handleSaveCategory}>Save Changes</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Import Modal */}\r\n      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Import Ticket Categories</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <Form.Group>\r\n            <Form.Label>Choose File</Form.Label>\r\n            <Form.Control type=\"file\" onChange={(e) => setImportFile(e.target.files[0])} />\r\n          </Form.Group>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowImportModal(false)}>Close</Button>\r\n          <Button variant=\"primary\" onClick={() => {\r\n            console.log('Importing file:', importFile);\r\n            setShowImportModal(false);\r\n          }}>Upload</Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TicketCategories;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AAC5D,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;;EAExC;EACA,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC;IAC7CsB,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd;IACA,MAAM4B,KAAK,GAAG,CACZ;MAAEC,EAAE,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEC,IAAI,EAAE,qBAAqB;MAAER,MAAM,EAAE;IAAS,CAAC,EACtF;MAAEM,EAAE,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEC,IAAI,EAAE,gBAAgB;MAAER,MAAM,EAAE;IAAS,CAAC,CAC/E;IACDT,mBAAmB,CAACc,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,uBAAuB,GAAIC,CAAC,IAAK;IACrCb,cAAc,CAAC;MAAE,GAAGD,WAAW;MAAE,CAACc,CAAC,CAACC,MAAM,CAACH,IAAI,GAAGE,CAAC,CAACC,MAAM,CAACC;IAAM,CAAC,CAAC;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,QAAQ,GAAG;MACfR,EAAE,EAAEhB,gBAAgB,CAACyB,MAAM,GAAG,CAAC;MAC/BR,QAAQ,EAAEX,WAAW,CAACE,aAAa;MACnCU,IAAI,EAAEZ,WAAW,CAACG,kBAAkB;MACpCC,MAAM,EAAEJ,WAAW,CAACI,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG;IACvD,CAAC;IACDT,mBAAmB,CAAC,CAAC,GAAGD,gBAAgB,EAAEwB,QAAQ,CAAC,CAAC;IACpDnB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMqB,YAAY,GAAG1B,gBAAgB,CAAC2B,MAAM,CAAEC,GAAG,IAC/CA,GAAG,CAACV,IAAI,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,MAAM,CAAC2B,WAAW,CAAC,CAAC,CACtD,CAAC;EAED,oBACEjC,OAAA;IAAKmC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCpC,OAAA;MAAKmC,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrEpC,OAAA;QAAImC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGNxC,OAAA;MAAKmC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAAC,IAAI,CAAE;QAAA2B,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9ExC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,WAAW;QAACC,OAAO,EAAEA,CAAA,KAAM1B,kBAAkB,CAAC,IAAI,CAAE;QAAAoB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAGNxC,OAAA;MAAKmC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CpC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC3DxC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DxC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DxC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1DxC,OAAA,CAACR,MAAM;QAACiD,OAAO,EAAC,mBAAmB;QAACE,IAAI,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAGNxC,OAAA;MAAKmC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCpC,OAAA,CAACN,IAAI,CAACkD,OAAO;QACXC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,6BAA6B;QACzCpB,KAAK,EAAEpB,MAAO;QACdyC,QAAQ,EAAGvB,CAAC,IAAKjB,SAAS,CAACiB,CAAC,CAACC,MAAM,CAACC,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxC,OAAA,CAACP,KAAK;MAACuD,QAAQ;MAACC,KAAK;MAACC,UAAU;MAACf,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACpDpC,OAAA;QAAOmC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC9BpC,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAAoC,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBxC,OAAA;YAAAoC,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BxC,OAAA;YAAAoC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfxC,OAAA;YAAAoC,QAAA,EAAI;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRxC,OAAA;QAAAoC,QAAA,EACGN,YAAY,CAACD,MAAM,GAAG,CAAC,GACtBC,YAAY,CAACqB,GAAG,CAAEnB,GAAG,iBACnBhC,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAAoC,QAAA,EAAKJ,GAAG,CAACX;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBxC,OAAA;YAAAoC,QAAA,EAAKJ,GAAG,CAACV;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnBxC,OAAA;YAAAoC,QAAA,eACEpC,OAAA;cAAMmC,SAAS,EAAE,SAASH,GAAG,CAAClB,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAG,cAAc,EAAG;cAAAsB,QAAA,EACjFJ,GAAG,CAAClB;YAAM;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLxC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA,CAACR,MAAM;cAACiD,OAAO,EAAC,SAAS;cAACE,IAAI,EAAC,IAAI;cAACR,SAAS,EAAC,MAAM;cAAAC,QAAA,eAACpC,OAAA,CAACH,MAAM;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxExC,OAAA,CAACR,MAAM;cAACiD,OAAO,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAAAP,QAAA,eAACpC,OAAA,CAACF,OAAO;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAXER,GAAG,CAACZ,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACL,CAAC,gBAEFxC,OAAA;UAAAoC,QAAA,eACEpC,OAAA;YAAIoD,OAAO,EAAC,GAAG;YAACjB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRxC,OAAA;MAAKmC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBpC,OAAA;QAAOmC,SAAS,EAAC,YAAY;QAAAC,QAAA,GAAC,WAAS,EAACN,YAAY,CAACD,MAAM,EAAC,MAAI,EAACzB,gBAAgB,CAACyB,MAAM,EAAC,UAAQ;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CAAC,eAGNxC,OAAA,CAACL,KAAK;MAAC0D,IAAI,EAAE7C,YAAa;MAAC8C,MAAM,EAAEA,CAAA,KAAM7C,eAAe,CAAC,KAAK,CAAE;MAAC8C,QAAQ;MAAAnB,QAAA,gBACvEpC,OAAA,CAACL,KAAK,CAAC6D,MAAM;QAACC,WAAW;QAAArB,QAAA,eACvBpC,OAAA,CAACL,KAAK,CAAC+D,KAAK;UAAAtB,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACfxC,OAAA,CAACL,KAAK,CAACgE,IAAI;QAAAvB,QAAA,gBACTpC,OAAA,CAACN,IAAI,CAACkE,KAAK;UAACzB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BpC,OAAA,CAACN,IAAI,CAACmE,KAAK;YAAAzB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3CxC,OAAA,CAACN,IAAI,CAACoE,MAAM;YAACxC,IAAI,EAAC,eAAe;YAACyB,QAAQ,EAAExB,uBAAwB;YAAAa,QAAA,gBAClEpC,OAAA;cAAQ0B,KAAK,EAAC,EAAE;cAAAU,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCxC,OAAA;cAAQ0B,KAAK,EAAC,kBAAkB;cAAAU,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1DxC,OAAA;cAAQ0B,KAAK,EAAC,eAAe;cAAAU,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpDxC,OAAA;cAAQ0B,KAAK,EAAC,oBAAoB;cAAAU,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9DxC,OAAA;cAAQ0B,KAAK,EAAC,gBAAgB;cAAAU,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbxC,OAAA,CAACN,IAAI,CAACkE,KAAK;UAACzB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BpC,OAAA,CAACN,IAAI,CAACmE,KAAK;YAAAzB,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7CxC,OAAA,CAACN,IAAI,CAACkD,OAAO;YACXC,IAAI,EAAC,MAAM;YACXvB,IAAI,EAAC,oBAAoB;YACzByB,QAAQ,EAAExB,uBAAwB;YAClCuB,WAAW,EAAC;UAA4B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACbxC,OAAA,CAACN,IAAI,CAACkE,KAAK;UAAAxB,QAAA,gBACTpC,OAAA,CAACN,IAAI,CAACmE,KAAK;YAAAzB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/BxC,OAAA;YAAAoC,QAAA,gBACEpC,OAAA,CAACN,IAAI,CAACqE,KAAK;cACTC,MAAM;cACNC,KAAK,EAAC,QAAQ;cACd3C,IAAI,EAAC,QAAQ;cACbuB,IAAI,EAAC,OAAO;cACZnB,KAAK,EAAC,QAAQ;cACdwC,OAAO,EAAExD,WAAW,CAACI,MAAM,KAAK,QAAS;cACzCiC,QAAQ,EAAExB;YAAwB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACFxC,OAAA,CAACN,IAAI,CAACqE,KAAK;cACTC,MAAM;cACNC,KAAK,EAAC,UAAU;cAChB3C,IAAI,EAAC,QAAQ;cACbuB,IAAI,EAAC,OAAO;cACZnB,KAAK,EAAC,UAAU;cAChBwC,OAAO,EAAExD,WAAW,CAACI,MAAM,KAAK,UAAW;cAC3CiC,QAAQ,EAAExB;YAAwB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACbxC,OAAA,CAACL,KAAK,CAACwE,MAAM;QAAA/B,QAAA,gBACXpC,OAAA,CAACR,MAAM;UAACiD,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAAC,KAAK,CAAE;UAAA2B,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjFxC,OAAA,CAACR,MAAM;UAACiD,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEf,kBAAmB;UAAAS,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRxC,OAAA,CAACL,KAAK;MAAC0D,IAAI,EAAEtC,eAAgB;MAACuC,MAAM,EAAEA,CAAA,KAAMtC,kBAAkB,CAAC,KAAK,CAAE;MAACuC,QAAQ;MAAAnB,QAAA,gBAC7EpC,OAAA,CAACL,KAAK,CAAC6D,MAAM;QAACC,WAAW;QAAArB,QAAA,eACvBpC,OAAA,CAACL,KAAK,CAAC+D,KAAK;UAAAtB,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACfxC,OAAA,CAACL,KAAK,CAACgE,IAAI;QAAAvB,QAAA,eACTpC,OAAA,CAACN,IAAI,CAACkE,KAAK;UAAAxB,QAAA,gBACTpC,OAAA,CAACN,IAAI,CAACmE,KAAK;YAAAzB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpCxC,OAAA,CAACN,IAAI,CAACkD,OAAO;YAACC,IAAI,EAAC,MAAM;YAACE,QAAQ,EAAGvB,CAAC,IAAKN,aAAa,CAACM,CAAC,CAACC,MAAM,CAAC2C,KAAK,CAAC,CAAC,CAAC;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACbxC,OAAA,CAACL,KAAK,CAACwE,MAAM;QAAA/B,QAAA,gBACXpC,OAAA,CAACR,MAAM;UAACiD,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEA,CAAA,KAAM1B,kBAAkB,CAAC,KAAK,CAAE;UAAAoB,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpFxC,OAAA,CAACR,MAAM;UAACiD,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEA,CAAA,KAAM;YACvC2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAErD,UAAU,CAAC;YAC1CD,kBAAkB,CAAC,KAAK,CAAC;UAC3B,CAAE;UAAAoB,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtC,EAAA,CAjMID,gBAAgB;AAAAsE,EAAA,GAAhBtE,gBAAgB;AAmMtB,eAAeA,gBAAgB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}