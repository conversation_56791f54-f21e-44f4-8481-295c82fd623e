const mongoose = require('mongoose');

const claimSchema = new mongoose.Schema({
  claimNumber: {
    type: String,
    unique: true,
    required: [true, 'Claim number is required']
  },
  policy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Policy',
    required: [true, 'Policy reference is required']
  },
  claimant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Claimant is required']
  },
  type: {
    type: String,
    enum: ['accident', 'theft', 'damage', 'medical', 'death', 'disability', 'other'],
    required: [true, 'Claim type is required']
  },
  status: {
    type: String,
    enum: ['submitted', 'under-review', 'investigating', 'approved', 'rejected', 'settled', 'closed'],
    default: 'submitted'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  incidentDate: {
    type: Date,
    required: [true, 'Incident date is required']
  },
  reportedDate: {
    type: Date,
    default: Date.now
  },
  description: {
    type: String,
    required: [true, 'Claim description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  claimedAmount: {
    type: Number,
    required: [true, 'Claimed amount is required'],
    min: [0, 'Claimed amount must be positive']
  },
  approvedAmount: {
    type: Number,
    default: 0,
    min: [0, 'Approved amount must be positive']
  },
  deductibleAmount: {
    type: Number,
    default: 0,
    min: [0, 'Deductible amount must be positive']
  },
  location: {
    address: String,
    city: String,
    state: String,
    zipCode: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  witnesses: [{
    name: String,
    phone: String,
    email: String,
    statement: String
  }],
  documents: [{
    name: String,
    type: {
      type: String,
      enum: ['photo', 'report', 'receipt', 'medical', 'police', 'other']
    },
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  assignedAdjuster: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  investigation: {
    startDate: Date,
    endDate: Date,
    findings: String,
    investigator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['pending', 'in-progress', 'completed', 'suspended'],
      default: 'pending'
    }
  },
  timeline: [{
    action: String,
    description: String,
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  communications: [{
    type: {
      type: String,
      enum: ['email', 'phone', 'letter', 'meeting', 'system']
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    subject: String,
    content: String,
    from: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    to: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    timestamp: {
      type: Date,
      default: Date.now
    },
    attachments: [String]
  }],
  settlement: {
    amount: Number,
    method: {
      type: String,
      enum: ['check', 'bank-transfer', 'card', 'other']
    },
    date: Date,
    reference: String,
    notes: String
  },
  fraudIndicators: [{
    type: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high']
    },
    description: String,
    detectedAt: {
      type: Date,
      default: Date.now
    }
  }],
  sla: {
    responseTime: {
      target: Number, // in hours
      actual: Number
    },
    resolutionTime: {
      target: Number, // in days
      actual: Number
    }
  },
  tags: [String],
  notes: [{
    content: String,
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: true
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for claim age
claimSchema.virtual('claimAge').get(function() {
  const now = new Date();
  const reported = new Date(this.reportedDate);
  return Math.floor((now - reported) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for processing time
claimSchema.virtual('processingTime').get(function() {
  if (!this.settlement || !this.settlement.date) return null;
  
  const reported = new Date(this.reportedDate);
  const settled = new Date(this.settlement.date);
  return Math.floor((settled - reported) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for net settlement amount
claimSchema.virtual('netSettlementAmount').get(function() {
  if (!this.settlement || !this.settlement.amount) return 0;
  return this.settlement.amount - (this.deductibleAmount || 0);
});

// Index for better query performance (claimNumber already has unique index)
claimSchema.index({ policy: 1 });
claimSchema.index({ claimant: 1 });
claimSchema.index({ status: 1 });
claimSchema.index({ assignedAdjuster: 1 });
claimSchema.index({ incidentDate: 1 });
claimSchema.index({ reportedDate: -1 });

// Pre-save middleware to generate claim number
claimSchema.pre('save', async function(next) {
  if (!this.claimNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments();
    this.claimNumber = `CLM-${year}-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

// Static method to find claims by status
claimSchema.statics.findByStatus = function(status) {
  return this.find({ status })
    .populate('policy claimant assignedAdjuster')
    .sort({ reportedDate: -1 });
};

// Static method to find overdue claims
claimSchema.statics.findOverdue = function(days = 30) {
  const overdueDate = new Date();
  overdueDate.setDate(overdueDate.getDate() - days);
  
  return this.find({
    status: { $in: ['submitted', 'under-review', 'investigating'] },
    reportedDate: { $lt: overdueDate }
  }).populate('policy claimant assignedAdjuster');
};

// Method to add timeline entry
claimSchema.methods.addTimelineEntry = function(action, description, performedBy, metadata = {}) {
  this.timeline.push({
    action,
    description,
    performedBy,
    metadata
  });
  return this.save();
};

// Method to assign adjuster
claimSchema.methods.assignAdjuster = function(adjusterId) {
  this.assignedAdjuster = adjusterId;
  this.addTimelineEntry('assigned', `Claim assigned to adjuster`, adjusterId);
  return this.save();
};

// Method to update status
claimSchema.methods.updateStatus = function(newStatus, performedBy, notes) {
  const oldStatus = this.status;
  this.status = newStatus;
  
  this.addTimelineEntry(
    'status_change', 
    `Status changed from ${oldStatus} to ${newStatus}${notes ? ': ' + notes : ''}`, 
    performedBy
  );
  
  return this.save();
};

module.exports = mongoose.model('Claim', claimSchema);
