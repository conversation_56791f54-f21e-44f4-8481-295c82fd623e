# Testing Guide - Morya Insurance Management System

This document provides comprehensive testing instructions to validate all functionality and RBAC permissions.

## Pre-Testing Setup

1. **Start the Application**
   ```bash
   # Option 1: Use startup scripts
   ./start.sh  # Linux/Mac
   start.bat   # Windows
   
   # Option 2: Manual startup
   cd server && npm run dev
   cd moryaInsure && npm start
   ```

2. **Verify Services**
   - Backend API: http://localhost:5000/api/health
   - Frontend App: http://localhost:3000
   - MongoDB: Ensure running on port 27017

## Authentication Testing

### 1. User Registration
- **Test**: Register new customer account
- **Steps**: 
  1. Navigate to `/register`
  2. Fill form with valid data
  3. Select "Customer" role
  4. Submit form
- **Expected**: Success message, redirect to login

### 2. User Login
- **Test**: Login with different roles
- **Demo Accounts**:
  - Admin: `<EMAIL>` / `admin123`
  - Employee: `<EMAIL>` / `emp123`
  - Customer: `<EMAIL>` / `cust123`
- **Expected**: Successful login, role-appropriate dashboard

### 3. Session Management
- **Test**: Token persistence and logout
- **Steps**:
  1. Login and refresh page
  2. Close browser and reopen
  3. Click logout
- **Expected**: Session maintained until logout

## RBAC Testing

### Admin Role Testing

#### Dashboard Access
- **Test**: Admin dashboard visibility
- **Expected**: 
  - Full system statistics
  - User management cards
  - System health monitoring
  - All navigation menu items visible

#### User Management
- **Test**: CRUD operations on users
- **Steps**:
  1. Navigate to Users page
  2. Create new user
  3. Edit user details
  4. Toggle user status
  5. Delete user (not self)
- **Expected**: All operations successful

#### System Settings
- **Test**: Access to system configuration
- **Expected**: Full access to all settings

### Employee Role Testing

#### Dashboard Access
- **Test**: Employee dashboard visibility
- **Expected**:
  - Work-focused statistics
  - Assigned tasks and policies
  - Performance metrics
  - Limited navigation menu

#### Policy Management
- **Test**: Policy operations
- **Steps**:
  1. View assigned policies
  2. Update policy status
  3. Add policy notes
  4. Approve/reject policies
- **Expected**: Can manage assigned policies only

#### Ticket Management
- **Test**: Support ticket handling
- **Steps**:
  1. View assigned tickets
  2. Add comments
  3. Change ticket status
  4. Escalate tickets
- **Expected**: Full ticket management capabilities

#### Restricted Access
- **Test**: Verify access restrictions
- **Expected**: 
  - Cannot access user management
  - Cannot access system settings
  - Cannot view other employees' data

### Customer Role Testing

#### Dashboard Access
- **Test**: Customer dashboard visibility
- **Expected**:
  - Personal policy overview
  - Premium due information
  - Loyalty points and membership
  - Limited navigation menu

#### Policy Viewing
- **Test**: Personal policy access
- **Expected**: Can view only own policies

#### Ticket Creation
- **Test**: Support ticket submission
- **Steps**:
  1. Create new support ticket
  2. Add attachments (if implemented)
  3. View ticket status
- **Expected**: Can create and view own tickets

#### Restricted Access
- **Test**: Verify access restrictions
- **Expected**:
  - Cannot access other users' data
  - Cannot access admin functions
  - Cannot view system statistics

## Functional Testing

### Navigation Testing
- **Test**: Sidebar collapse/expand
- **Steps**:
  1. Click hamburger menu
  2. Verify sidebar collapses
  3. Hover over collapsed items
- **Expected**: Smooth animation, tooltips appear

### Responsive Design
- **Test**: Mobile compatibility
- **Steps**:
  1. Resize browser window
  2. Test on mobile device
  3. Check all breakpoints
- **Expected**: Responsive layout, no horizontal scroll

### Form Validation
- **Test**: Input validation
- **Steps**:
  1. Submit forms with invalid data
  2. Test required fields
  3. Test email format validation
  4. Test password strength
- **Expected**: Appropriate error messages

### Error Handling
- **Test**: API error responses
- **Steps**:
  1. Disconnect from internet
  2. Stop backend server
  3. Submit forms
- **Expected**: User-friendly error messages

## Security Testing

### Authentication Security
- **Test**: Token validation
- **Steps**:
  1. Manually modify token in localStorage
  2. Try accessing protected routes
  3. Test expired tokens
- **Expected**: Redirect to login page

### Authorization Security
- **Test**: Role-based restrictions
- **Steps**:
  1. Login as customer
  2. Manually navigate to admin URLs
  3. Try API calls with wrong role
- **Expected**: Access denied messages

### Input Security
- **Test**: XSS and injection prevention
- **Steps**:
  1. Enter script tags in forms
  2. Test SQL injection patterns
  3. Test malformed data
- **Expected**: Input sanitized, no execution

## Performance Testing

### Load Testing
- **Test**: Multiple concurrent users
- **Steps**:
  1. Open multiple browser tabs
  2. Login with different accounts
  3. Perform simultaneous operations
- **Expected**: Stable performance

### API Response Times
- **Test**: Backend performance
- **Expected**: 
  - Login: < 2 seconds
  - Dashboard load: < 3 seconds
  - Data operations: < 1 second

## Integration Testing

### Frontend-Backend Integration
- **Test**: API communication
- **Steps**:
  1. Create new policy
  2. Update user profile
  3. Submit support ticket
- **Expected**: Data persists correctly

### Database Integration
- **Test**: Data persistence
- **Steps**:
  1. Create data in application
  2. Restart server
  3. Verify data still exists
- **Expected**: All data preserved

## Browser Compatibility

### Supported Browsers
- **Chrome**: Latest version
- **Firefox**: Latest version
- **Safari**: Latest version
- **Edge**: Latest version

### Testing Steps
1. Test core functionality in each browser
2. Verify responsive design
3. Check for console errors

## Common Issues & Solutions

### Backend Connection Issues
- **Issue**: Cannot connect to API
- **Solution**: 
  1. Check if backend server is running
  2. Verify MongoDB connection
  3. Check CORS configuration

### Authentication Issues
- **Issue**: Login fails
- **Solution**:
  1. Verify demo credentials
  2. Check JWT secret configuration
  3. Clear browser cache

### Database Issues
- **Issue**: Data not persisting
- **Solution**:
  1. Verify MongoDB is running
  2. Check database connection string
  3. Verify user permissions

## Test Checklist

### Authentication ✓
- [ ] User registration works
- [ ] Login with all roles works
- [ ] Logout clears session
- [ ] Token persistence works
- [ ] Password validation works

### RBAC ✓
- [ ] Admin sees all features
- [ ] Employee has limited access
- [ ] Customer has restricted access
- [ ] Unauthorized access blocked
- [ ] Role-based navigation works

### UI/UX ✓
- [ ] Sidebar collapse/expand works
- [ ] Responsive design works
- [ ] Forms validate properly
- [ ] Error messages display
- [ ] Loading states work

### Security ✓
- [ ] Invalid tokens rejected
- [ ] Role restrictions enforced
- [ ] Input sanitization works
- [ ] HTTPS in production
- [ ] Rate limiting works

### Performance ✓
- [ ] Fast page loads
- [ ] Smooth animations
- [ ] No memory leaks
- [ ] Efficient API calls
- [ ] Optimized bundle size

## Reporting Issues

When reporting issues, include:
1. Steps to reproduce
2. Expected vs actual behavior
3. Browser and version
4. Console error messages
5. Screenshots if applicable

---

**Note**: This testing guide should be executed before any production deployment to ensure system reliability and security.
