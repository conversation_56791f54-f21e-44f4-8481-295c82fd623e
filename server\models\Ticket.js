const mongoose = require('mongoose');

const ticketSchema = new mongoose.Schema({
  ticketNumber: {
    type: String,
    unique: true,
    required: [true, 'Ticket number is required']
  },
  title: {
    type: String,
    required: [true, 'Ticket title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Ticket description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Ticket category is required']
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['open', 'in-progress', 'pending-customer', 'resolved', 'closed', 'cancelled'],
    default: 'open'
  },
  type: {
    type: String,
    enum: ['general', 'technical', 'billing', 'claim', 'policy', 'complaint'],
    required: [true, 'Ticket type is required']
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Submitter is required']
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  relatedPolicy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Policy'
  },
  tags: [String],
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  comments: [{
    content: {
      type: String,
      required: true,
      maxlength: [1000, 'Comment cannot exceed 1000 characters']
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    isInternal: {
      type: Boolean,
      default: false
    },
    attachments: [{
      filename: String,
      url: String
    }],
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  resolution: {
    summary: String,
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: Date,
    resolutionTime: Number // in minutes
  },
  satisfaction: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String,
    submittedAt: Date
  },
  sla: {
    responseTime: {
      target: Number, // in minutes
      actual: Number
    },
    resolutionTime: {
      target: Number, // in minutes
      actual: Number
    }
  },
  escalation: {
    level: {
      type: Number,
      default: 0
    },
    escalatedAt: Date,
    escalatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for ticket age
ticketSchema.virtual('ticketAge').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  return Math.floor((now - created) / (1000 * 60 * 60 * 24)); // days
});

// Virtual for response time
ticketSchema.virtual('responseTime').get(function() {
  if (this.comments.length === 0) return null;
  
  const firstResponse = this.comments
    .filter(comment => comment.author.toString() !== this.submittedBy.toString())
    .sort((a, b) => a.createdAt - b.createdAt)[0];
  
  if (!firstResponse) return null;
  
  const created = new Date(this.createdAt);
  const responded = new Date(firstResponse.createdAt);
  return Math.floor((responded - created) / (1000 * 60)); // minutes
});

// Virtual for total resolution time
ticketSchema.virtual('totalResolutionTime').get(function() {
  if (!this.resolution.resolvedAt) return null;
  
  const created = new Date(this.createdAt);
  const resolved = new Date(this.resolution.resolvedAt);
  return Math.floor((resolved - created) / (1000 * 60)); // minutes
});

// Index for better query performance (ticketNumber already has unique index)
ticketSchema.index({ submittedBy: 1 });
ticketSchema.index({ assignedTo: 1 });
ticketSchema.index({ status: 1 });
ticketSchema.index({ priority: 1 });
ticketSchema.index({ category: 1 });
ticketSchema.index({ createdAt: -1 });

// Pre-save middleware to generate ticket number
ticketSchema.pre('save', async function(next) {
  if (!this.ticketNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments();
    this.ticketNumber = `TK-${year}-${String(count + 1).padStart(6, '0')}`;
  }
  
  // Set resolution time when status changes to resolved
  if (this.isModified('status') && this.status === 'resolved' && !this.resolution.resolvedAt) {
    this.resolution.resolvedAt = new Date();
    this.resolution.resolutionTime = this.totalResolutionTime;
  }
  
  next();
});

// Static method to find tickets by status
ticketSchema.statics.findByStatus = function(status) {
  return this.find({ status })
    .populate('submittedBy assignedTo category')
    .sort({ createdAt: -1 });
};

// Static method to find overdue tickets
ticketSchema.statics.findOverdue = function() {
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.find({
    status: { $in: ['open', 'in-progress'] },
    createdAt: { $lt: oneDayAgo }
  }).populate('submittedBy assignedTo');
};

// Static method to find tickets by agent
ticketSchema.statics.findByAgent = function(agentId) {
  return this.find({ assignedTo: agentId })
    .populate('submittedBy category')
    .sort({ priority: -1, createdAt: -1 });
};

// Method to add comment
ticketSchema.methods.addComment = function(content, authorId, isInternal = false) {
  this.comments.push({
    content,
    author: authorId,
    isInternal
  });
  return this.save();
};

// Method to assign ticket
ticketSchema.methods.assignTo = function(agentId) {
  this.assignedTo = agentId;
  this.status = 'in-progress';
  return this.save();
};

// Method to escalate ticket
ticketSchema.methods.escalate = function(reason, escalatedBy) {
  this.escalation.level += 1;
  this.escalation.escalatedAt = new Date();
  this.escalation.escalatedBy = escalatedBy;
  this.escalation.reason = reason;
  
  // Increase priority on escalation
  if (this.priority === 'low') this.priority = 'medium';
  else if (this.priority === 'medium') this.priority = 'high';
  else if (this.priority === 'high') this.priority = 'urgent';
  
  return this.save();
};

module.exports = mongoose.model('Ticket', ticketSchema);
