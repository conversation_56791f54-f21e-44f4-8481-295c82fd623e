import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Row, <PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, Tab } from 'react-bootstrap';
import { FaSave, FaUndo, FaDownload, FaUpload } from 'react-icons/fa';
import { systemSettingsAPI } from '../services/api';

const SystemSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await systemSettingsAPI.getSettings();
      if (response.success) {
        setSettings(response.data.settings);
      } else {
        setError('Failed to fetch system settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to fetch system settings');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (category, field, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: value
      }
    }));
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      const response = await systemSettingsAPI.updateSettings(settings);
      if (response.success) {
        setSuccess('System settings saved successfully!');
        setSettings(response.data.settings);
      } else {
        setError(response.message || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container className="py-4">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-2">Loading system settings...</p>
        </div>
      </Container>
    );
  }

  if (!settings) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          Failed to load system settings. Please try refreshing the page.
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">System Settings</h2>
              <p className="text-muted">Configure system-wide settings and preferences</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="outline-secondary" onClick={fetchSettings}>
                <FaUndo className="me-2" />
                Reset
              </Button>
              <Button variant="success" onClick={handleSubmit} disabled={saving}>
                {saving ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <FaSave className="me-2" />
                    Save All
                  </>
                )}
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-4">
        <Tab eventKey="general" title="General">
          <Card>
            <Card.Header>
              <h5 className="mb-0">Organization Information</h5>
            </Card.Header>
            <Card.Body>
              <Form>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Organization Name *</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.organizationName || ''}
                        onChange={(e) => handleChange('', 'organizationName', e.target.value)}
                        placeholder="Enter organization name"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Email *</Form.Label>
                      <Form.Control
                        type="email"
                        value={settings.email || ''}
                        onChange={(e) => handleChange('', 'email', e.target.value)}
                        placeholder="Enter email address"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Phone *</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.phone || ''}
                        onChange={(e) => handleChange('', 'phone', e.target.value)}
                        placeholder="Enter phone number"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Website</Form.Label>
                      <Form.Control
                        type="url"
                        value={settings.website || ''}
                        onChange={(e) => handleChange('', 'website', e.target.value)}
                        placeholder="Enter website URL"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <h6 className="mt-4 mb-3">Address Information</h6>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Street Address</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.address?.street || ''}
                        onChange={(e) => handleChange('address', 'street', e.target.value)}
                        placeholder="Enter street address"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>City</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.address?.city || ''}
                        onChange={(e) => handleChange('address', 'city', e.target.value)}
                        placeholder="Enter city"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>State</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.address?.state || ''}
                        onChange={(e) => handleChange('address', 'state', e.target.value)}
                        placeholder="Enter state"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>ZIP Code</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.address?.zipCode || ''}
                        onChange={(e) => handleChange('address', 'zipCode', e.target.value)}
                        placeholder="Enter ZIP code"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Country</Form.Label>
                      <Form.Control
                        type="text"
                        value={settings.address?.country || ''}
                        onChange={(e) => handleChange('address', 'country', e.target.value)}
                        placeholder="Enter country"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="security" title="Security">
          <Card>
            <Card.Header>
              <h5 className="mb-0">Security Settings</h5>
            </Card.Header>
            <Card.Body>
              <Form>
                <h6 className="mb-3">Password Requirements</h6>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Minimum Password Length</Form.Label>
                      <Form.Control
                        type="number"
                        min="6"
                        max="20"
                        value={settings.passwordPolicy?.minLength || 8}
                        onChange={(e) => handleChange('passwordPolicy', 'minLength', parseInt(e.target.value))}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Session Timeout (minutes)</Form.Label>
                      <Form.Control
                        type="number"
                        min="5"
                        max="120"
                        value={settings.sessionTimeout || 30}
                        onChange={(e) => handleChange('', 'sessionTimeout', parseInt(e.target.value))}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Require uppercase letters"
                        checked={settings.passwordPolicy?.requireUppercase || false}
                        onChange={(e) => handleChange('passwordPolicy', 'requireUppercase', e.target.checked)}
                      />
                    </Form.Group>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Require lowercase letters"
                        checked={settings.passwordPolicy?.requireLowercase || false}
                        onChange={(e) => handleChange('passwordPolicy', 'requireLowercase', e.target.checked)}
                      />
                    </Form.Group>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Require numbers"
                        checked={settings.passwordPolicy?.requireNumbers || false}
                        onChange={(e) => handleChange('passwordPolicy', 'requireNumbers', e.target.checked)}
                      />
                    </Form.Group>
                    <Form.Group className="mb-3">
                      <Form.Check
                        type="checkbox"
                        label="Require special characters"
                        checked={settings.passwordPolicy?.requireSpecialChars || false}
                        onChange={(e) => handleChange('passwordPolicy', 'requireSpecialChars', e.target.checked)}
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Form>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="notifications" title="Notifications">
          <Card>
            <Card.Header>
              <h5 className="mb-0">Notification Settings</h5>
            </Card.Header>
            <Card.Body>
              <Form>
                <h6 className="mb-3">Email Notifications</h6>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Enable email notifications"
                    checked={settings.emailNotifications?.enabled || false}
                    onChange={(e) => handleChange('emailNotifications', 'enabled', e.target.checked)}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="New user registration notifications"
                    checked={settings.emailNotifications?.newUser || false}
                    onChange={(e) => handleChange('emailNotifications', 'newUser', e.target.checked)}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Policy expiry notifications"
                    checked={settings.emailNotifications?.policyExpiry || false}
                    onChange={(e) => handleChange('emailNotifications', 'policyExpiry', e.target.checked)}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Claim status notifications"
                    checked={settings.emailNotifications?.claimStatus || false}
                    onChange={(e) => handleChange('emailNotifications', 'claimStatus', e.target.checked)}
                  />
                </Form.Group>
              </Form>
            </Card.Body>
          </Card>
        </Tab>

        <Tab eventKey="system" title="System">
          <Card>
            <Card.Header>
              <h5 className="mb-0">System Configuration</h5>
            </Card.Header>
            <Card.Body>
              <Form>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Default Currency</Form.Label>
                      <Form.Select
                        value={settings.defaultCurrency || 'INR'}
                        onChange={(e) => handleChange('', 'defaultCurrency', e.target.value)}
                      >
                        <option value="INR">Indian Rupee (₹)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Date Format</Form.Label>
                      <Form.Select
                        value={settings.dateFormat || 'DD/MM/YYYY'}
                        onChange={(e) => handleChange('', 'dateFormat', e.target.value)}
                      >
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Items Per Page</Form.Label>
                      <Form.Select
                        value={settings.itemsPerPage || 10}
                        onChange={(e) => handleChange('', 'itemsPerPage', parseInt(e.target.value))}
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Timezone</Form.Label>
                      <Form.Select
                        value={settings.timezone || 'Asia/Kolkata'}
                        onChange={(e) => handleChange('', 'timezone', e.target.value)}
                      >
                        <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                        <option value="America/New_York">America/New_York (EST)</option>
                        <option value="Europe/London">Europe/London (GMT)</option>
                        <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Maintenance Mode"
                    checked={settings.maintenanceMode || false}
                    onChange={(e) => handleChange('', 'maintenanceMode', e.target.checked)}
                  />
                  <Form.Text className="text-muted">
                    When enabled, only administrators can access the system
                  </Form.Text>
                </Form.Group>
              </Form>
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>
    </Container>
  );
};

export default SystemSettings;
