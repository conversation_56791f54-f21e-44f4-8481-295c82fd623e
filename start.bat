@echo off
echo ========================================
echo    Morya Insurance Management System
echo ========================================
echo.

echo Starting MongoDB...
echo Please ensure MongoDB is installed and running
echo If using Docker: docker run -d -p 27017:27017 --name mongodb mongo:latest
echo.

echo Starting Backend Server...
cd server
start "Backend Server" cmd /k "npm run dev"
cd ..

echo.
echo Starting Frontend Application...
cd moryaInsure
start "Frontend App" cmd /k "npm start"
cd ..

echo.
echo ========================================
echo    Application Starting...
echo ========================================
echo.
echo Backend will be available at: http://localhost:5000
echo Frontend will be available at: http://localhost:3000
echo.
echo Demo Credentials:
echo Admin: <EMAIL> / admin123
echo Employee: <EMAIL> / emp123
echo Customer: <EMAIL> / cust123
echo.
echo Press any key to exit...
pause > nul
