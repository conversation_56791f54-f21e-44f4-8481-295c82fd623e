import React, { useState } from 'react';
import { Table, Button, Form, Modal, Row, Col } from 'react-bootstrap';
import { FaEdit, FaTrash } from 'react-icons/fa';

const PolicyHolder = () => {
  const [showModal, setShowModal] = useState(false);
  const [search, setSearch] = useState('');

  const [holders, setHolders] = useState([
    // {
    //   id: 1,
    //   holderName: '<PERSON>',
    //   contactNumber: '9876543210',
    //   policyName: 'AutoSecure Collision Plan',
    //   category: 'Auto Insurance',
    //   subCategory: 'Comprehensive Coverage',
    //   sumAssured: '80000',
    //   premium: '8000',
    //   tenure: '36',
    //   status: 'Active'
    // },
    // {
    //   id: 2,
    //   holderName: '<PERSON><PERSON><PERSON>',
    //   contactNumber: '9876543770',
    //   policyName: 'FamilyShield Term Plan',
    //   category: 'Life Insurance',
    //   subCategory: 'Term Life Insurance',
    //   sumAssured: '150000',
    //   premium: '20000',
    //   tenure: '24',
    //   status: 'Pending'
    // }
  ]);

  const [form, setForm] = useState({
    holderName: '',
    contactNumber: '',
    policyName: '',
    category: '',
    subCategory: '',
    sumAssured: '',
    premium: '',
    tenure: ''
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    const newHolder = { ...form, id: holders.length + 1, status: 'Active' };
    setHolders([...holders, newHolder]);
    setShowModal(false);
    setForm({
      holderName: '', contactNumber: '', policyName: '', category: '',
      subCategory: '', sumAssured: '', premium: '', tenure: ''
    });
  };

  const filteredHolders = holders.filter(h =>
    h.holderName.toLowerCase().includes(search.toLowerCase()) ||
    h.policyName.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="container-fluid p-3">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h4 className="fw-bold text-uppercase">Policy Holders</h4>
        <Button variant="primary" onClick={() => setShowModal(true)}>+ New</Button>
      </div>

      <div className="mb-3 d-flex flex-wrap gap-2">
        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      <div className="mb-3">
        <Form.Control
          type="text"
          placeholder="Search policy holders..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <Table bordered hover responsive className="shadow-sm">
        <thead className="table-primary">
          <tr>
            <th>Policy Holder Name</th>
            <th>Contact Number</th>
            <th>Policy Name</th>
            <th>Category</th>
            <th>Sub Category</th>
            <th>Sum Assured</th>
            <th>Premium</th>
            <th>Tenure</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filteredHolders.map((h) => (
            <tr key={h.id}>
              <td>{h.holderName}</td>
              <td>{h.contactNumber}</td>
              <td>{h.policyName}</td>
              <td>{h.category}</td>
              <td>{h.subCategory}</td>
              <td>{h.sumAssured} PHP</td>
              <td>{h.premium} PHP</td>
              <td>{h.tenure} months</td>
              <td><span className={`badge bg-${h.status === 'Active' ? 'success' : 'warning'}`}>{h.status}</span></td>
              <td>
                <Button variant="primary" size="sm" className="me-2"><FaEdit /></Button>
                <Button variant="danger" size="sm"><FaTrash /></Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      {/* Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>New Policy Holder</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Policy Holder Name</Form.Label>
              <Form.Control name="holderName" value={form.holderName} onChange={handleChange} />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Contact Number</Form.Label>
              <Form.Control name="contactNumber" value={form.contactNumber} onChange={handleChange} />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Policy Name</Form.Label>
              <Form.Control name="policyName" value={form.policyName} onChange={handleChange} />
            </Form.Group>

            <Row className="mb-3">
              <Col>
                <Form.Label>Category</Form.Label>
                <Form.Select name="category" value={form.category} onChange={handleChange}>
                  <option value="">Select</option>
                  {/* <option>Auto Insurance</option>
                  <option>Life Insurance</option>
                  <option>Travel Insurance</option> */}
                </Form.Select>
              </Col>
              <Col>
                <Form.Label>Sub Category</Form.Label>
                <Form.Select name="subCategory" value={form.subCategory} onChange={handleChange}>
                  <option value="">Select</option>
                  {/* <option>Comprehensive Coverage</option>
                  <option>Term Life Insurance</option>
                  <option>Travel Cancellation Insurance</option> */}
                </Form.Select>
              </Col>
            </Row>

            <Row className="mb-3">
              <Col>
                <Form.Label>Sum Assured</Form.Label>
                <Form.Control name="sumAssured" value={form.sumAssured} onChange={handleChange} />
              </Col>
              <Col>
                <Form.Label>Premium</Form.Label>
                <Form.Control name="premium" value={form.premium} onChange={handleChange} />
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Tenure (Months)</Form.Label>
              <Form.Control name="tenure" value={form.tenure} onChange={handleChange} />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
          <Button variant="primary" onClick={handleSave}>Save Changes</Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default PolicyHolder;