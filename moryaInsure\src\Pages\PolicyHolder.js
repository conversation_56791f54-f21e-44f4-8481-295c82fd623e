import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Form, Modal, <PERSON>, Col, Con<PERSON>er, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';
import { policyHoldersAPI, policiesAPI } from '../services/api';

const PolicyHolder = () => {
  const [policyHolders, setPolicyHolders] = useState([]);
  const [policies, setPolicies] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedHolder, setSelectedHolder] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  // Form state
  const [form, setForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'India'
    },
    emergencyContact: {
      name: '',
      relationship: '',
      phone: ''
    },
    occupation: '',
    annualIncome: '',
    status: 'active'
  });

  useEffect(() => {
    fetchPolicyHolders();
    fetchPolicies();
  }, []);

  const fetchPolicyHolders = async () => {
    try {
      setLoading(true);
      const response = await policyHoldersAPI.getPolicyHolders();
      if (response.success) {
        setPolicyHolders(response.data.policyHolders || []);
      } else {
        setError('Failed to fetch policy holders');
      }
    } catch (error) {
      console.error('Error fetching policy holders:', error);
      setError('Failed to fetch policy holders');
    } finally {
      setLoading(false);
    }
  };

  const fetchPolicies = async () => {
    try {
      const response = await policiesAPI.getPolicies();
      if (response.success) {
        setPolicies(response.data.policies || []);
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setForm(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setForm(prev => ({
        ...prev,
        [name]: value
      }));
    }
    setError('');
  };

  const handleSave = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await policyHoldersAPI.createPolicyHolder(form);
      if (response.success) {
        setSuccess('Policy holder created successfully!');
        resetForm();
        setShowModal(false);
        fetchPolicyHolders();
      } else {
        setError(response.message || 'Failed to create policy holder');
      }
    } catch (error) {
      console.error('Error creating policy holder:', error);
      setError('Failed to create policy holder');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await policyHoldersAPI.updatePolicyHolder(selectedHolder._id, form);
      if (response.success) {
        setSuccess('Policy holder updated successfully!');
        setShowEditModal(false);
        setSelectedHolder(null);
        fetchPolicyHolders();
      } else {
        setError(response.message || 'Failed to update policy holder');
      }
    } catch (error) {
      console.error('Error updating policy holder:', error);
      setError('Failed to update policy holder');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (holderId) => {
    if (window.confirm('Are you sure you want to delete this policy holder?')) {
      try {
        const response = await policyHoldersAPI.deletePolicyHolder(holderId);
        if (response.success) {
          setSuccess('Policy holder deleted successfully!');
          fetchPolicyHolders();
        } else {
          setError(response.message || 'Failed to delete policy holder');
        }
      } catch (error) {
        console.error('Error deleting policy holder:', error);
        setError('Failed to delete policy holder');
      }
    }
  };

  const resetForm = () => {
    setForm({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'India'
      },
      emergencyContact: {
        name: '',
        relationship: '',
        phone: ''
      },
      occupation: '',
      annualIncome: '',
      status: 'active'
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (holder) => {
    setSelectedHolder(holder);
    setForm({
      firstName: holder.firstName,
      lastName: holder.lastName,
      email: holder.email,
      phone: holder.phone,
      dateOfBirth: holder.dateOfBirth ? new Date(holder.dateOfBirth).toISOString().split('T')[0] : '',
      gender: holder.gender || '',
      address: {
        street: holder.address?.street || '',
        city: holder.address?.city || '',
        state: holder.address?.state || '',
        zipCode: holder.address?.zipCode || '',
        country: holder.address?.country || 'India'
      },
      emergencyContact: {
        name: holder.emergencyContact?.name || '',
        relationship: holder.emergencyContact?.relationship || '',
        phone: holder.emergencyContact?.phone || ''
      },
      occupation: holder.occupation || '',
      annualIncome: holder.annualIncome || '',
      status: holder.status || 'active'
    });
    setShowEditModal(true);
  };

  const openViewModal = (holder) => {
    setSelectedHolder(holder);
    setShowViewModal(true);
  };

  const filteredHolders = policyHolders.filter((holder) =>
    `${holder.firstName} ${holder.lastName}`.toLowerCase().includes(search.toLowerCase()) ||
    holder.email?.toLowerCase().includes(search.toLowerCase()) ||
    holder.phone?.includes(search)
  );

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { variant: 'success', text: 'Active' },
      'inactive': { variant: 'secondary', text: 'Inactive' },
      'suspended': { variant: 'warning', text: 'Suspended' },
      'blocked': { variant: 'danger', text: 'Blocked' }
    };

    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatDate = (dateString) => {
    return dateString ? new Date(dateString).toLocaleDateString('en-IN') : 'N/A';
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Policy Holders</h2>
              <p className="text-muted">Manage insurance policy holders</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="primary" onClick={openCreateModal}>
                <FaPlus className="me-2" />
                New Policy Holder
              </Button>
              <Button variant="secondary">
                <FaFileImport className="me-2" />
                Import
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Row className="mb-3">
        <Col md={6}>
          <div className="position-relative">
            <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
            <Form.Control
              type="text"
              placeholder="Search policy holders..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="ps-5"
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading policy holders...</p>
                </div>
              ) : filteredHolders.length === 0 ? (
                <div className="text-center py-4">
                  <FaUser size={48} className="text-muted mb-3" />
                  <h5>No Policy Holders Found</h5>
                  <p className="text-muted">
                    {search ? 'No policy holders match your search.' : 'Start by adding your first policy holder.'}
                  </p>
                  {!search && (
                    <Button variant="primary" onClick={openCreateModal}>
                      <FaPlus className="me-2" />
                      Add First Policy Holder
                    </Button>
                  )}
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Contact</th>
                      <th>Email</th>
                      <th>Date of Birth</th>
                      <th>Occupation</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredHolders.map((holder) => (
                      <tr key={holder._id}>
                        <td>
                          <div>
                            <strong>{holder.firstName} {holder.lastName}</strong>
                            <br />
                            <small className="text-muted">
                              <FaUser className="me-1" />
                              {holder.gender || 'N/A'}
                            </small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <FaPhone className="me-1 text-muted" />
                            {holder.phone}
                          </div>
                        </td>
                        <td>
                          <div>
                            <FaEnvelope className="me-1 text-muted" />
                            {holder.email}
                          </div>
                        </td>
                        <td>{formatDate(holder.dateOfBirth)}</td>
                        <td>{holder.occupation || 'N/A'}</td>
                        <td>{getStatusBadge(holder.status)}</td>
                        <td>{formatDate(holder.createdAt)}</td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-info"
                              size="sm"
                              onClick={() => openViewModal(holder)}
                              title="View Details"
                            >
                              <FaEye />
                            </Button>
                            <Button
                              variant="outline-warning"
                              size="sm"
                              onClick={() => openEditModal(holder)}
                              title="Edit Policy Holder"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDelete(holder._id)}
                              title="Delete Policy Holder"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Create Policy Holder Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Policy Holder</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="firstName"
                    value={form.firstName}
                    onChange={handleChange}
                    placeholder="Enter first name"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="lastName"
                    value={form.lastName}
                    onChange={handleChange}
                    placeholder="Enter last name"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email *</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={form.email}
                    onChange={handleChange}
                    placeholder="Enter email address"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone *</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={form.phone}
                    onChange={handleChange}
                    placeholder="Enter phone number"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date of Birth</Form.Label>
                  <Form.Control
                    type="date"
                    name="dateOfBirth"
                    value={form.dateOfBirth}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Gender</Form.Label>
                  <Form.Select
                    name="gender"
                    value={form.gender}
                    onChange={handleChange}
                  >
                    <option value="">Select gender...</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Occupation</Form.Label>
                  <Form.Control
                    type="text"
                    name="occupation"
                    value={form.occupation}
                    onChange={handleChange}
                    placeholder="Enter occupation"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Annual Income</Form.Label>
                  <Form.Control
                    type="number"
                    name="annualIncome"
                    value={form.annualIncome}
                    onChange={handleChange}
                    placeholder="Enter annual income"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Creating...
              </>
            ) : (
              'Create Policy Holder'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PolicyHolder;