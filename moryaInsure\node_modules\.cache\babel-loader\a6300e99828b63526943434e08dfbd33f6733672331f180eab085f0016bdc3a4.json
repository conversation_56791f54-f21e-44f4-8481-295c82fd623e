{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n    console.log('AuthContext: Checking stored auth data', {\n      token: !!token,\n      userData: !!userData\n    });\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        console.log('AuthContext: Setting user from localStorage', parsedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await authAPI.login({\n        email,\n        password\n      });\n      if (response.success) {\n        const {\n          user,\n          token\n        } = response.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n        setUser(user);\n        return {\n          success: true,\n          user\n        };\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authAPI.register(userData);\n      if (response.success) {\n        return {\n          success: true,\n          user: response.data.user\n        };\n      } else {\n        throw new Error(response.message || 'Registration failed');\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: error.message || 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n  const updateUser = updatedUserData => {\n    const updatedUser = {\n      ...user,\n      ...updatedUserData\n    };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n  const hasAnyRole = roles => {\n    return user && roles.includes(user.role);\n  };\n  const isAuthenticated = () => {\n    return !!user && !!localStorage.getItem('token');\n  };\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    hasRole,\n    hasAnyRole,\n    isAuthenticated\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "userData", "console", "log", "parsedUser", "JSON", "parse", "error", "removeItem", "login", "email", "password", "response", "success", "data", "setItem", "stringify", "message", "register", "logout", "updateUser", "updatedUserData", "updatedUser", "hasRole", "role", "hasAnyRole", "roles", "includes", "isAuthenticated", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const token = localStorage.getItem('token');\n    const userData = localStorage.getItem('user');\n\n    console.log('AuthContext: Checking stored auth data', { token: !!token, userData: !!userData });\n\n    if (token && userData) {\n      try {\n        const parsedUser = JSON.parse(userData);\n        console.log('AuthContext: Setting user from localStorage', parsedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (email, password) => {\n    try {\n      const response = await authAPI.login({ email, password });\n\n      if (response.success) {\n        const { user, token } = response.data;\n\n        localStorage.setItem('token', token);\n        localStorage.setItem('user', JSON.stringify(user));\n        setUser(user);\n\n        return { success: true, user };\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      return { success: false, error: error.message || 'Login failed' };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await authAPI.register(userData);\n\n      if (response.success) {\n        return { success: true, user: response.data.user };\n      } else {\n        throw new Error(response.message || 'Registration failed');\n      }\n    } catch (error) {\n      return { success: false, error: error.message || 'Registration failed' };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  const updateUser = (updatedUserData) => {\n    const updatedUser = { ...user, ...updatedUserData };\n    setUser(updatedUser);\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n  };\n\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  const hasAnyRole = (roles) => {\n    return user && roles.includes(user.role);\n  };\n\n  const isAuthenticated = () => {\n    return !!user && !!localStorage.getItem('token');\n  };\n\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    hasRole,\n    hasAnyRole,\n    isAuthenticated\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7CE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MAAEL,KAAK,EAAE,CAAC,CAACA,KAAK;MAAEG,QAAQ,EAAE,CAAC,CAACA;IAAS,CAAC,CAAC;IAE/F,IAAIH,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACF,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;QACvCC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEC,UAAU,CAAC;QACtET,OAAO,CAACS,UAAU,CAAC;MACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;QAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAX,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,OAAO,CAAC0B,KAAK,CAAC;QAAEC,KAAK;QAAEC;MAAS,CAAC,CAAC;MAEzD,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAM;UAAEnB,IAAI;UAAEI;QAAM,CAAC,GAAGc,QAAQ,CAACE,IAAI;QAErCf,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;QACpCC,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACW,SAAS,CAACtB,IAAI,CAAC,CAAC;QAClDC,OAAO,CAACD,IAAI,CAAC;QAEb,OAAO;UAAEmB,OAAO,EAAE,IAAI;UAAEnB;QAAK,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAIJ,KAAK,CAACsB,QAAQ,CAACK,OAAO,IAAI,cAAc,CAAC;MACrD;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,OAAO;QAAEM,OAAO,EAAE,KAAK;QAAEN,KAAK,EAAEA,KAAK,CAACU,OAAO,IAAI;MAAe,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOjB,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAM7B,OAAO,CAACmC,QAAQ,CAACjB,QAAQ,CAAC;MAEjD,IAAIW,QAAQ,CAACC,OAAO,EAAE;QACpB,OAAO;UAAEA,OAAO,EAAE,IAAI;UAAEnB,IAAI,EAAEkB,QAAQ,CAACE,IAAI,CAACpB;QAAK,CAAC;MACpD,CAAC,MAAM;QACL,MAAM,IAAIJ,KAAK,CAACsB,QAAQ,CAACK,OAAO,IAAI,qBAAqB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,OAAO;QAAEM,OAAO,EAAE,KAAK;QAAEN,KAAK,EAAEA,KAAK,CAACU,OAAO,IAAI;MAAsB,CAAC;IAC1E;EACF,CAAC;EAED,MAAME,MAAM,GAAGA,CAAA,KAAM;IACnBpB,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/Bb,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMyB,UAAU,GAAIC,eAAe,IAAK;IACtC,MAAMC,WAAW,GAAG;MAAE,GAAG5B,IAAI;MAAE,GAAG2B;IAAgB,CAAC;IACnD1B,OAAO,CAAC2B,WAAW,CAAC;IACpBvB,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACW,SAAS,CAACM,WAAW,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,OAAO,GAAIC,IAAI,IAAK;IACxB,OAAO9B,IAAI,IAAIA,IAAI,CAAC8B,IAAI,KAAKA,IAAI;EACnC,CAAC;EAED,MAAMC,UAAU,GAAIC,KAAK,IAAK;IAC5B,OAAOhC,IAAI,IAAIgC,KAAK,CAACC,QAAQ,CAACjC,IAAI,CAAC8B,IAAI,CAAC;EAC1C,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAAClC,IAAI,IAAI,CAAC,CAACK,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAClD,CAAC;EAED,MAAM6B,KAAK,GAAG;IACZnC,IAAI;IACJE,OAAO;IACPa,KAAK;IACLS,QAAQ;IACRC,MAAM;IACNC,UAAU;IACVG,OAAO;IACPE,UAAU;IACVG;EACF,CAAC;EAED,oBACE3C,OAAA,CAACC,WAAW,CAAC4C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArC,QAAA,EAChCA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzC,GAAA,CApGWF,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAsGzB,eAAeL,WAAW;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}