const mongoose = require('mongoose');
require('dotenv').config();

// Import all models to ensure they're registered
const User = require('../models/User');
const Category = require('../models/Category');
const SubCategory = require('../models/SubCategory');
const Policy = require('../models/Policy');
const PolicyHolder = require('../models/PolicyHolder');
const Ticket = require('../models/Ticket');
const TicketCategory = require('../models/TicketCategory');
const Claim = require('../models/Claim');
const Notification = require('../models/Notification');
const SystemSettings = require('../models/SystemSettings');

async function cleanupDuplicateIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/insurance23jun');
    console.log('Connected to MongoDB');

    const collections = [
      { name: 'users', model: User, duplicateFields: ['email'] },
      { name: 'categories', model: Category, duplicateFields: ['name'] },
      { name: 'subcategories', model: SubCategory, duplicateFields: ['code'] },
      { name: 'policies', model: Policy, duplicateFields: ['policyNumber'] },
      { name: 'policyholders', model: PolicyHolder, duplicateFields: ['email'] },
      { name: 'tickets', model: Ticket, duplicateFields: ['ticketNumber'] },
      { name: 'claims', model: Claim, duplicateFields: ['claimNumber'] },
      { name: 'notifications', model: Notification, duplicateFields: ['expiresAt'] }
    ];

    for (const collection of collections) {
      console.log(`\n--- Processing ${collection.name} collection ---`);
      
      try {
        // Get current indexes
        const indexes = await collection.model.collection.getIndexes();
        console.log(`Current indexes for ${collection.name}:`, Object.keys(indexes));

        // Drop duplicate indexes for each field
        for (const field of collection.duplicateFields) {
          try {
            // Try to drop the regular index (keep unique indexes)
            const indexName = `${field}_1`;
            if (indexes[indexName] && !indexes[indexName].unique) {
              await collection.model.collection.dropIndex(indexName);
              console.log(`✓ Dropped duplicate index: ${indexName}`);
            } else if (indexes[indexName] && indexes[indexName].unique) {
              console.log(`✓ Keeping unique index: ${indexName}`);
            } else {
              console.log(`- No duplicate index found for: ${field}`);
            }
          } catch (error) {
            if (error.code === 27) {
              console.log(`- Index ${field}_1 doesn't exist (already cleaned)`);
            } else {
              console.log(`- Error dropping index ${field}_1:`, error.message);
            }
          }
        }

        // Special handling for TTL indexes
        if (collection.name === 'notifications') {
          try {
            // Check if there are multiple expiresAt indexes
            const expiresIndexes = Object.keys(indexes).filter(key => key.includes('expiresAt'));
            console.log(`ExpiresAt indexes found:`, expiresIndexes);
            
            // Keep only the TTL index, drop any regular expiresAt index
            for (const indexName of expiresIndexes) {
              const indexInfo = indexes[indexName];
              if (!indexInfo.expireAfterSeconds && indexInfo.expireAfterSeconds !== 0) {
                // This is a regular index, not TTL - drop it
                await collection.model.collection.dropIndex(indexName);
                console.log(`✓ Dropped non-TTL expiresAt index: ${indexName}`);
              }
            }
          } catch (error) {
            console.log(`- Error handling TTL indexes:`, error.message);
          }
        }

      } catch (error) {
        console.log(`Error processing ${collection.name}:`, error.message);
      }
    }

    console.log('\n--- Index Cleanup Complete ---');
    
    // Verify the cleanup by checking indexes again
    console.log('\n--- Verification ---');
    for (const collection of collections) {
      try {
        const indexes = await collection.model.collection.getIndexes();
        console.log(`${collection.name} final indexes:`, Object.keys(indexes));
      } catch (error) {
        console.log(`Error verifying ${collection.name}:`, error.message);
      }
    }

  } catch (error) {
    console.error('Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Alternative method: Drop and recreate all indexes
async function recreateAllIndexes() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/insurance23jun');
    console.log('Connected to MongoDB for index recreation');

    const models = [User, Category, SubCategory, Policy, PolicyHolder, Ticket, TicketCategory, Claim, Notification, SystemSettings];

    for (const model of models) {
      try {
        console.log(`\n--- Recreating indexes for ${model.modelName} ---`);
        
        // Drop all indexes except _id
        await model.collection.dropIndexes();
        console.log(`✓ Dropped all indexes for ${model.modelName}`);
        
        // Recreate indexes based on schema
        await model.syncIndexes();
        console.log(`✓ Recreated indexes for ${model.modelName}`);
        
      } catch (error) {
        console.log(`Error recreating indexes for ${model.modelName}:`, error.message);
      }
    }

    console.log('\n--- Index Recreation Complete ---');

  } catch (error) {
    console.error('Error during index recreation:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the cleanup
if (process.argv[2] === '--recreate') {
  console.log('Running index recreation...');
  recreateAllIndexes();
} else {
  console.log('Running index cleanup...');
  cleanupDuplicateIndexes();
}

// Export for use in other scripts
module.exports = { cleanupDuplicateIndexes, recreateAllIndexes };
