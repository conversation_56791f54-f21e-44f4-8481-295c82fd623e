{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Card, Form, But<PERSON>, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FaUser, FaLock, FaEye, FaEyeSlash } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [validated, setValidated] = useState(false);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user starts typing\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Simulate API call - replace with actual API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Mock authentication logic\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        const userData = {\n          id: 1,\n          name: 'Admin User',\n          email: formData.email,\n          role: 'admin'\n        };\n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        navigate('/dashboard');\n      } else if (formData.email === '<EMAIL>' && formData.password === 'emp123') {\n        const userData = {\n          id: 2,\n          name: 'Employee User',\n          email: formData.email,\n          role: 'employee'\n        };\n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        navigate('/dashboard');\n      } else if (formData.email === '<EMAIL>' && formData.password === 'cust123') {\n        const userData = {\n          id: 3,\n          name: 'Customer User',\n          email: formData.email,\n          role: 'customer'\n        };\n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        navigate('/dashboard');\n      } else {\n        setError('Invalid email or password');\n      }\n    } catch (err) {\n      setError('Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-vh-100 d-flex align-items-center bg-light\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow-lg border-0\",\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"fw-bold text-primary\",\n                  children: \"Welcome Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted\",\n                  children: \"Sign in to your account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-3\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                noValidate: true,\n                validated: validated,\n                onSubmit: handleSubmit,\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"position-relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      name: \"email\",\n                      value: formData.email,\n                      onChange: handleChange,\n                      required: true,\n                      placeholder: \"Enter your email\",\n                      className: \"ps-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: \"Please provide a valid email.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Password\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"position-relative\",\n                    children: [/*#__PURE__*/_jsxDEV(FaLock, {\n                      className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: showPassword ? 'text' : 'password',\n                      name: \"password\",\n                      value: formData.password,\n                      onChange: handleChange,\n                      required: true,\n                      placeholder: \"Enter your password\",\n                      className: \"ps-5 pe-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"link\",\n                      className: \"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\",\n                      onClick: () => setShowPassword(!showPassword),\n                      type: \"button\",\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(FaEyeSlash, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 147,\n                        columnNumber: 41\n                      }, this) : /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 147,\n                        columnNumber: 58\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: \"Please provide a password.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"primary\",\n                  className: \"w-100 mb-3\",\n                  disabled: loading,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 25\n                    }, this), \"Signing In...\"]\n                  }, void 0, true) : 'Sign In'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/register\",\n                    className: \"text-primary text-decoration-none\",\n                    children: \"Sign up here\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                className: \"my-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Demo Credentials:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 55\n                  }, this), \"Admin: <EMAIL> / admin123\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 54\n                  }, this), \"Employee: <EMAIL> / emp123\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 58\n                  }, this), \"Customer: <EMAIL> / cust123\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"AEyMCaRtnHwFG0oHXCbSIe8TUck=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "FaUser", "FaLock", "FaEye", "FaEyeSlash", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "navigate", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "loading", "setLoading", "error", "setError", "validated", "setValidated", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "Promise", "resolve", "setTimeout", "userData", "id", "role", "localStorage", "setItem", "JSON", "stringify", "err", "className", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "noValidate", "onSubmit", "Group", "Label", "Control", "type", "onChange", "required", "placeholder", "<PERSON><PERSON><PERSON>", "onClick", "disabled", "animation", "size", "to", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\n\nconst Login = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [validated, setValidated] = useState(false);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user starts typing\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    \n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Simulate API call - replace with actual API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Mock authentication logic\n      if (formData.email === '<EMAIL>' && formData.password === 'admin123') {\n        const userData = {\n          id: 1,\n          name: 'Admin User',\n          email: formData.email,\n          role: 'admin'\n        };\n        \n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        \n        navigate('/dashboard');\n      } else if (formData.email === '<EMAIL>' && formData.password === 'emp123') {\n        const userData = {\n          id: 2,\n          name: 'Employee User',\n          email: formData.email,\n          role: 'employee'\n        };\n        \n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        \n        navigate('/dashboard');\n      } else if (formData.email === '<EMAIL>' && formData.password === 'cust123') {\n        const userData = {\n          id: 3,\n          name: 'Customer User',\n          email: formData.email,\n          role: 'customer'\n        };\n        \n        localStorage.setItem('token', 'mock-jwt-token');\n        localStorage.setItem('user', JSON.stringify(userData));\n        \n        navigate('/dashboard');\n      } else {\n        setError('Invalid email or password');\n      }\n    } catch (err) {\n      setError('Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-vh-100 d-flex align-items-center bg-light\">\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col md={6} lg={4}>\n            <Card className=\"shadow-lg border-0\">\n              <Card.Body className=\"p-5\">\n                <div className=\"text-center mb-4\">\n                  <h2 className=\"fw-bold text-primary\">Welcome Back</h2>\n                  <p className=\"text-muted\">Sign in to your account</p>\n                </div>\n\n                {error && (\n                  <Alert variant=\"danger\" className=\"mb-3\">\n                    {error}\n                  </Alert>\n                )}\n\n                <Form noValidate validated={validated} onSubmit={handleSubmit}>\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <div className=\"position-relative\">\n                      <FaUser className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                      <Form.Control\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleChange}\n                        required\n                        placeholder=\"Enter your email\"\n                        className=\"ps-5\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        Please provide a valid email.\n                      </Form.Control.Feedback>\n                    </div>\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-4\">\n                    <Form.Label>Password</Form.Label>\n                    <div className=\"position-relative\">\n                      <FaLock className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\n                      <Form.Control\n                        type={showPassword ? 'text' : 'password'}\n                        name=\"password\"\n                        value={formData.password}\n                        onChange={handleChange}\n                        required\n                        placeholder=\"Enter your password\"\n                        className=\"ps-5 pe-5\"\n                      />\n                      <Button\n                        variant=\"link\"\n                        className=\"position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0\"\n                        onClick={() => setShowPassword(!showPassword)}\n                        type=\"button\"\n                      >\n                        {showPassword ? <FaEyeSlash /> : <FaEye />}\n                      </Button>\n                      <Form.Control.Feedback type=\"invalid\">\n                        Please provide a password.\n                      </Form.Control.Feedback>\n                    </div>\n                  </Form.Group>\n\n                  <Button\n                    type=\"submit\"\n                    variant=\"primary\"\n                    className=\"w-100 mb-3\"\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                        Signing In...\n                      </>\n                    ) : (\n                      'Sign In'\n                    )}\n                  </Button>\n                </Form>\n\n                <div className=\"text-center\">\n                  <p className=\"mb-0\">\n                    Don't have an account?{' '}\n                    <Link to=\"/register\" className=\"text-primary text-decoration-none\">\n                      Sign up here\n                    </Link>\n                  </p>\n                </div>\n\n                <hr className=\"my-4\" />\n\n                <div className=\"text-center\">\n                  <small className=\"text-muted\">\n                    <strong>Demo Credentials:</strong><br />\n                    Admin: <EMAIL> / admin123<br />\n                    Employee: <EMAIL> / emp123<br />\n                    Customer: <EMAIL> / cust123\n                  </small>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAClE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmC,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFP,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,MAAMC,IAAI,GAAGN,CAAC,CAACO,aAAa;IAE5B,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,KAAK,KAAK,EAAE;MAClCR,CAAC,CAACS,eAAe,CAAC,CAAC;MACnBX,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAJ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,IAAIxB,QAAQ,CAACE,KAAK,KAAK,iBAAiB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;QAC5E,MAAMuB,QAAQ,GAAG;UACfC,EAAE,EAAE,CAAC;UACLZ,IAAI,EAAE,YAAY;UAClBb,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrB0B,IAAI,EAAE;QACR,CAAC;QAEDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;QAC/CD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAAC,CAAC;QAEtD3B,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM,IAAIC,QAAQ,CAACE,KAAK,KAAK,oBAAoB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,QAAQ,EAAE;QACpF,MAAMuB,QAAQ,GAAG;UACfC,EAAE,EAAE,CAAC;UACLZ,IAAI,EAAE,eAAe;UACrBb,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrB0B,IAAI,EAAE;QACR,CAAC;QAEDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;QAC/CD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAAC,CAAC;QAEtD3B,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM,IAAIC,QAAQ,CAACE,KAAK,KAAK,oBAAoB,IAAIF,QAAQ,CAACG,QAAQ,KAAK,SAAS,EAAE;QACrF,MAAMuB,QAAQ,GAAG;UACfC,EAAE,EAAE,CAAC;UACLZ,IAAI,EAAE,eAAe;UACrBb,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrB0B,IAAI,EAAE;QACR,CAAC;QAEDC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;QAC/CD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAAC,CAAC;QAEtD3B,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLU,QAAQ,CAAC,2BAA2B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACZxB,QAAQ,CAAC,iCAAiC,CAAC;IAC7C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IAAKwC,SAAS,EAAC,+CAA+C;IAAAC,QAAA,eAC5DzC,OAAA,CAAChB,SAAS;MAAAyD,QAAA,eACRzC,OAAA,CAACf,GAAG;QAACuD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCzC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAF,QAAA,eAChBzC,OAAA,CAACb,IAAI;YAACqD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAClCzC,OAAA,CAACb,IAAI,CAACyD,IAAI;cAACJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBACxBzC,OAAA;gBAAKwC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BzC,OAAA;kBAAIwC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtDhD,OAAA;kBAAGwC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,EAELlC,KAAK,iBACJd,OAAA,CAACV,KAAK;gBAAC2D,OAAO,EAAC,QAAQ;gBAACT,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACrC3B;cAAK;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,eAEDhD,OAAA,CAACZ,IAAI;gBAAC8D,UAAU;gBAAClC,SAAS,EAAEA,SAAU;gBAACmC,QAAQ,EAAE5B,YAAa;gBAAAkB,QAAA,gBAC5DzC,OAAA,CAACZ,IAAI,CAACgE,KAAK;kBAACZ,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACiE,KAAK;oBAAAZ,QAAA,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtChD,OAAA;oBAAKwC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzC,OAAA,CAACN,MAAM;sBAAC8C,SAAS,EAAC;oBAAqE;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1FhD,OAAA,CAACZ,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,OAAO;sBACZlC,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;sBACtBgD,QAAQ,EAAEtC,YAAa;sBACvBuC,QAAQ;sBACRC,WAAW,EAAC,kBAAkB;sBAC9BlB,SAAS,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFhD,OAAA,CAACZ,IAAI,CAACkE,OAAO,CAACK,QAAQ;sBAACJ,IAAI,EAAC,SAAS;sBAAAd,QAAA,EAAC;oBAEtC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAuB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbhD,OAAA,CAACZ,IAAI,CAACgE,KAAK;kBAACZ,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACiE,KAAK;oBAAAZ,QAAA,EAAC;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjChD,OAAA;oBAAKwC,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzC,OAAA,CAACL,MAAM;sBAAC6C,SAAS,EAAC;oBAAqE;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1FhD,OAAA,CAACZ,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAE7C,YAAY,GAAG,MAAM,GAAG,UAAW;sBACzCW,IAAI,EAAC,UAAU;sBACfC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;sBACzB+C,QAAQ,EAAEtC,YAAa;sBACvBuC,QAAQ;sBACRC,WAAW,EAAC,qBAAqB;sBACjClB,SAAS,EAAC;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACFhD,OAAA,CAACX,MAAM;sBACL4D,OAAO,EAAC,MAAM;sBACdT,SAAS,EAAC,qEAAqE;sBAC/EoB,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAC,CAACD,YAAY,CAAE;sBAC9C6C,IAAI,EAAC,QAAQ;sBAAAd,QAAA,EAEZ/B,YAAY,gBAAGV,OAAA,CAACH,UAAU;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGhD,OAAA,CAACJ,KAAK;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACThD,OAAA,CAACZ,IAAI,CAACkE,OAAO,CAACK,QAAQ;sBAACJ,IAAI,EAAC,SAAS;sBAAAd,QAAA,EAAC;oBAEtC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAuB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbhD,OAAA,CAACX,MAAM;kBACLkE,IAAI,EAAC,QAAQ;kBACbN,OAAO,EAAC,SAAS;kBACjBT,SAAS,EAAC,YAAY;kBACtBqB,QAAQ,EAAEjD,OAAQ;kBAAA6B,QAAA,EAEjB7B,OAAO,gBACNZ,OAAA,CAAAE,SAAA;oBAAAuC,QAAA,gBACEzC,OAAA,CAACT,OAAO;sBAACuE,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAACvB,SAAS,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAE3D;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEPhD,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BzC,OAAA;kBAAGwC,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAC,wBACI,EAAC,GAAG,eAC1BzC,OAAA,CAACR,IAAI;oBAACwE,EAAE,EAAC,WAAW;oBAACxB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAEnE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENhD,OAAA;gBAAIwC,SAAS,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEvBhD,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BzC,OAAA;kBAAOwC,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3BzC,OAAA;oBAAAyC,QAAA,EAAQ;kBAAiB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAAAhD,OAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,qCACP,eAAAhD,OAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,yCACF,eAAAhD,OAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,0CAE7C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA/LID,KAAK;EAAA,QACQV,WAAW;AAAA;AAAAwE,EAAA,GADxB9D,KAAK;AAiMX,eAAeA,KAAK;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}