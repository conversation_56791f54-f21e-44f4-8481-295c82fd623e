{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\ThemeToggle.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Button } from 'react-bootstrap';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = ({\n  className = '',\n  style = {}\n}) => {\n  _s();\n  const {\n    theme,\n    toggleTheme,\n    isLoading\n  } = useTheme();\n  if (isLoading) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: toggleTheme,\n    className: `theme-toggle ${className}`,\n    style: {\n      position: 'fixed',\n      top: '20px',\n      right: '20px',\n      zIndex: 1050,\n      border: 'none',\n      borderRadius: '50%',\n      width: '50px',\n      height: '50px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: 'var(--card-bg)',\n      color: 'var(--text-primary)',\n      boxShadow: '0 2px 10px var(--card-shadow)',\n      transition: 'all 0.3s ease',\n      cursor: 'pointer',\n      ...style\n    },\n    title: `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    \"aria-label\": `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,\n    onMouseEnter: e => {\n      e.target.style.transform = 'scale(1.1)';\n      e.target.style.boxShadow = '0 4px 15px var(--card-shadow)';\n    },\n    onMouseLeave: e => {\n      e.target.style.transform = 'scale(1)';\n      e.target.style.boxShadow = '0 2px 10px var(--card-shadow)';\n    },\n    children: theme === 'light' ? /*#__PURE__*/_jsxDEV(FaMoon, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(FaSun, {\n      size: 18\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"bhs2nNrwnPXVPJcROq0+hArJHTo=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "FaSun", "FaMoon", "useTheme", "jsxDEV", "_jsxDEV", "ThemeToggle", "className", "style", "_s", "theme", "toggleTheme", "isLoading", "onClick", "position", "top", "right", "zIndex", "border", "borderRadius", "width", "height", "display", "alignItems", "justifyContent", "backgroundColor", "color", "boxShadow", "transition", "cursor", "title", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/ThemeToggle.js"], "sourcesContent": ["import React from 'react';\nimport { But<PERSON> } from 'react-bootstrap';\nimport { FaSun, FaMoon } from 'react-icons/fa';\nimport { useTheme } from '../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', style = {} }) => {\n  const { theme, toggleTheme, isLoading } = useTheme();\n\n  if (isLoading) {\n    return null;\n  }\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={`theme-toggle ${className}`}\n      style={{\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        zIndex: 1050,\n        border: 'none',\n        borderRadius: '50%',\n        width: '50px',\n        height: '50px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: 'var(--card-bg)',\n        color: 'var(--text-primary)',\n        boxShadow: '0 2px 10px var(--card-shadow)',\n        transition: 'all 0.3s ease',\n        cursor: 'pointer',\n        ...style\n      }}\n      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n      onMouseEnter={(e) => {\n        e.target.style.transform = 'scale(1.1)';\n        e.target.style.boxShadow = '0 4px 15px var(--card-shadow)';\n      }}\n      onMouseLeave={(e) => {\n        e.target.style.transform = 'scale(1)';\n        e.target.style.boxShadow = '0 2px 10px var(--card-shadow)';\n      }}\n    >\n      {theme === 'light' ? (\n        <FaMoon size={18} />\n      ) : (\n        <FaSun size={18} />\n      )}\n    </button>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAC9C,SAASC,QAAQ,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,KAAK,GAAG,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAEpD,IAAIS,SAAS,EAAE;IACb,OAAO,IAAI;EACb;EAEA,oBACEP,OAAA;IACEQ,OAAO,EAAEF,WAAY;IACrBJ,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAE;MACLM,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,eAAe,EAAE,gBAAgB;MACjCC,KAAK,EAAE,qBAAqB;MAC5BC,SAAS,EAAE,+BAA+B;MAC1CC,UAAU,EAAE,eAAe;MAC3BC,MAAM,EAAE,SAAS;MACjB,GAAGrB;IACL,CAAE;IACFsB,KAAK,EAAE,aAAapB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IAChE,cAAY,aAAaA,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,OAAQ;IACrEqB,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,YAAY;MACvCF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACmB,SAAS,GAAG,+BAA+B;IAC5D,CAAE;IACFQ,YAAY,EAAGH,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC0B,SAAS,GAAG,UAAU;MACrCF,CAAC,CAACC,MAAM,CAACzB,KAAK,CAACmB,SAAS,GAAG,+BAA+B;IAC5D,CAAE;IAAAS,QAAA,EAED1B,KAAK,KAAK,OAAO,gBAChBL,OAAA,CAACH,MAAM;MAACmC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBpC,OAAA,CAACJ,KAAK;MAACoC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAChC,EAAA,CAhDIH,WAAW;EAAA,QAC2BH,QAAQ;AAAA;AAAAuC,EAAA,GAD9CpC,WAAW;AAkDjB,eAAeA,WAAW;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}