import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge, Modal, Form, Table } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { reportsAPI, policiesAPI } from '../../services/api';
import { FaPlus, FaEye, FaFileAlt, FaCreditCard } from 'react-icons/fa';

const CustomerDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [customerData, setCustomerData] = useState({
    activePolicies: 0,
    pendingClaims: 0,
    totalClaims: 0,
    premiumDue: 0,
    nextPaymentDate: '',
    coverageAmount: 0,
    loyaltyPoints: 0,
    membershipLevel: 'Silver',
  });

  const [policies, setPolicies] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(false);

  // Policy application state
  const [showPolicyModal, setShowPolicyModal] = useState(false);
  const [policyForm, setPolicyForm] = useState({
    type: '',
    coverageAmount: '',
    description: ''
  });

  // Fetch real data from API
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await reportsAPI.getDashboardStats();
      if (response.success && response.data.overview) {
        const { overview, myData } = response.data;
        setCustomerData({
          activePolicies: overview.activePolicies || 0,
          pendingClaims: overview.pendingClaims || 0,
          totalClaims: overview.totalClaims || 0,
          premiumDue: overview.premiumDue || 0,
          nextPaymentDate: overview.nextPaymentDate || '',
          coverageAmount: overview.coverageAmount || 0,
          loyaltyPoints: overview.loyaltyPoints || 0,
          membershipLevel: overview.membershipLevel || 'Bronze',
        });

        if (myData) {
          setPolicies(myData.policies || []);
          setRecentActivity(myData.tickets || []);
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Fallback to existing dummy data
      fetchDummyData();
    } finally {
      setLoading(false);
    }
  };

  const fetchDummyData = () => {
    const fetchedData = {
      activePolicies: 0,
      pendingClaims: 0,
      totalClaims: 0,
      premiumDue: 0,
      nextPaymentDate: '',
      coverageAmount: 0,
      loyaltyPoints: 0,
      membershipLevel: 'Gold',
    };

    const fetchedPolicies = [
    
    ];

    const fetchedActivity = [
      
    ];

    setCustomerData(fetchedData);
    setPolicies(fetchedPolicies);
    setRecentActivity(fetchedActivity);
  };

  // Handle policy application
  const handlePolicyFormChange = (e) => {
    setPolicyForm({
      ...policyForm,
      [e.target.name]: e.target.value
    });
  };

  const handlePolicySubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await policiesAPI.createPolicy(policyForm);
      if (response.success) {
        setShowPolicyModal(false);
        setPolicyForm({ type: '', coverageAmount: '', description: '' });
        fetchDashboardData(); // Refresh data
        alert('Policy application submitted successfully!');
      }
    } catch (error) {
      console.error('Error submitting policy application:', error);
      alert('Error submitting policy application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const cardData = [
    { label: 'Active Policies', count: customerData.activePolicies, icon: 'bi-shield-check', color: 'primary' },
    { label: 'Pending Claims', count: customerData.pendingClaims, icon: 'bi-clock-history', color: 'warning' },
    { label: 'Total Coverage', count: `$${customerData.coverageAmount.toLocaleString()}`, icon: 'bi-umbrella', color: 'success' },
    { label: 'Loyalty Points', count: customerData.loyaltyPoints, icon: 'bi-star-fill', color: 'info' },
  ];

  const quickActions = [
    { title: 'Apply for Policy', description: 'Apply for new insurance policy', icon: 'bi-plus-circle', action: 'apply' },
    { title: 'Pay Premium', description: 'Make premium payments', icon: 'bi-credit-card', action: 'payment' },
    { title: 'File Claim', description: 'Submit new insurance claim', icon: 'bi-file-plus', action: 'claim' },
    { title: 'Contact Support', description: 'Get help from our team', icon: 'bi-headset', action: 'support' },
  ];

  const handleQuickAction = (action) => {
    switch (action) {
      case 'apply':
        setShowPolicyModal(true);
        break;
      case 'payment':
        // Navigate to payment page (you can create this later)
        alert('Payment functionality coming soon!');
        break;
      case 'claim':
        // Navigate to claims page
        navigate('/claims');
        break;
      case 'support':
        // Navigate to contact support page
        navigate('/contact-support');
        break;
      default:
        break;
    }
  };

  const getStatusBadge = (status) => {
    const variants = {
      'Active': 'success',
      'Pending': 'warning',
      'Expired': 'danger',
      'completed': 'success',
      'pending': 'warning'
    };
    return <Badge bg={variants[status]}>{status}</Badge>;
  };

  const getMembershipColor = (level) => {
    const colors = {
      'Bronze': 'warning',
      'Silver': 'secondary',
      'Gold': 'warning',
      'Platinum': 'dark'
    };
    return colors[level] || 'primary';
  };

  return (
    <Container className="mt-4">
      <div className="mb-4">
        <h2 className="fw-bold">Customer Dashboard</h2>
        <p className="lead">
          Welcome back, <span className="text-primary fw-semibold">{user.name}</span>! 
          Manage your insurance policies and claims.
        </p>
      </div>

      {/* Overview Cards */}
      <Row className="g-4 mb-5">
        {cardData.map((item, idx) => (
          <Col lg={3} md={6} key={idx}>
            <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>
              <Card.Body className="d-flex align-items-center justify-content-between">
                <div>
                  <h6 className="card-title mb-2">{item.label}</h6>
                  <h4 className="fw-bold">{item.count}</h4>
                </div>
                <i className={`bi ${item.icon} fs-2`}></i>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Membership Status and Premium Due */}
      <Row className="mb-4">
        <Col lg={4}>
          <Card className="shadow-sm h-100">
            <Card.Header>
              <h5 className="fw-bold mb-0">Membership Status</h5>
            </Card.Header>
            <Card.Body className="text-center">
              <i className={`bi bi-award text-${getMembershipColor(customerData.membershipLevel)} fs-1 mb-3`}></i>
              <h4 className={`text-${getMembershipColor(customerData.membershipLevel)}`}>
                {customerData.membershipLevel}
              </h4>
              <p className="text-muted">Member Level</p>
              <div className="mt-3">
                <small className="text-muted">Loyalty Points</small>
                <h5 className="text-primary">{customerData.loyaltyPoints}</h5>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={8}>
          <Card className="shadow-sm h-100">
            <Card.Header>
              <h5 className="fw-bold mb-0">Premium Due</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="text-center">
                    <h2 className="text-danger">${customerData.premiumDue}</h2>
                    <p className="text-muted">Total Amount Due</p>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="text-center">
                    <h5 className="text-primary">{customerData.nextPaymentDate}</h5>
                    <p className="text-muted">Next Payment Date</p>
                    <Button variant="primary" className="w-100">
                      Pay Now
                    </Button>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row className="mb-4">
        <Col>
          <h4 className="fw-bold mb-3">Quick Actions</h4>
        </Col>
      </Row>
      <Row className="g-4 mb-5">
        {quickActions.map((action, idx) => (
          <Col lg={3} md={6} key={idx}>
            <Card className="shadow-sm border-0 h-100">
              <Card.Body className="text-center">
                <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>
                <h6 className="fw-bold">{action.title}</h6>
                <p className="text-muted mb-3 small">{action.description}</p>
                <Button
                  variant="outline-primary"
                  size="sm"
                  className="w-100"
                  onClick={() => handleQuickAction(action.action)}
                >
                  {action.title}
                </Button>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Policies and Recent Activity */}
      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="fw-bold mb-0">My Policies</h5>
            </Card.Header>
            <Card.Body>
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead>
                    <tr>
                      <th>Policy ID</th>
                      <th>Type</th>
                      <th>Status</th>
                      <th>Premium</th>
                      <th>Coverage</th>
                      <th>Next Due</th>
                    </tr>
                  </thead>
                  <tbody>
                    {policies.map((policy) => (
                      <tr key={policy.id}>
                        <td>{policy.id}</td>
                        <td>{policy.type}</td>
                        <td>{getStatusBadge(policy.status)}</td>
                        <td>${policy.premium}</td>
                        <td>${policy.coverage.toLocaleString()}</td>
                        <td>{policy.nextDue}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={4}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="fw-bold mb-0">Recent Activity</h5>
            </Card.Header>
            <Card.Body>
              <div className="list-group list-group-flush">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="list-group-item">
                    <div className="d-flex justify-content-between align-items-start">
                      <div>
                        <h6 className="mb-1">{activity.action}</h6>
                        <small className="text-muted">{activity.description}</small>
                        <br />
                        <small className="text-muted">{activity.date}</small>
                      </div>
                      {getStatusBadge(activity.status)}
                    </div>
                  </div>
                ))}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Policy Application Modal */}
      <Modal show={showPolicyModal} onHide={() => setShowPolicyModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Apply for New Policy</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handlePolicySubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Policy Type</Form.Label>
                  <Form.Select
                    name="type"
                    value={policyForm.type}
                    onChange={handlePolicyFormChange}
                    required
                  >
                    <option value="">Select Policy Type</option>
                    <option value="life">Life Insurance</option>
                    <option value="health">Health Insurance</option>
                    <option value="auto">Auto Insurance</option>
                    <option value="home">Home Insurance</option>
                    <option value="travel">Travel Insurance</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Coverage Amount ($)</Form.Label>
                  <Form.Control
                    type="number"
                    name="coverageAmount"
                    value={policyForm.coverageAmount}
                    onChange={handlePolicyFormChange}
                    placeholder="Enter coverage amount"
                    min="1000"
                    step="1000"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Additional Information</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={policyForm.description}
                onChange={handlePolicyFormChange}
                placeholder="Provide any additional information about your insurance needs..."
              />
            </Form.Group>
            <div className="alert alert-info">
              <small>
                <strong>Note:</strong> This is a preliminary application. Our team will contact you within 24 hours to complete the process and provide a detailed quote.
              </small>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowPolicyModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? 'Submitting...' : 'Submit Application'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default CustomerDashboard;
