{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\ReportTool.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Form, But<PERSON>, Card } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportTool = () => {\n  _s();\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const handleGenerate = () => {\n    alert(`Generating report from ${startDate} to ${endDate}`);\n    // API call can go here\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-4\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"shadow-sm p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"mb-4 fw-bold text-uppercase\",\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Start Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"date\",\n              value: startDate,\n              onChange: e => setStartDate(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"End Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"date\",\n              value: endDate,\n              onChange: e => setEndDate(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: handleGenerate,\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportTool, \"tOzGTxBi4OgONuhYAP43LC7m7Tg=\");\n_c = ReportTool;\nexport default ReportTool;\nvar _c;\n$RefreshReg$(_c, \"ReportTool\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "Card", "jsxDEV", "_jsxDEV", "ReportTool", "_s", "startDate", "setStartDate", "endDate", "setEndDate", "handleGenerate", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "md", "Group", "Label", "Control", "type", "value", "onChange", "e", "target", "variant", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/ReportTool.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Container, Row, Col, Form, But<PERSON>, Card } from 'react-bootstrap';\r\n\r\nconst ReportTool = () => {\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n\r\n  const handleGenerate = () => {\r\n    alert(`Generating report from ${startDate} to ${endDate}`);\r\n    // API call can go here\r\n  };\r\n\r\n  return (\r\n    <Container className=\"mt-4\">\r\n      {/* Top Right User Display */}\r\n      \r\n\r\n      {/* Main Report Section */}\r\n      <Card className=\"shadow-sm p-4\">\r\n        <h4 className=\"mb-4 fw-bold text-uppercase\">Generate Report</h4>\r\n\r\n        <Row className=\"mb-3\">\r\n          <Col md={6}>\r\n            <Form.Group>\r\n              <Form.Label>Start Date</Form.Label>\r\n              <Form.Control\r\n                type=\"date\"\r\n                value={startDate}\r\n                onChange={(e) => setStartDate(e.target.value)}\r\n              />\r\n            </Form.Group>\r\n          </Col>\r\n\r\n          <Col md={6}>\r\n            <Form.Group>\r\n              <Form.Label>End Date</Form.Label>\r\n              <Form.Control\r\n                type=\"date\"\r\n                value={endDate}\r\n                onChange={(e) => setEndDate(e.target.value)}\r\n              />\r\n            </Form.Group>\r\n          </Col>\r\n        </Row>\r\n\r\n        <Button variant=\"primary\" onClick={handleGenerate}>\r\n          Generate Report\r\n        </Button>\r\n      </Card>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ReportTool;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3BC,KAAK,CAAC,0BAA0BL,SAAS,OAAOE,OAAO,EAAE,CAAC;IAC1D;EACF,CAAC;EAED,oBACEL,OAAA,CAACP,SAAS;IAACgB,SAAS,EAAC,MAAM;IAAAC,QAAA,eAKzBV,OAAA,CAACF,IAAI;MAACW,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BV,OAAA;QAAIS,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEhEd,OAAA,CAACN,GAAG;QAACe,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBV,OAAA,CAACL,GAAG;UAACoB,EAAE,EAAE,CAAE;UAAAL,QAAA,eACTV,OAAA,CAACJ,IAAI,CAACoB,KAAK;YAAAN,QAAA,gBACTV,OAAA,CAACJ,IAAI,CAACqB,KAAK;cAAAP,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnCd,OAAA,CAACJ,IAAI,CAACsB,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEjB,SAAU;cACjBkB,QAAQ,EAAGC,CAAC,IAAKlB,YAAY,CAACkB,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENd,OAAA,CAACL,GAAG;UAACoB,EAAE,EAAE,CAAE;UAAAL,QAAA,eACTV,OAAA,CAACJ,IAAI,CAACoB,KAAK;YAAAN,QAAA,gBACTV,OAAA,CAACJ,IAAI,CAACqB,KAAK;cAAAP,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACjCd,OAAA,CAACJ,IAAI,CAACsB,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEf,OAAQ;cACfgB,QAAQ,EAAGC,CAAC,IAAKhB,UAAU,CAACgB,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENd,OAAA,CAACH,MAAM;QAAC2B,OAAO,EAAC,SAAS;QAACC,OAAO,EAAElB,cAAe;QAAAG,QAAA,EAAC;MAEnD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACZ,EAAA,CAhDID,UAAU;AAAAyB,EAAA,GAAVzB,UAAU;AAkDhB,eAAeA,UAAU;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}