import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Modal, Form, Row, Col, Container, <PERSON>, Al<PERSON>, Spin<PERSON>, Badge } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport } from 'react-icons/fa';
import { policiesAPI, categoriesAPI, subCategoriesAPI } from '../services/api';

const InsurancePolicy = () => {
  const [policies, setPolicies] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'

  // Form state
  const [form, setForm] = useState({
    name: '',
    description: '',
    type: '',
    category: '',
    subCategory: '',
    premiumAmount: '',
    coverageAmount: '',
    duration: 12,
    terms: '',
    eligibilityCriteria: '',
    isActive: true
  });

  useEffect(() => {
    fetchPolicies();
    fetchCategories();
    fetchSubCategories();
  }, []);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      const response = await policiesAPI.getPolicies();
      if (response.success) {
        setPolicies(response.data.policies || []);
      } else {
        setError('Failed to fetch policies');
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
      setError('Failed to fetch policies');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesAPI.getCategories();
      if (response.success) {
        setCategories(response.data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchSubCategories = async () => {
    try {
      const response = await subCategoriesAPI.getSubCategories();
      if (response.success) {
        setSubCategories(response.data.subcategories || []);
      }
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    setError('');
  };

  const handleSave = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await policiesAPI.createPolicy(form);
      if (response.success) {
        setSuccess('Policy created successfully!');
        resetForm();
        setShowModal(false);
        fetchPolicies();
      } else {
        setError(response.message || 'Failed to create policy');
      }
    } catch (error) {
      console.error('Error creating policy:', error);
      setError('Failed to create policy');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await policiesAPI.updatePolicy(selectedPolicy._id, form);
      if (response.success) {
        setSuccess('Policy updated successfully!');
        setShowEditModal(false);
        setSelectedPolicy(null);
        fetchPolicies();
      } else {
        setError(response.message || 'Failed to update policy');
      }
    } catch (error) {
      console.error('Error updating policy:', error);
      setError('Failed to update policy');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (policyId) => {
    if (window.confirm('Are you sure you want to delete this policy?')) {
      try {
        const response = await policiesAPI.deletePolicy(policyId);
        if (response.success) {
          setSuccess('Policy deleted successfully!');
          fetchPolicies();
        } else {
          setError(response.message || 'Failed to delete policy');
        }
      } catch (error) {
        console.error('Error deleting policy:', error);
        setError('Failed to delete policy');
      }
    }
  };

  const resetForm = () => {
    setForm({
      name: '',
      description: '',
      type: '',
      category: '',
      subCategory: '',
      premiumAmount: '',
      coverageAmount: '',
      duration: 12,
      terms: '',
      eligibilityCriteria: '',
      isActive: true
    });
  };

  const openCreateModal = () => {
    resetForm();
    setModalMode('create');
    setShowModal(true);
  };

  const openEditModal = (policy) => {
    setSelectedPolicy(policy);
    setForm({
      name: policy.name,
      description: policy.description || '',
      type: policy.type,
      category: policy.category?._id || policy.category,
      subCategory: policy.subCategory?._id || policy.subCategory,
      premiumAmount: policy.premiumAmount,
      coverageAmount: policy.coverageAmount,
      duration: policy.duration,
      terms: policy.terms || '',
      eligibilityCriteria: policy.eligibilityCriteria || '',
      isActive: policy.isActive
    });
    setModalMode('edit');
    setShowEditModal(true);
  };

  const openViewModal = (policy) => {
    setSelectedPolicy(policy);
    setShowViewModal(true);
  };

  const filteredPolicies = policies.filter((policy) =>
    policy.name?.toLowerCase().includes(search.toLowerCase())
  );

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { variant: 'success', text: 'Active' },
      'inactive': { variant: 'secondary', text: 'Inactive' },
      'pending': { variant: 'warning', text: 'Pending' },
      'expired': { variant: 'danger', text: 'Expired' }
    };

    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Insurance Policies</h2>
              <p className="text-muted">Manage insurance policy templates</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="primary" onClick={openCreateModal}>
                <FaPlus className="me-2" />
                New Policy
              </Button>
              <Button variant="secondary">
                <FaFileImport className="me-2" />
                Import
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Row className="mb-3">
        <Col md={6}>
          <div className="position-relative">
            <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
            <Form.Control
              type="text"
              placeholder="Search policies..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="ps-5"
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading policies...</p>
                </div>
              ) : filteredPolicies.length === 0 ? (
                <div className="text-center py-4">
                  <FaPlus size={48} className="text-muted mb-3" />
                  <h5>No Policies Found</h5>
                  <p className="text-muted">
                    {search ? 'No policies match your search.' : 'Start by creating your first policy.'}
                  </p>
                  {!search && (
                    <Button variant="primary" onClick={openCreateModal}>
                      <FaPlus className="me-2" />
                      Create First Policy
                    </Button>
                  )}
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Policy Name</th>
                      <th>Type</th>
                      <th>Category</th>
                      <th>Premium</th>
                      <th>Coverage</th>
                      <th>Duration</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPolicies.map((policy) => (
                      <tr key={policy._id}>
                        <td>
                          <strong>{policy.name}</strong>
                          <br />
                          <small className="text-muted">{policy.description}</small>
                        </td>
                        <td>
                          <Badge bg="info" className="text-capitalize">
                            {policy.type}
                          </Badge>
                        </td>
                        <td>{policy.category?.name || 'N/A'}</td>
                        <td>{formatCurrency(policy.premiumAmount)}</td>
                        <td>{formatCurrency(policy.coverageAmount)}</td>
                        <td>{policy.duration} months</td>
                        <td>
                          {getStatusBadge(policy.isActive ? 'active' : 'inactive')}
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-info"
                              size="sm"
                              onClick={() => openViewModal(policy)}
                              title="View Policy"
                            >
                              <FaEye />
                            </Button>
                            <Button
                              variant="outline-warning"
                              size="sm"
                              onClick={() => openEditModal(policy)}
                              title="Edit Policy"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDelete(policy._id)}
                              title="Delete Policy"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Create Policy Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Create New Policy</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Policy Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={form.name}
                    onChange={handleChange}
                    placeholder="Enter policy name"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Policy Type *</Form.Label>
                  <Form.Select
                    name="type"
                    value={form.type}
                    onChange={handleChange}
                    required
                  >
                    <option value="">Select type...</option>
                    <option value="life">Life Insurance</option>
                    <option value="health">Health Insurance</option>
                    <option value="auto">Auto Insurance</option>
                    <option value="home">Home Insurance</option>
                    <option value="travel">Travel Insurance</option>
                    <option value="business">Business Insurance</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={form.description}
                onChange={handleChange}
                placeholder="Enter policy description"
              />
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Category</Form.Label>
                  <Form.Select
                    name="category"
                    value={form.category}
                    onChange={handleChange}
                  >
                    <option value="">Select category...</option>
                    {categories.map((category) => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Sub Category</Form.Label>
                  <Form.Select
                    name="subCategory"
                    value={form.subCategory}
                    onChange={handleChange}
                  >
                    <option value="">Select subcategory...</option>
                    {subCategories
                      .filter(sub => sub.category?._id === form.category)
                      .map((subCategory) => (
                        <option key={subCategory._id} value={subCategory._id}>
                          {subCategory.name}
                        </option>
                      ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Premium Amount (₹) *</Form.Label>
                  <Form.Control
                    type="number"
                    name="premiumAmount"
                    value={form.premiumAmount}
                    onChange={handleChange}
                    placeholder="Enter premium amount"
                    min="0"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Coverage Amount (₹) *</Form.Label>
                  <Form.Control
                    type="number"
                    name="coverageAmount"
                    value={form.coverageAmount}
                    onChange={handleChange}
                    placeholder="Enter coverage amount"
                    min="0"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Duration (months) *</Form.Label>
                  <Form.Control
                    type="number"
                    name="duration"
                    value={form.duration}
                    onChange={handleChange}
                    min="1"
                    max="120"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="isActive"
                    label="Active"
                    checked={form.isActive}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Terms & Conditions</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="terms"
                value={form.terms}
                onChange={handleChange}
                placeholder="Enter terms and conditions"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Eligibility Criteria</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="eligibilityCriteria"
                value={form.eligibilityCriteria}
                onChange={handleChange}
                placeholder="Enter eligibility criteria"
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Creating...
              </>
            ) : (
              'Create Policy'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Policy Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Policy</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {selectedPolicy && (
            <Form>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Policy Name *</Form.Label>
                    <Form.Control
                      type="text"
                      name="name"
                      value={form.name}
                      onChange={handleChange}
                      placeholder="Enter policy name"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Policy Type *</Form.Label>
                    <Form.Select
                      name="type"
                      value={form.type}
                      onChange={handleChange}
                      required
                    >
                      <option value="">Select type...</option>
                      <option value="life">Life Insurance</option>
                      <option value="health">Health Insurance</option>
                      <option value="auto">Auto Insurance</option>
                      <option value="home">Home Insurance</option>
                      <option value="travel">Travel Insurance</option>
                      <option value="business">Business Insurance</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  placeholder="Enter policy description"
                />
              </Form.Group>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Premium Amount (₹) *</Form.Label>
                    <Form.Control
                      type="number"
                      name="premiumAmount"
                      value={form.premiumAmount}
                      onChange={handleChange}
                      placeholder="Enter premium amount"
                      min="0"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Coverage Amount (₹) *</Form.Label>
                    <Form.Control
                      type="number"
                      name="coverageAmount"
                      value={form.coverageAmount}
                      onChange={handleChange}
                      placeholder="Enter coverage amount"
                      min="0"
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Duration (months) *</Form.Label>
                    <Form.Control
                      type="number"
                      name="duration"
                      value={form.duration}
                      onChange={handleChange}
                      min="1"
                      max="120"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      name="isActive"
                      label="Active"
                      checked={form.isActive}
                      onChange={handleChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleEdit}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              'Update Policy'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Policy Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Policy Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedPolicy && (
            <div>
              <Row>
                <Col md={6}>
                  <h6>Basic Information</h6>
                  <p><strong>Name:</strong> {selectedPolicy.name}</p>
                  <p><strong>Type:</strong> {selectedPolicy.type}</p>
                  <p><strong>Description:</strong> {selectedPolicy.description || 'N/A'}</p>
                  <p><strong>Status:</strong> {getStatusBadge(selectedPolicy.isActive ? 'active' : 'inactive')}</p>
                </Col>
                <Col md={6}>
                  <h6>Financial Details</h6>
                  <p><strong>Premium:</strong> {formatCurrency(selectedPolicy.premiumAmount)}</p>
                  <p><strong>Coverage:</strong> {formatCurrency(selectedPolicy.coverageAmount)}</p>
                  <p><strong>Duration:</strong> {selectedPolicy.duration} months</p>
                  <p><strong>Created:</strong> {new Date(selectedPolicy.createdAt).toLocaleDateString()}</p>
                </Col>
              </Row>

              {selectedPolicy.terms && (
                <div className="mt-3">
                  <h6>Terms & Conditions</h6>
                  <p>{selectedPolicy.terms}</p>
                </div>
              )}

              {selectedPolicy.eligibilityCriteria && (
                <div className="mt-3">
                  <h6>Eligibility Criteria</h6>
                  <p>{selectedPolicy.eligibilityCriteria}</p>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default InsurancePolicy;
