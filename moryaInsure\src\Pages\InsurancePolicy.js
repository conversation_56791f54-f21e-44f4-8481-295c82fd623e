import React, { useState } from 'react';
import { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';
import { FaEdit, FaTrash } from 'react-icons/fa';

const InsurancePolicy = () => {
  const [showModal, setShowModal] = useState(false);
  const [search, setSearch] = useState('');

  const [policies, setPolicies] = useState([
    {
      id: 1,
      name: 'AutoSecure Collision Plan',
      category: 'Auto Insurance',
      subCategory: 'Comprehensive Coverage',
      sumAssured: '80000',
      premium: '8000',
      tenure: '36',
      status: 'Active'
    },
    {
      id: 2,
      name: 'FamilyShield Term Plan',
      category: 'Life Insurance',
      subCategory: 'Term Life Insurance',
      sumAssured: '150000',
      premium: '20000',
      tenure: '24',
      status: 'Pending'
    }
  ]);

  const [form, setForm] = useState({
    name: '',
    category: '',
    subCategory: '',
    sumAssured: '',
    premium: '',
    tenure: ''
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    const newPolicy = {
      ...form,
      id: policies.length + 1,
      status: 'Active'
    };
    setPolicies([...policies, newPolicy]);
    setShowModal(false);
    setForm({ name: '', category: '', subCategory: '', sumAssured: '', premium: '', tenure: '' });
  };

  const filteredPolicies = policies.filter((p) =>
    p.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="container-fluid p-3">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h4 className="fw-bold text-uppercase">Insurance Policy</h4>
        <Button variant="primary" onClick={() => setShowModal(true)}>+ New</Button>
      </div>

      <div className="mb-3 d-flex flex-wrap gap-2">
        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      <div className="mb-3">
        <Form.Control
          type="text"
          placeholder="Search policies..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <Table bordered hover responsive className="shadow-sm">
        <thead className="table-primary">
          <tr>
            <th>Name</th>
            <th>Category</th>
            <th>Sub Category</th>
            <th>Sum Assured</th>
            <th>Premium</th>
            <th>Tenure</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filteredPolicies.map((p) => (
            <tr key={p.id}>
              <td>{p.name}</td>
              <td>{p.category}</td>
              <td>{p.subCategory}</td>
              <td>{p.sumAssured} PHP</td>
              <td>{p.premium} PHP</td>
              <td>{p.tenure} months</td>
              <td><span className={`badge bg-${p.status === 'Active' ? 'success' : 'warning'}`}>{p.status}</span></td>
              <td>
                <Button variant="primary" size="sm" className="me-2"><FaEdit /></Button>
                <Button variant="danger" size="sm"><FaTrash /></Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>New Insurance Policy</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Policy Name</Form.Label>
              <Form.Control
                name="name"
                value={form.name}
                onChange={handleChange}
                placeholder="Enter Policy Name"
              />
            </Form.Group>

            <Row className="mb-3">
              <Col>
                <Form.Label>Category</Form.Label>
                <Form.Select name="category" value={form.category} onChange={handleChange}>
                  <option value="">Select</option>
                  <option>Auto Insurance</option>
                  <option>Life Insurance</option>
                  <option>Travel Insurance</option>
                </Form.Select>
              </Col>
              <Col>
                <Form.Label>Sub Category</Form.Label>
                <Form.Select name="subCategory" value={form.subCategory} onChange={handleChange}>
                  <option value="">Select</option>
                  <option>Comprehensive Coverage</option>
                  <option>Term Life Insurance</option>
                  <option>Travel Cancellation Insurance</option>
                </Form.Select>
              </Col>
            </Row>

            <Row className="mb-3">
              <Col>
                <Form.Label>Sum Assured (PHP)</Form.Label>
                <Form.Control
                  type="number"
                  name="sumAssured"
                  value={form.sumAssured}
                  onChange={handleChange}
                />
              </Col>
              <Col>
                <Form.Label>Premium (PHP)</Form.Label>
                <Form.Control
                  type="number"
                  name="premium"
                  value={form.premium}
                  onChange={handleChange}
                />
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Tenure (months)</Form.Label>
              <Form.Control
                type="number"
                name="tenure"
                value={form.tenure}
                onChange={handleChange}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
          <Button variant="primary" onClick={handleSave}>Save Changes</Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default InsurancePolicy;
