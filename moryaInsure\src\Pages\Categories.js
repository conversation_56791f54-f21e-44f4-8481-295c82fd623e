import React, { useEffect, useState } from 'react';
import { Button, Table, Form, Modal } from 'react-bootstrap';
import { FaUser, FaEdit, FaTrash } from 'react-icons/fa';

const CategoriesPage = () => {
  const [username] = useState('');
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');

  // New category modal states
  const [showNewModal, setShowNewModal] = useState(false);
  const [newCategory, setNewCategory] = useState({
    categoryName: '',
    status: 'Active',
  });

  // Import modal states
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);

  useEffect(() => {
    const dummyData = [
    //   { id: 1, categoryName: 'Health Insurance', status: 'Active' },
    //   { id: 2, categoryName: 'Car Insurance', status: 'Active' },
    //   { id: 3, categoryName: 'Life Insurance', status: 'Active' },
    //   { id: 4, categoryName: 'Property Insurance', status: 'Active' },
     ];
    setCategories(dummyData);
  }, []);

  const filteredCategories = categories.filter((cat) =>
    cat.categoryName.toLowerCase().includes(search.toLowerCase())
  );

  // Handlers for new category modal
  const handleNewInputChange = (e) => {
    const { name, value } = e.target;
    setNewCategory((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddCategory = () => {
    const newEntry = {
      id: categories.length + 1,
      ...newCategory,
    };
    setCategories([...categories, newEntry]);
    setNewCategory({ categoryName: '', status: 'Active' });
    setShowNewModal(false);
  };

  // Handlers for import modal
  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const handleFileUpload = () => {
    if (selectedFile) {
      alert(`Uploaded file: ${selectedFile.name}`);
      setSelectedFile(null);
      setShowImportModal(false);
    } else {
      alert('Please select a file first.');
    }
  };

  return (
    <div className="container-fluid p-3">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h3 className="fw-bold text-uppercase">Insurance Category</h3>
      </div>

      {/* Action Buttons */}
      <div className="mb-3 d-flex gap-2">
        <Button variant="primary" onClick={() => setShowNewModal(true)}>New</Button>
        <Button variant="secondary" onClick={() => setShowImportModal(true)}>Import</Button>
      </div>

      {/* Export Buttons */}
      <div className="mb-3 d-flex flex-wrap gap-2">
        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      {/* Search */}
      <div className="mb-3 w-100 w-md-50">
        <Form.Control
          type="text"
          placeholder="Search categories..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {/* Table */}
      <Table bordered hover responsive className="shadow-sm">
        <thead className="table-primary">
          <tr>
            <th>Category Name</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filteredCategories.map((cat) => (
            <tr key={cat.id}>
              <td>{cat.categoryName}</td>
              <td><span className="badge bg-success">{cat.status}</span></td>
              <td>
                <Button variant="primary" size="sm" className="me-2">
                  <FaEdit />
                </Button>
                <Button variant="danger" size="sm">
                  <FaTrash />
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      {/* Modal - New Category */}
      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Add New Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Category Name</Form.Label>
              <Form.Control
                type="text"
                name="categoryName"
                placeholder="Enter category name"
                value={newCategory.categoryName}
                onChange={handleNewInputChange}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Status</Form.Label>
              <Form.Select
                name="status"
                value={newCategory.status}
                onChange={handleNewInputChange}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </Form.Select>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNewModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleAddCategory}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal - Import File */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Import Categories</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group>
              <Form.Label>Select file (CSV, Excel)</Form.Label>
              <Form.Control type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" onChange={handleFileChange} />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleFileUpload}>
            Upload
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default CategoriesPage;
