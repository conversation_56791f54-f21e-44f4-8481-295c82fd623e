import React, { useEffect, useState } from 'react';
import { But<PERSON>, Table, Form, Modal, <PERSON><PERSON>, Spinner, Container, Row, Col, Card } from 'react-bootstrap';
import { FaPlus, FaEdit, FaTrash, FaFileImport, FaSearch } from 'react-icons/fa';
import { categoriesAPI } from '../services/api';

const CategoriesPage = () => {
  const [categories, setCategories] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // New category modal states
  const [showNewModal, setShowNewModal] = useState(false);
  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    type: 'insurance',
    isActive: true,
  });

  // Edit modal states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editCategory, setEditCategory] = useState(null);

  // Import modal states
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await categoriesAPI.getCategories();
      if (response.success) {
        setCategories(response.data.categories || []);
      } else {
        setError('Failed to fetch categories');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError('Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  };

  const filteredCategories = categories.filter((cat) =>
    cat.name.toLowerCase().includes(search.toLowerCase())
  );

  // Handlers for new category modal
  const handleNewInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewCategory((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddCategory = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await categoriesAPI.createCategory(newCategory);
      if (response.success) {
        setSuccess('Category created successfully!');
        setNewCategory({ name: '', description: '', type: 'insurance', isActive: true });
        setShowNewModal(false);
        fetchCategories(); // Refresh the list
      } else {
        setError(response.message || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      setError('Failed to create category');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditCategory = async () => {
    try {
      setSubmitting(true);
      setError('');

      const response = await categoriesAPI.updateCategory(editCategory._id, editCategory);
      if (response.success) {
        setSuccess('Category updated successfully!');
        setShowEditModal(false);
        setEditCategory(null);
        fetchCategories(); // Refresh the list
      } else {
        setError(response.message || 'Failed to update category');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      setError('Failed to update category');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        const response = await categoriesAPI.deleteCategory(categoryId);
        if (response.success) {
          setSuccess('Category deleted successfully!');
          fetchCategories(); // Refresh the list
        } else {
          setError(response.message || 'Failed to delete category');
        }
      } catch (error) {
        console.error('Error deleting category:', error);
        setError('Failed to delete category');
      }
    }
  };

  const openEditModal = (category) => {
    setEditCategory({ ...category });
    setShowEditModal(true);
  };

  // Handlers for import modal
  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const handleFileUpload = () => {
    if (selectedFile) {
      alert(`Uploaded file: ${selectedFile.name}`);
      setSelectedFile(null);
      setShowImportModal(false);
    } else {
      alert('Please select a file first.');
    }
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">Categories Management</h2>
              <p className="text-muted">Manage insurance categories</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="primary" onClick={() => setShowNewModal(true)}>
                <FaPlus className="me-2" />
                New Category
              </Button>
              <Button variant="secondary" onClick={() => setShowImportModal(true)}>
                <FaFileImport className="me-2" />
                Import
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Row className="mb-3">
        <Col md={6}>
          <div className="position-relative">
            <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
            <Form.Control
              type="text"
              placeholder="Search categories..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="ps-5"
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading categories...</p>
                </div>
              ) : filteredCategories.length === 0 ? (
                <div className="text-center py-4">
                  <FaPlus size={48} className="text-muted mb-3" />
                  <h5>No Categories Found</h5>
                  <p className="text-muted">
                    {search ? 'No categories match your search.' : 'Start by creating your first category.'}
                  </p>
                  {!search && (
                    <Button variant="primary" onClick={() => setShowNewModal(true)}>
                      <FaPlus className="me-2" />
                      Create First Category
                    </Button>
                  )}
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Description</th>
                      <th>Type</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCategories.map((category) => (
                      <tr key={category._id}>
                        <td>
                          <strong>{category.name}</strong>
                        </td>
                        <td>{category.description || '-'}</td>
                        <td>
                          <span className="badge bg-info text-capitalize">
                            {category.type}
                          </span>
                        </td>
                        <td>
                          <span className={`badge ${category.isActive ? 'bg-success' : 'bg-secondary'}`}>
                            {category.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td>{new Date(category.createdAt).toLocaleDateString()}</td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-warning"
                              size="sm"
                              onClick={() => openEditModal(category)}
                              title="Edit Category"
                            >
                              <FaEdit />
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDeleteCategory(category._id)}
                              title="Delete Category"
                            >
                              <FaTrash />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Modal - New Category */}
      <Modal show={showNewModal} onHide={() => setShowNewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add New Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Category Name *</Form.Label>
              <Form.Control
                type="text"
                name="name"
                placeholder="Enter category name"
                value={newCategory.name}
                onChange={handleNewInputChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                placeholder="Enter category description"
                value={newCategory.description}
                onChange={handleNewInputChange}
              />
            </Form.Group>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Type</Form.Label>
                  <Form.Select
                    name="type"
                    value={newCategory.type}
                    onChange={handleNewInputChange}
                  >
                    <option value="insurance">Insurance</option>
                    <option value="policy">Policy</option>
                    <option value="claim">Claim</option>
                    <option value="general">General</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="isActive"
                    label="Active"
                    checked={newCategory.isActive}
                    onChange={handleNewInputChange}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNewModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddCategory}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Creating...
              </>
            ) : (
              'Create Category'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal - Edit Category */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          {editCategory && (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Category Name *</Form.Label>
                <Form.Control
                  type="text"
                  name="name"
                  placeholder="Enter category name"
                  value={editCategory.name}
                  onChange={(e) => setEditCategory({...editCategory, name: e.target.value})}
                  required
                />
              </Form.Group>
              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  placeholder="Enter category description"
                  value={editCategory.description || ''}
                  onChange={(e) => setEditCategory({...editCategory, description: e.target.value})}
                />
              </Form.Group>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Type</Form.Label>
                    <Form.Select
                      name="type"
                      value={editCategory.type}
                      onChange={(e) => setEditCategory({...editCategory, type: e.target.value})}
                    >
                      <option value="insurance">Insurance</option>
                      <option value="policy">Policy</option>
                      <option value="claim">Claim</option>
                      <option value="general">General</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      name="isActive"
                      label="Active"
                      checked={editCategory.isActive}
                      onChange={(e) => setEditCategory({...editCategory, isActive: e.target.checked})}
                    />
                  </Form.Group>
                </Col>
              </Row>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleEditCategory}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              'Update Category'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default CategoriesPage;
