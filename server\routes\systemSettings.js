const express = require('express');
const { body, validationResult } = require('express-validator');
const SystemSettings = require('../models/SystemSettings');
const { authenticate, adminOnly } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/system-settings
// @desc    Get system settings
// @access  Private (Admin only)
router.get('/', authenticate, adminOnly, async (req, res) => {
  try {
    const settings = await SystemSettings.getSettings();
    
    res.json({
      success: true,
      data: { settings }
    });
  } catch (error) {
    console.error('Get system settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching system settings'
    });
  }
});

// @route   PUT /api/system-settings
// @desc    Update system settings
// @access  Private (Admin only)
router.put('/', authenticate, adminOnly, [
  body('organizationName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Organization name must be between 1 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .matches(/^\+?[\d\s-()]+$/)
    .withMessage('Please provide a valid phone number')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const updates = req.body;
    const settings = await SystemSettings.updateSettings(updates, req.user._id);
    
    res.json({
      success: true,
      message: 'System settings updated successfully',
      data: { settings }
    });
  } catch (error) {
    console.error('Update system settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating system settings'
    });
  }
});

// @route   GET /api/system-settings/:category
// @desc    Get specific settings category
// @access  Private (Admin only)
router.get('/:category', authenticate, adminOnly, async (req, res) => {
  try {
    const { category } = req.params;
    const settings = await SystemSettings.getSettings();
    const categoryData = settings.getCategory(category);
    
    if (!categoryData || Object.keys(categoryData).length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Settings category not found'
      });
    }
    
    res.json({
      success: true,
      data: { [category]: categoryData }
    });
  } catch (error) {
    console.error('Get settings category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching settings category'
    });
  }
});

// @route   PUT /api/system-settings/:category
// @desc    Update specific settings category
// @access  Private (Admin only)
router.put('/:category', authenticate, adminOnly, async (req, res) => {
  try {
    const { category } = req.params;
    const updates = req.body;
    
    const settings = await SystemSettings.getSettings();
    await settings.updateCategory(category, updates, req.user._id);
    
    res.json({
      success: true,
      message: `${category} settings updated successfully`,
      data: { [category]: settings.getCategory(category) }
    });
  } catch (error) {
    console.error('Update settings category error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating settings category'
    });
  }
});

// @route   POST /api/system-settings/reset
// @desc    Reset settings to default
// @access  Private (Admin only)
router.post('/reset', authenticate, adminOnly, async (req, res) => {
  try {
    // Delete existing settings and create new default ones
    await SystemSettings.deleteMany({});
    const settings = await SystemSettings.getSettings();
    
    res.json({
      success: true,
      message: 'System settings reset to default values',
      data: { settings }
    });
  } catch (error) {
    console.error('Reset system settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while resetting system settings'
    });
  }
});

// @route   GET /api/system-settings/backup/export
// @desc    Export settings as JSON
// @access  Private (Admin only)
router.get('/backup/export', authenticate, adminOnly, async (req, res) => {
  try {
    const settings = await SystemSettings.getSettings();
    
    // Remove sensitive data before export
    const exportData = settings.toObject();
    delete exportData._id;
    delete exportData.__v;
    delete exportData.createdAt;
    delete exportData.updatedAt;
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=system-settings-backup.json');
    res.json(exportData);
  } catch (error) {
    console.error('Export settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while exporting settings'
    });
  }
});

// @route   POST /api/system-settings/backup/import
// @desc    Import settings from JSON
// @access  Private (Admin only)
router.post('/backup/import', authenticate, adminOnly, async (req, res) => {
  try {
    const importData = req.body;
    
    // Validate import data structure
    if (!importData || typeof importData !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Invalid import data format'
      });
    }
    
    const settings = await SystemSettings.updateSettings(importData, req.user._id);
    
    res.json({
      success: true,
      message: 'Settings imported successfully',
      data: { settings }
    });
  } catch (error) {
    console.error('Import settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while importing settings'
    });
  }
});

module.exports = router;
