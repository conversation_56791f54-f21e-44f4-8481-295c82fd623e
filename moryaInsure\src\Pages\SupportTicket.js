import React, { useState } from 'react';
import { Table, Button, Modal, Form, Row, Col } from 'react-bootstrap';
import { FaEdit, FaTrash } from 'react-icons/fa';

const SupportTickets = () => {
  const [tickets, setTickets] = useState([
    {
      id: 101,
      customerName: '<PERSON>',
      contact: '9876543210',
      subject: 'Login Failure',
      description: 'Unable to login to the portal',
      category: 'Technical Issue',
      openDate: '2025-06-22',
      status: 'Open',
      staff: '<PERSON><PERSON>',
      remark: 'Checking login error logs'
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [form, setForm] = useState({
    customerName: '',
    contact: '',
    subject: '',
    description: '',
    category: '',
    openDate: '',
    status: 'Open',
    staff: '',
    remark: ''
  });
  const [search, setSearch] = useState('');

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    setTickets([...tickets, { ...form, id: tickets.length + 101 }]);
    setShowModal(false);
    setForm({
      customerName: '', contact: '', subject: '', description: '', category: '', openDate: '',
      status: 'Open', staff: '', remark: ''
    });
  };

  const filtered = tickets.filter(t =>
    t.customerName.toLowerCase().includes(search.toLowerCase()) ||
    t.subject.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="container-fluid p-3">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h4 className="fw-bold text-uppercase">Support Tickets</h4>
        <Button onClick={() => setShowModal(true)}>+ New</Button>
      </div>

      <div className="mb-2 d-flex flex-wrap gap-2">
        <Button variant="outline-secondary" size="sm">Copy</Button>
        <Button variant="outline-secondary" size="sm">CSV</Button>
        <Button variant="outline-secondary" size="sm">Excel</Button>
        <Button variant="outline-secondary" size="sm">PDF</Button>
        <Button variant="outline-secondary" size="sm">Print</Button>
      </div>

      <div className="mb-3 d-flex justify-content-between">
        <Form.Select style={{ width: '80px' }}>
          <option>10</option>
          <option>25</option>
          <option>50</option>
        </Form.Select>
        <Form.Control
          type="text"
          style={{ width: '300px' }}
          placeholder="Search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <Table bordered hover responsive>
        <thead className="table-primary">
          <tr>
            <th>Ticket ID</th>
            <th>Customer Name</th>
            <th>Customer Contact</th>
            <th>Ticket Subject</th>
            <th>Ticket Description</th>
            <th>Ticket Category</th>
            <th>Open Date</th>
            <th>Status</th>
            <th>Assign Staff</th>
            <th>Remark</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {filtered.length === 0 ? (
            <tr><td colSpan="11" className="text-center">No data available in database</td></tr>
          ) : (
            filtered.map(ticket => (
              <tr key={ticket.id}>
                <td>{ticket.id}</td>
                <td>{ticket.customerName}</td>
                <td>{ticket.contact}</td>
                <td>{ticket.subject}</td>
                <td>{ticket.description}</td>
                <td>{ticket.category}</td>
                <td>{ticket.openDate}</td>
                <td><span className={`badge bg-${ticket.status === 'Open' ? 'success' : 'warning'}`}>{ticket.status}</span></td>
                <td>{ticket.staff}</td>
                <td>{ticket.remark}</td>
                <td>
                  <Button size="sm" className="me-2"><FaEdit /></Button>
                  <Button variant="danger" size="sm"><FaTrash /></Button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </Table>

      {/* Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>New Support Ticket</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row className="mb-2">
              <Col><Form.Label>Customer Name</Form.Label>
              <Form.Control name="customerName" value={form.customerName} onChange={handleChange} /></Col>
              <Col><Form.Label>Contact</Form.Label>
              <Form.Control name="contact" value={form.contact} onChange={handleChange} /></Col>
            </Row>

            <Form.Group className="mb-2">
              <Form.Label>Subject</Form.Label>
              <Form.Control name="subject" value={form.subject} onChange={handleChange} />
            </Form.Group>

            <Form.Group className="mb-2">
              <Form.Label>Description</Form.Label>
              <Form.Control name="description" value={form.description} onChange={handleChange} as="textarea" rows={2} />
            </Form.Group>

            <Row className="mb-2">
              <Col>
                <Form.Label>Category</Form.Label>
                <Form.Select name="category" value={form.category} onChange={handleChange}>
                  <option value="">Select</option>
                  <option>Technical Issue</option>
                  <option>Policy Request</option>
                  <option>Account Issue</option>
                </Form.Select>
              </Col>
              <Col>
                <Form.Label>Open Date</Form.Label>
                <Form.Control type="date" name="openDate" value={form.openDate} onChange={handleChange} />
              </Col>
            </Row>

            <Row className="mb-2">
              <Col>
                <Form.Label>Assign Staff</Form.Label>
                <Form.Control name="staff" value={form.staff} onChange={handleChange} />
              </Col>
              <Col>
                <Form.Label>Remark</Form.Label>
                <Form.Control name="remark" value={form.remark} onChange={handleChange} />
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Status</Form.Label>
              <Form.Select name="status" value={form.status} onChange={handleChange}>
                <option>Open</option>
                <option>Pending</option>
                <option>Closed</option>
              </Form.Select>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>Close</Button>
          <Button variant="primary" onClick={handleSave}>Save Changes</Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SupportTickets;
