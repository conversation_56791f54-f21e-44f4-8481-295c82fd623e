import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Table, Badge, Modal, Form, Alert, Spinner, <PERSON><PERSON>, Tab, ProgressBar } from 'react-bootstrap';
import { FaEye, FaPlay, FaPause, FaCheck, FaComment, FaClock, FaCalendarAlt, FaExclamationTriangle, FaUser } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { tasksAPI } from '../services/api';

const MyTasks = () => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [modalMode, setModalMode] = useState('view'); // 'view', 'update', 'comment', 'time'
  const [activeTab, setActiveTab] = useState('pending');
  const [formData, setFormData] = useState({
    status: '',
    progress: 0,
    completionNotes: '',
    comment: '',
    startTime: '',
    endTime: '',
    timeDescription: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [taskStats, setTaskStats] = useState({});

  useEffect(() => {
    fetchMyTasks();
    fetchTaskStats();
  }, [activeTab]);

  const fetchMyTasks = async () => {
    try {
      setLoading(true);
      const params = activeTab !== 'all' ? { status: activeTab } : {};
      const response = await tasksAPI.getMyTasks(params);
      
      if (response.success) {
        setTasks(response.data.tasks || []);
      }
    } catch (error) {
      console.error('Error fetching my tasks:', error);
      setError('Failed to fetch your tasks');
    } finally {
      setLoading(false);
    }
  };

  const fetchTaskStats = async () => {
    try {
      const response = await tasksAPI.getTaskStats();
      if (response.success) {
        setTaskStats(response.data.stats || {});
      }
    } catch (error) {
      console.error('Error fetching task stats:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleUpdateTask = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const updateData = {
        status: formData.status,
        progress: parseInt(formData.progress),
        completionNotes: formData.completionNotes
      };

      const response = await tasksAPI.updateTask(selectedTask._id, updateData);
      
      if (response.success) {
        setSuccess('Task updated successfully!');
        setShowModal(false);
        fetchMyTasks();
        fetchTaskStats();
      } else {
        setError(response.message || 'Failed to update task');
      }
    } catch (error) {
      console.error('Error updating task:', error);
      setError('Failed to update task. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const response = await tasksAPI.addComment(selectedTask._id, {
        content: formData.comment
      });
      
      if (response.success) {
        setSuccess('Comment added successfully!');
        setFormData(prev => ({ ...prev, comment: '' }));
        // Refresh task details
        const taskResponse = await tasksAPI.getTaskById(selectedTask._id);
        if (taskResponse.success) {
          setSelectedTask(taskResponse.data.task);
        }
      } else {
        setError(response.message || 'Failed to add comment');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      setError('Failed to add comment. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleLogTime = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const response = await tasksAPI.logTime(selectedTask._id, {
        startTime: new Date(formData.startTime).toISOString(),
        endTime: new Date(formData.endTime).toISOString(),
        description: formData.timeDescription
      });
      
      if (response.success) {
        setSuccess('Time logged successfully!');
        setFormData(prev => ({ 
          ...prev, 
          startTime: '', 
          endTime: '', 
          timeDescription: '' 
        }));
        // Refresh task details
        const taskResponse = await tasksAPI.getTaskById(selectedTask._id);
        if (taskResponse.success) {
          setSelectedTask(taskResponse.data.task);
        }
      } else {
        setError(response.message || 'Failed to log time');
      }
    } catch (error) {
      console.error('Error logging time:', error);
      setError('Failed to log time. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const openViewModal = async (task) => {
    try {
      const response = await tasksAPI.getTaskById(task._id);
      if (response.success) {
        setSelectedTask(response.data.task);
        setModalMode('view');
        setShowModal(true);
      }
    } catch (error) {
      console.error('Error fetching task details:', error);
      setError('Failed to fetch task details');
    }
  };

  const openUpdateModal = (task) => {
    setSelectedTask(task);
    setFormData({
      status: task.status,
      progress: task.progress,
      completionNotes: task.completionNotes || '',
      comment: '',
      startTime: '',
      endTime: '',
      timeDescription: ''
    });
    setModalMode('update');
    setShowModal(true);
  };

  const openCommentModal = (task) => {
    setSelectedTask(task);
    setFormData(prev => ({ ...prev, comment: '' }));
    setModalMode('comment');
    setShowModal(true);
  };

  const openTimeModal = (task) => {
    setSelectedTask(task);
    setFormData(prev => ({ 
      ...prev, 
      startTime: '', 
      endTime: '', 
      timeDescription: '' 
    }));
    setModalMode('time');
    setShowModal(true);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'pending': { variant: 'warning', text: 'Pending' },
      'in_progress': { variant: 'info', text: 'In Progress' },
      'completed': { variant: 'success', text: 'Completed' },
      'cancelled': { variant: 'secondary', text: 'Cancelled' },
      'on_hold': { variant: 'dark', text: 'On Hold' }
    };
    
    const config = statusConfig[status] || { variant: 'secondary', text: status };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'low': { variant: 'success', text: 'Low' },
      'medium': { variant: 'warning', text: 'Medium' },
      'high': { variant: 'danger', text: 'High' },
      'urgent': { variant: 'dark', text: 'Urgent' }
    };
    
    const config = priorityConfig[priority] || { variant: 'secondary', text: priority };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('en-IN');
  };

  const isOverdue = (dueDate, status) => {
    return new Date(dueDate) < new Date() && status !== 'completed' && status !== 'cancelled';
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-1">My Tasks</h2>
              <p className="text-muted">View and manage your assigned tasks</p>
            </div>
          </div>
        </Col>
      </Row>

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Task Statistics */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-warning">{taskStats.pending || 0}</h4>
              <small>Pending Tasks</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-info">{taskStats.in_progress || 0}</h4>
              <small>In Progress</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-success">{taskStats.completed || 0}</h4>
              <small>Completed</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h4 className="text-dark">{taskStats.on_hold || 0}</h4>
              <small>On Hold</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Task Tabs */}
      <Row>
        <Col>
          <Card>
            <Card.Header>
              <Tabs activeKey={activeTab} onSelect={setActiveTab}>
                <Tab eventKey="pending" title="Pending" />
                <Tab eventKey="in_progress" title="In Progress" />
                <Tab eventKey="completed" title="Completed" />
                <Tab eventKey="all" title="All Tasks" />
              </Tabs>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center py-4">
                  <Spinner animation="border" />
                  <p className="mt-2">Loading your tasks...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-4">
                  <FaUser size={48} className="text-muted mb-3" />
                  <h5>No Tasks Found</h5>
                  <p className="text-muted">You don't have any tasks in this category.</p>
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Task</th>
                      <th>Type</th>
                      <th>Priority</th>
                      <th>Status</th>
                      <th>Due Date</th>
                      <th>Progress</th>
                      <th>Assigned By</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tasks.map((task) => (
                      <tr key={task._id} className={isOverdue(task.dueDate, task.status) ? 'table-danger' : ''}>
                        <td>
                          <div>
                            <strong>{task.title}</strong>
                            {isOverdue(task.dueDate, task.status) && (
                              <FaExclamationTriangle className="text-danger ms-2" title="Overdue" />
                            )}
                          </div>
                          <small className="text-muted">{task.description.substring(0, 50)}...</small>
                        </td>
                        <td>
                          <Badge bg="light" text="dark">
                            {task.type.replace('_', ' ')}
                          </Badge>
                        </td>
                        <td>{getPriorityBadge(task.priority)}</td>
                        <td>{getStatusBadge(task.status)}</td>
                        <td>
                          <FaCalendarAlt className="me-1 text-muted" />
                          {formatDate(task.dueDate)}
                        </td>
                        <td>
                          <ProgressBar 
                            now={task.progress} 
                            label={`${task.progress}%`}
                            style={{ height: '20px' }}
                          />
                        </td>
                        <td>
                          <div>
                            <FaUser className="me-1" />
                            {task.assignedBy.firstName} {task.assignedBy.lastName}
                          </div>
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <Button
                              variant="outline-info"
                              size="sm"
                              onClick={() => openViewModal(task)}
                              title="View Details"
                            >
                              <FaEye />
                            </Button>
                            {task.status !== 'completed' && task.status !== 'cancelled' && (
                              <>
                                <Button
                                  variant="outline-warning"
                                  size="sm"
                                  onClick={() => openUpdateModal(task)}
                                  title="Update Task"
                                >
                                  <FaPlay />
                                </Button>
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() => openCommentModal(task)}
                                  title="Add Comment"
                                >
                                  <FaComment />
                                </Button>
                                <Button
                                  variant="outline-secondary"
                                  size="sm"
                                  onClick={() => openTimeModal(task)}
                                  title="Log Time"
                                >
                                  <FaClock />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Task Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {modalMode === 'view' && 'Task Details'}
            {modalMode === 'update' && 'Update Task'}
            {modalMode === 'comment' && 'Add Comment'}
            {modalMode === 'time' && 'Log Time'}
          </Modal.Title>
        </Modal.Header>
        
        {modalMode === 'view' && selectedTask ? (
          <Modal.Body>
            <Row>
              <Col md={6}>
                <h6>Task Information</h6>
                <p><strong>Title:</strong> {selectedTask.title}</p>
                <p><strong>Description:</strong> {selectedTask.description}</p>
                <p><strong>Type:</strong> {selectedTask.type.replace('_', ' ')}</p>
                <p><strong>Priority:</strong> {getPriorityBadge(selectedTask.priority)}</p>
                <p><strong>Status:</strong> {getStatusBadge(selectedTask.status)}</p>
                <p><strong>Progress:</strong> {selectedTask.progress}%</p>
              </Col>
              <Col md={6}>
                <h6>Assignment Details</h6>
                <p><strong>Assigned By:</strong> {selectedTask.assignedBy.firstName} {selectedTask.assignedBy.lastName}</p>
                <p><strong>Due Date:</strong> {formatDate(selectedTask.dueDate)}</p>
                <p><strong>Estimated Hours:</strong> {selectedTask.estimatedHours}</p>
                <p><strong>Assigned Date:</strong> {formatDate(selectedTask.assignedAt)}</p>
                {selectedTask.completedAt && (
                  <p><strong>Completed Date:</strong> {formatDate(selectedTask.completedAt)}</p>
                )}
              </Col>
            </Row>
            
            {selectedTask.timeEntries && selectedTask.timeEntries.length > 0 && (
              <div className="mt-3">
                <h6>Time Entries</h6>
                <Table size="sm">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Duration</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedTask.timeEntries.map((entry, index) => (
                      <tr key={index}>
                        <td>{formatDateTime(entry.startTime)}</td>
                        <td>{formatDuration(entry.duration)}</td>
                        <td>{entry.description || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
                <p><strong>Total Time:</strong> {formatDuration(selectedTask.totalTimeSpent || 0)}</p>
              </div>
            )}
            
            {selectedTask.comments && selectedTask.comments.length > 0 && (
              <div className="mt-3">
                <h6>Comments</h6>
                {selectedTask.comments.map((comment, index) => (
                  <div key={index} className="border-start border-3 border-primary ps-3 mb-2">
                    <small className="text-muted">
                      {comment.author.firstName} {comment.author.lastName} - {formatDateTime(comment.createdAt)}
                    </small>
                    <p className="mb-0">{comment.content}</p>
                  </div>
                ))}
              </div>
            )}
          </Modal.Body>
        ) : modalMode === 'update' ? (
          <Form onSubmit={handleUpdateTask}>
            <Modal.Body>
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="on_hold">On Hold</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Progress (%)</Form.Label>
                    <Form.Control
                      type="number"
                      name="progress"
                      value={formData.progress}
                      onChange={handleInputChange}
                      min="0"
                      max="100"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Completion Notes</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="completionNotes"
                  value={formData.completionNotes}
                  onChange={handleInputChange}
                  placeholder="Add any notes about your progress or completion..."
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    Updating...
                  </>
                ) : (
                  'Update Task'
                )}
              </Button>
            </Modal.Footer>
          </Form>
        ) : modalMode === 'comment' ? (
          <Form onSubmit={handleAddComment}>
            <Modal.Body>
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Form.Group className="mb-3">
                <Form.Label>Comment</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  name="comment"
                  value={formData.comment}
                  onChange={handleInputChange}
                  placeholder="Add your comment..."
                  required
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    Adding...
                  </>
                ) : (
                  'Add Comment'
                )}
              </Button>
            </Modal.Footer>
          </Form>
        ) : modalMode === 'time' ? (
          <Form onSubmit={handleLogTime}>
            <Modal.Body>
              {error && <Alert variant="danger">{error}</Alert>}
              
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Start Time</Form.Label>
                    <Form.Control
                      type="datetime-local"
                      name="startTime"
                      value={formData.startTime}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>End Time</Form.Label>
                    <Form.Control
                      type="datetime-local"
                      name="endTime"
                      value={formData.endTime}
                      onChange={handleInputChange}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Description (Optional)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={2}
                  name="timeDescription"
                  value={formData.timeDescription}
                  onChange={handleInputChange}
                  placeholder="Describe what you worked on..."
                />
              </Form.Group>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    Logging...
                  </>
                ) : (
                  'Log Time'
                )}
              </Button>
            </Modal.Footer>
          </Form>
        ) : null}
      </Modal>
    </Container>
  );
};

export default MyTasks;
