{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { Container, Row, Col, Alert } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRoles = [],\n  requireAuth = true\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center min-vh-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mx-auto mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated()) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If specific roles are required\n  if (requiredRoles.length > 0 && user) {\n    const hasRequiredRole = requiredRoles.includes(user.role);\n    if (!hasRequiredRole) {\n      return /*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-5\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Access Denied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"You don't have permission to access this page.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Required roles: \", requiredRoles.join(', ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Your role: \", user.role]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this);\n    }\n  }\n  return children;\n};\n_s(ProtectedRoute, \"pvp8inAvQHEb0BVUe3eLqKvyMMQ=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Container", "Row", "Col", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRoles", "requireAuth", "_s", "user", "isAuthenticated", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "length", "hasRequiredRole", "includes", "role", "md", "variant", "join", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { Container, Row, Col, Alert } from 'react-bootstrap';\n\nconst ProtectedRoute = ({ children, requiredRoles = [], requireAuth = true }) => {\n  const { user, isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center min-vh-100\">\n        <div className=\"text-center\">\n          <div className=\"loading-spinner mx-auto mb-3\"></div>\n          <p>Loading...</p>\n        </div>\n      </Container>\n    );\n  }\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated()) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // If specific roles are required\n  if (requiredRoles.length > 0 && user) {\n    const hasRequiredRole = requiredRoles.includes(user.role);\n    \n    if (!hasRequiredRole) {\n      return (\n        <Container className=\"mt-5\">\n          <Row className=\"justify-content-center\">\n            <Col md={8}>\n              <Alert variant=\"danger\" className=\"text-center\">\n                <h4>Access Denied</h4>\n                <p>You don't have permission to access this page.</p>\n                <p>Required roles: {requiredRoles.join(', ')}</p>\n                <p>Your role: {user.role}</p>\n              </Alert>\n            </Col>\n          </Row>\n        </Container>\n      );\n    }\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG,EAAE;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGd,OAAO,CAAC,CAAC;EACpD,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIe,OAAO,EAAE;IACX,oBACER,OAAA,CAACL,SAAS;MAACe,SAAS,EAAC,6DAA6D;MAAAR,QAAA,eAChFF,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAR,QAAA,gBAC1BF,OAAA;UAAKU,SAAS,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpDd,OAAA;UAAAE,QAAA,EAAG;QAAU;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;;EAEA;EACA,IAAIV,WAAW,IAAI,CAACG,eAAe,CAAC,CAAC,EAAE;IACrC,oBAAOP,OAAA,CAACR,QAAQ;MAACuB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIX,aAAa,CAACgB,MAAM,GAAG,CAAC,IAAIb,IAAI,EAAE;IACpC,MAAMc,eAAe,GAAGjB,aAAa,CAACkB,QAAQ,CAACf,IAAI,CAACgB,IAAI,CAAC;IAEzD,IAAI,CAACF,eAAe,EAAE;MACpB,oBACEpB,OAAA,CAACL,SAAS;QAACe,SAAS,EAAC,MAAM;QAAAR,QAAA,eACzBF,OAAA,CAACJ,GAAG;UAACc,SAAS,EAAC,wBAAwB;UAAAR,QAAA,eACrCF,OAAA,CAACH,GAAG;YAAC0B,EAAE,EAAE,CAAE;YAAArB,QAAA,eACTF,OAAA,CAACF,KAAK;cAAC0B,OAAO,EAAC,QAAQ;cAACd,SAAS,EAAC,aAAa;cAAAR,QAAA,gBAC7CF,OAAA;gBAAAE,QAAA,EAAI;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBd,OAAA;gBAAAE,QAAA,EAAG;cAA8C;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDd,OAAA;gBAAAE,QAAA,GAAG,kBAAgB,EAACC,aAAa,CAACsB,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDd,OAAA;gBAAAE,QAAA,GAAG,aAAW,EAACI,IAAI,CAACgB,IAAI;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEhB;EACF;EAEA,OAAOZ,QAAQ;AACjB,CAAC;AAACG,EAAA,CA5CIJ,cAAc;EAAA,QACyBP,OAAO,EACjCD,WAAW;AAAA;AAAAiC,EAAA,GAFxBzB,cAAc;AA8CpB,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}