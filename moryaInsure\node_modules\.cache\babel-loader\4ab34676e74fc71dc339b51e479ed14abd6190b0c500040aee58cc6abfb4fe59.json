{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\ContactSupport.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Form, Alert, Spinner, Table, Badge, Accordion } from 'react-bootstrap';\nimport { FaPhone, FaEnvelope, FaMapMarkerAlt, FaClock, FaTicketAlt, FaQuestionCircle, FaHeadset } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { ticketsAPI, categoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ContactSupport = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [tickets, setTickets] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [formData, setFormData] = useState({\n    subject: '',\n    category: '',\n    priority: 'medium',\n    description: '',\n    attachments: []\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [activeTab, setActiveTab] = useState('contact');\n  useEffect(() => {\n    fetchTickets();\n    fetchCategories();\n  }, []);\n  const fetchTickets = async () => {\n    try {\n      const response = await ticketsAPI.getTickets();\n      if (response.success) {\n        setTickets(response.data.tickets || []);\n      }\n    } catch (error) {\n      console.error('Error fetching tickets:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await categoriesAPI.getCategories();\n      if (response.success) {\n        setCategories(response.data.categories || []);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setError('');\n  };\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    setFormData(prev => ({\n      ...prev,\n      attachments: files\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError('');\n    try {\n      const ticketData = new FormData();\n      ticketData.append('subject', formData.subject);\n      ticketData.append('category', formData.category);\n      ticketData.append('priority', formData.priority);\n      ticketData.append('description', formData.description);\n\n      // Append attachments\n      formData.attachments.forEach(file => {\n        ticketData.append('attachments', file);\n      });\n      const response = await ticketsAPI.createTicket(ticketData);\n      if (response.success) {\n        setSuccess('Support ticket submitted successfully! We will get back to you soon.');\n        setFormData({\n          subject: '',\n          category: '',\n          priority: 'medium',\n          description: '',\n          attachments: []\n        });\n        fetchTickets(); // Refresh tickets list\n      } else {\n        setError(response.message || 'Failed to submit ticket');\n      }\n    } catch (error) {\n      console.error('Error submitting ticket:', error);\n      setError('Failed to submit ticket. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'open': {\n        variant: 'primary',\n        text: 'Open'\n      },\n      'in-progress': {\n        variant: 'warning',\n        text: 'In Progress'\n      },\n      'resolved': {\n        variant: 'success',\n        text: 'Resolved'\n      },\n      'closed': {\n        variant: 'secondary',\n        text: 'Closed'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 12\n    }, this);\n  };\n  const getPriorityBadge = priority => {\n    const priorityConfig = {\n      'low': {\n        variant: 'success',\n        text: 'Low'\n      },\n      'medium': {\n        variant: 'warning',\n        text: 'Medium'\n      },\n      'high': {\n        variant: 'danger',\n        text: 'High'\n      },\n      'urgent': {\n        variant: 'dark',\n        text: 'Urgent'\n      }\n    };\n    const config = priorityConfig[priority] || {\n      variant: 'secondary',\n      text: priority\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-IN');\n  };\n  const faqData = [{\n    question: \"How do I file an insurance claim?\",\n    answer: \"You can file a claim by visiting the Claims page in your dashboard. Select your policy, provide incident details, and upload supporting documents. Our team will review and process your claim within 3-5 business days.\"\n  }, {\n    question: \"How can I update my policy information?\",\n    answer: \"You can update your personal information and policy details through your profile page. For major changes like beneficiary updates, please contact our support team.\"\n  }, {\n    question: \"What documents do I need for claim processing?\",\n    answer: \"Required documents vary by claim type but typically include: incident photos, police reports (for theft/accidents), medical bills (for health claims), and any relevant receipts or invoices.\"\n  }, {\n    question: \"How long does claim processing take?\",\n    answer: \"Most claims are processed within 7-14 business days. Complex claims may take longer. You'll receive email updates throughout the process.\"\n  }, {\n    question: \"Can I cancel my policy?\",\n    answer: \"Yes, you can request policy cancellation by contacting our support team. Please note that cancellation terms and any applicable fees depend on your policy type and duration.\"\n  }];\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mb-1\",\n          children: \"Contact Support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"Get help with your insurance needs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"success\",\n      dismissible: true,\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-2 mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: activeTab === 'contact' ? 'primary' : 'outline-primary',\n            onClick: () => setActiveTab('contact'),\n            children: [/*#__PURE__*/_jsxDEV(FaHeadset, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), \"Contact Info\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: activeTab === 'ticket' ? 'primary' : 'outline-primary',\n            onClick: () => setActiveTab('ticket'),\n            children: [/*#__PURE__*/_jsxDEV(FaTicketAlt, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), \"Submit Ticket\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: activeTab === 'tickets' ? 'primary' : 'outline-primary',\n            onClick: () => setActiveTab('tickets'),\n            children: [/*#__PURE__*/_jsxDEV(FaTicketAlt, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), \"My Tickets\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: activeTab === 'faq' ? 'primary' : 'outline-primary',\n            onClick: () => setActiveTab('faq'),\n            children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), \"FAQ\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), activeTab === 'contact' && /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"me-2 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Phone:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), \" +91 98765 43210\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                className: \"me-2 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), \" <EMAIL>\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                className: \"me-2 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Address:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 44\n              }, this), \"Morya Insurance Ltd.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 39\n              }, this), \"123 Business District\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 40\n              }, this), \"Mumbai, Maharashtra 400001\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(FaClock, {\n                className: \"me-2 text-primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Business Hours:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 51\n              }, this), \"Monday - Friday: 9:00 AM - 6:00 PM\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 53\n              }, this), \"Saturday: 9:00 AM - 2:00 PM\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 46\n              }, this), \"Sunday: Closed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        className: \"mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Emergency Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"24/7 Emergency Helpline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 59\n              }, this), /*#__PURE__*/_jsxDEV(FaPhone, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"+91 98765 43211\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"For urgent claims and emergencies, call our 24/7 helpline. Our emergency response team is available round the clock to assist you.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mt-4\",\n              children: \"Quick Actions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-grid gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-primary\",\n                onClick: () => setActiveTab('ticket'),\n                children: \"Submit Support Ticket\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-success\",\n                onClick: () => setActiveTab('faq'),\n                children: \"Browse FAQ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this), activeTab === 'ticket' && /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        className: \"mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Submit Support Ticket\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 27\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Subject *\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      name: \"subject\",\n                      value: formData.subject,\n                      onChange: handleInputChange,\n                      placeholder: \"Brief description of your issue\",\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Category *\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      name: \"category\",\n                      value: formData.category,\n                      onChange: handleInputChange,\n                      required: true,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select category...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"technical\",\n                        children: \"Technical Issue\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"billing\",\n                        children: \"Billing & Payments\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"claims\",\n                        children: \"Claims Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"policy\",\n                        children: \"Policy Information\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"general\",\n                        children: \"General Inquiry\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Priority\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"priority\",\n                  value: formData.priority,\n                  onChange: handleInputChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"low\",\n                    children: \"Low\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"medium\",\n                    children: \"Medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"high\",\n                    children: \"High\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"urgent\",\n                    children: \"Urgent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Description *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 5,\n                  name: \"description\",\n                  value: formData.description,\n                  onChange: handleInputChange,\n                  placeholder: \"Please provide detailed information about your issue...\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Attachments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  multiple: true,\n                  accept: \".pdf,.jpg,.jpeg,.png,.doc,.docx\",\n                  onChange: handleFileChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                  className: \"text-muted\",\n                  children: \"Upload screenshots, documents, or other relevant files (PDF, JPG, PNG, DOC)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-grid\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"primary\",\n                  size: \"lg\",\n                  disabled: submitting,\n                  children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      animation: \"border\",\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 27\n                    }, this), \"Submitting...\"]\n                  }, void 0, true) : 'Submit Ticket'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this), activeTab === 'tickets' && /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"My Support Tickets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2\",\n                children: \"Loading tickets...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this) : tickets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaTicketAlt, {\n                size: 48,\n                className: \"text-muted mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"No Tickets Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"You haven't submitted any support tickets yet.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: () => setActiveTab('ticket'),\n                children: \"Submit Your First Ticket\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Ticket ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Subject\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Priority\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Created\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Last Updated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: tickets.map(ticket => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [\"#\", ticket.ticketId]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ticket.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"light\",\n                      text: \"dark\",\n                      children: ticket.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getPriorityBadge(ticket.priority)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(ticket.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(ticket.createdAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(ticket.updatedAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 27\n                  }, this)]\n                }, ticket._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 9\n    }, this), activeTab === 'faq' && /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        lg: 10,\n        className: \"mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Frequently Asked Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Accordion, {\n              children: faqData.map((faq, index) => /*#__PURE__*/_jsxDEV(Accordion.Item, {\n                eventKey: index.toString(),\n                children: [/*#__PURE__*/_jsxDEV(Accordion.Header, {\n                  children: faq.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Accordion.Body, {\n                  children: faq.answer\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Can't find what you're looking for?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: () => setActiveTab('ticket'),\n                children: \"Submit a Support Ticket\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(ContactSupport, \"DNsfVoS5dWUzYSyTe9fXtPX5pk4=\", false, function () {\n  return [useAuth];\n});\n_c = ContactSupport;\nexport default ContactSupport;\nvar _c;\n$RefreshReg$(_c, \"ContactSupport\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Table", "Badge", "Accordion", "FaPhone", "FaEnvelope", "FaMapMarkerAlt", "FaClock", "FaTicketAlt", "FaQuestionCircle", "FaHeadset", "useAuth", "ticketsAPI", "categoriesAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ContactSupport", "_s", "user", "tickets", "setTickets", "categories", "setCategories", "loading", "setLoading", "submitting", "setSubmitting", "formData", "setFormData", "subject", "category", "priority", "description", "attachments", "error", "setError", "success", "setSuccess", "activeTab", "setActiveTab", "fetchTickets", "fetchCategories", "response", "getTickets", "data", "console", "getCategories", "handleInputChange", "e", "name", "value", "target", "prev", "handleFileChange", "files", "Array", "from", "handleSubmit", "preventDefault", "ticketData", "FormData", "append", "for<PERSON>ach", "file", "createTicket", "message", "getStatusBadge", "status", "statusConfig", "variant", "text", "config", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priorityConfig", "formatDate", "dateString", "Date", "toLocaleDateString", "faqData", "question", "answer", "fluid", "className", "dismissible", "onClose", "onClick", "md", "Header", "Body", "lg", "onSubmit", "Group", "Label", "Control", "type", "onChange", "placeholder", "required", "Select", "as", "rows", "multiple", "accept", "Text", "size", "disabled", "animation", "length", "responsive", "hover", "map", "ticket", "ticketId", "createdAt", "updatedAt", "_id", "faq", "index", "<PERSON><PERSON>", "eventKey", "toString", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/ContactSupport.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON>er, <PERSON>, Col, Card, Button, Form, Alert, Spinner, Table, Badge, Accordion } from 'react-bootstrap';\nimport { FaPhone, FaEnvelope, FaMapMarkerAlt, FaClock, FaTicketAlt, FaQuestionCircle, FaHeadset } from 'react-icons/fa';\nimport { useAuth } from '../context/AuthContext';\nimport { ticketsAPI, categoriesAPI } from '../services/api';\n\nconst ContactSupport = () => {\n  const { user } = useAuth();\n  const [tickets, setTickets] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [formData, setFormData] = useState({\n    subject: '',\n    category: '',\n    priority: 'medium',\n    description: '',\n    attachments: []\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [activeTab, setActiveTab] = useState('contact');\n\n  useEffect(() => {\n    fetchTickets();\n    fetchCategories();\n  }, []);\n\n  const fetchTickets = async () => {\n    try {\n      const response = await ticketsAPI.getTickets();\n      if (response.success) {\n        setTickets(response.data.tickets || []);\n      }\n    } catch (error) {\n      console.error('Error fetching tickets:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await categoriesAPI.getCategories();\n      if (response.success) {\n        setCategories(response.data.categories || []);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    setError('');\n  };\n\n  const handleFileChange = (e) => {\n    const files = Array.from(e.target.files);\n    setFormData(prev => ({\n      ...prev,\n      attachments: files\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSubmitting(true);\n    setError('');\n\n    try {\n      const ticketData = new FormData();\n      ticketData.append('subject', formData.subject);\n      ticketData.append('category', formData.category);\n      ticketData.append('priority', formData.priority);\n      ticketData.append('description', formData.description);\n      \n      // Append attachments\n      formData.attachments.forEach((file) => {\n        ticketData.append('attachments', file);\n      });\n\n      const response = await ticketsAPI.createTicket(ticketData);\n      \n      if (response.success) {\n        setSuccess('Support ticket submitted successfully! We will get back to you soon.');\n        setFormData({\n          subject: '',\n          category: '',\n          priority: 'medium',\n          description: '',\n          attachments: []\n        });\n        fetchTickets(); // Refresh tickets list\n      } else {\n        setError(response.message || 'Failed to submit ticket');\n      }\n    } catch (error) {\n      console.error('Error submitting ticket:', error);\n      setError('Failed to submit ticket. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const getStatusBadge = (status) => {\n    const statusConfig = {\n      'open': { variant: 'primary', text: 'Open' },\n      'in-progress': { variant: 'warning', text: 'In Progress' },\n      'resolved': { variant: 'success', text: 'Resolved' },\n      'closed': { variant: 'secondary', text: 'Closed' }\n    };\n    \n    const config = statusConfig[status] || { variant: 'secondary', text: status };\n    return <Badge bg={config.variant}>{config.text}</Badge>;\n  };\n\n  const getPriorityBadge = (priority) => {\n    const priorityConfig = {\n      'low': { variant: 'success', text: 'Low' },\n      'medium': { variant: 'warning', text: 'Medium' },\n      'high': { variant: 'danger', text: 'High' },\n      'urgent': { variant: 'dark', text: 'Urgent' }\n    };\n    \n    const config = priorityConfig[priority] || { variant: 'secondary', text: priority };\n    return <Badge bg={config.variant}>{config.text}</Badge>;\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-IN');\n  };\n\n  const faqData = [\n    {\n      question: \"How do I file an insurance claim?\",\n      answer: \"You can file a claim by visiting the Claims page in your dashboard. Select your policy, provide incident details, and upload supporting documents. Our team will review and process your claim within 3-5 business days.\"\n    },\n    {\n      question: \"How can I update my policy information?\",\n      answer: \"You can update your personal information and policy details through your profile page. For major changes like beneficiary updates, please contact our support team.\"\n    },\n    {\n      question: \"What documents do I need for claim processing?\",\n      answer: \"Required documents vary by claim type but typically include: incident photos, police reports (for theft/accidents), medical bills (for health claims), and any relevant receipts or invoices.\"\n    },\n    {\n      question: \"How long does claim processing take?\",\n      answer: \"Most claims are processed within 7-14 business days. Complex claims may take longer. You'll receive email updates throughout the process.\"\n    },\n    {\n      question: \"Can I cancel my policy?\",\n      answer: \"Yes, you can request policy cancellation by contacting our support team. Please note that cancellation terms and any applicable fees depend on your policy type and duration.\"\n    }\n  ];\n\n  return (\n    <Container fluid className=\"py-4\">\n      <Row className=\"mb-4\">\n        <Col>\n          <h2 className=\"mb-1\">Contact Support</h2>\n          <p className=\"text-muted\">Get help with your insurance needs</p>\n        </Col>\n      </Row>\n\n      {success && (\n        <Alert variant=\"success\" dismissible onClose={() => setSuccess('')}>\n          {success}\n        </Alert>\n      )}\n\n      <Row className=\"mb-4\">\n        <Col>\n          <div className=\"d-flex gap-2 mb-3\">\n            <Button \n              variant={activeTab === 'contact' ? 'primary' : 'outline-primary'}\n              onClick={() => setActiveTab('contact')}\n            >\n              <FaHeadset className=\"me-2\" />\n              Contact Info\n            </Button>\n            <Button \n              variant={activeTab === 'ticket' ? 'primary' : 'outline-primary'}\n              onClick={() => setActiveTab('ticket')}\n            >\n              <FaTicketAlt className=\"me-2\" />\n              Submit Ticket\n            </Button>\n            <Button \n              variant={activeTab === 'tickets' ? 'primary' : 'outline-primary'}\n              onClick={() => setActiveTab('tickets')}\n            >\n              <FaTicketAlt className=\"me-2\" />\n              My Tickets\n            </Button>\n            <Button \n              variant={activeTab === 'faq' ? 'primary' : 'outline-primary'}\n              onClick={() => setActiveTab('faq')}\n            >\n              <FaQuestionCircle className=\"me-2\" />\n              FAQ\n            </Button>\n          </div>\n        </Col>\n      </Row>\n\n      {activeTab === 'contact' && (\n        <Row>\n          <Col md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header>\n                <h5 className=\"mb-0\">Contact Information</h5>\n              </Card.Header>\n              <Card.Body>\n                <div className=\"mb-3\">\n                  <FaPhone className=\"me-2 text-primary\" />\n                  <strong>Phone:</strong> +91 98765 43210\n                </div>\n                <div className=\"mb-3\">\n                  <FaEnvelope className=\"me-2 text-primary\" />\n                  <strong>Email:</strong> <EMAIL>\n                </div>\n                <div className=\"mb-3\">\n                  <FaMapMarkerAlt className=\"me-2 text-primary\" />\n                  <strong>Address:</strong><br />\n                  Morya Insurance Ltd.<br />\n                  123 Business District<br />\n                  Mumbai, Maharashtra 400001\n                </div>\n                <div className=\"mb-3\">\n                  <FaClock className=\"me-2 text-primary\" />\n                  <strong>Business Hours:</strong><br />\n                  Monday - Friday: 9:00 AM - 6:00 PM<br />\n                  Saturday: 9:00 AM - 2:00 PM<br />\n                  Sunday: Closed\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n          <Col md={6} className=\"mb-4\">\n            <Card className=\"h-100\">\n              <Card.Header>\n                <h5 className=\"mb-0\">Emergency Contact</h5>\n              </Card.Header>\n              <Card.Body>\n                <Alert variant=\"danger\">\n                  <strong>24/7 Emergency Helpline</strong><br />\n                  <FaPhone className=\"me-2\" />\n                  <strong>+91 98765 43211</strong>\n                </Alert>\n                <p>For urgent claims and emergencies, call our 24/7 helpline. Our emergency response team is available round the clock to assist you.</p>\n                \n                <h6 className=\"mt-4\">Quick Actions:</h6>\n                <div className=\"d-grid gap-2\">\n                  <Button variant=\"outline-primary\" onClick={() => setActiveTab('ticket')}>\n                    Submit Support Ticket\n                  </Button>\n                  <Button variant=\"outline-success\" onClick={() => setActiveTab('faq')}>\n                    Browse FAQ\n                  </Button>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {activeTab === 'ticket' && (\n        <Row>\n          <Col lg={8} className=\"mx-auto\">\n            <Card>\n              <Card.Header>\n                <h5 className=\"mb-0\">Submit Support Ticket</h5>\n              </Card.Header>\n              <Card.Body>\n                {error && <Alert variant=\"danger\">{error}</Alert>}\n                \n                <Form onSubmit={handleSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Subject *</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          name=\"subject\"\n                          value={formData.subject}\n                          onChange={handleInputChange}\n                          placeholder=\"Brief description of your issue\"\n                          required\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Category *</Form.Label>\n                        <Form.Select\n                          name=\"category\"\n                          value={formData.category}\n                          onChange={handleInputChange}\n                          required\n                        >\n                          <option value=\"\">Select category...</option>\n                          <option value=\"technical\">Technical Issue</option>\n                          <option value=\"billing\">Billing & Payments</option>\n                          <option value=\"claims\">Claims Support</option>\n                          <option value=\"policy\">Policy Information</option>\n                          <option value=\"general\">General Inquiry</option>\n                        </Form.Select>\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Priority</Form.Label>\n                    <Form.Select\n                      name=\"priority\"\n                      value={formData.priority}\n                      onChange={handleInputChange}\n                    >\n                      <option value=\"low\">Low</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"high\">High</option>\n                      <option value=\"urgent\">Urgent</option>\n                    </Form.Select>\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Description *</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={5}\n                      name=\"description\"\n                      value={formData.description}\n                      onChange={handleInputChange}\n                      placeholder=\"Please provide detailed information about your issue...\"\n                      required\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Attachments</Form.Label>\n                    <Form.Control\n                      type=\"file\"\n                      multiple\n                      accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\"\n                      onChange={handleFileChange}\n                    />\n                    <Form.Text className=\"text-muted\">\n                      Upload screenshots, documents, or other relevant files (PDF, JPG, PNG, DOC)\n                    </Form.Text>\n                  </Form.Group>\n\n                  <div className=\"d-grid\">\n                    <Button \n                      type=\"submit\" \n                      variant=\"primary\" \n                      size=\"lg\"\n                      disabled={submitting}\n                    >\n                      {submitting ? (\n                        <>\n                          <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                          Submitting...\n                        </>\n                      ) : (\n                        'Submit Ticket'\n                      )}\n                    </Button>\n                  </div>\n                </Form>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {activeTab === 'tickets' && (\n        <Row>\n          <Col>\n            <Card>\n              <Card.Header>\n                <h5 className=\"mb-0\">My Support Tickets</h5>\n              </Card.Header>\n              <Card.Body>\n                {loading ? (\n                  <div className=\"text-center py-4\">\n                    <Spinner animation=\"border\" />\n                    <p className=\"mt-2\">Loading tickets...</p>\n                  </div>\n                ) : tickets.length === 0 ? (\n                  <div className=\"text-center py-4\">\n                    <FaTicketAlt size={48} className=\"text-muted mb-3\" />\n                    <h5>No Tickets Found</h5>\n                    <p className=\"text-muted\">You haven't submitted any support tickets yet.</p>\n                    <Button variant=\"primary\" onClick={() => setActiveTab('ticket')}>\n                      Submit Your First Ticket\n                    </Button>\n                  </div>\n                ) : (\n                  <Table responsive hover>\n                    <thead>\n                      <tr>\n                        <th>Ticket ID</th>\n                        <th>Subject</th>\n                        <th>Category</th>\n                        <th>Priority</th>\n                        <th>Status</th>\n                        <th>Created</th>\n                        <th>Last Updated</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {tickets.map((ticket) => (\n                        <tr key={ticket._id}>\n                          <td>\n                            <strong>#{ticket.ticketId}</strong>\n                          </td>\n                          <td>{ticket.subject}</td>\n                          <td>\n                            <Badge bg=\"light\" text=\"dark\">\n                              {ticket.category}\n                            </Badge>\n                          </td>\n                          <td>{getPriorityBadge(ticket.priority)}</td>\n                          <td>{getStatusBadge(ticket.status)}</td>\n                          <td>{formatDate(ticket.createdAt)}</td>\n                          <td>{formatDate(ticket.updatedAt)}</td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </Table>\n                )}\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n\n      {activeTab === 'faq' && (\n        <Row>\n          <Col lg={10} className=\"mx-auto\">\n            <Card>\n              <Card.Header>\n                <h5 className=\"mb-0\">Frequently Asked Questions</h5>\n              </Card.Header>\n              <Card.Body>\n                <Accordion>\n                  {faqData.map((faq, index) => (\n                    <Accordion.Item eventKey={index.toString()} key={index}>\n                      <Accordion.Header>{faq.question}</Accordion.Header>\n                      <Accordion.Body>{faq.answer}</Accordion.Body>\n                    </Accordion.Item>\n                  ))}\n                </Accordion>\n                \n                <div className=\"mt-4 text-center\">\n                  <p className=\"text-muted\">Can't find what you're looking for?</p>\n                  <Button variant=\"primary\" onClick={() => setActiveTab('ticket')}>\n                    Submit a Support Ticket\n                  </Button>\n                </div>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      )}\n    </Container>\n  );\n};\n\nexport default ContactSupport;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,QAAQ,iBAAiB;AAClH,SAASC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAEC,OAAO,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,QAAQ,gBAAgB;AACvH,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC;IACvCwC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,SAAS,CAAC;EAErDC,SAAS,CAAC,MAAM;IACdkD,YAAY,CAAC,CAAC;IACdC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMhC,UAAU,CAACiC,UAAU,CAAC,CAAC;MAC9C,IAAID,QAAQ,CAACN,OAAO,EAAE;QACpBhB,UAAU,CAACsB,QAAQ,CAACE,IAAI,CAACzB,OAAO,IAAI,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM/B,aAAa,CAACmC,aAAa,CAAC,CAAC;MACpD,IAAIJ,QAAQ,CAACN,OAAO,EAAE;QACpBd,aAAa,CAACoB,QAAQ,CAACE,IAAI,CAACvB,UAAU,IAAI,EAAE,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMa,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvB,WAAW,CAACwB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACHf,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMkB,gBAAgB,GAAIL,CAAC,IAAK;IAC9B,MAAMM,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACR,CAAC,CAACG,MAAM,CAACG,KAAK,CAAC;IACxC1B,WAAW,CAACwB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPnB,WAAW,EAAEqB;IACf,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClBhC,aAAa,CAAC,IAAI,CAAC;IACnBS,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMwB,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCD,UAAU,CAACE,MAAM,CAAC,SAAS,EAAElC,QAAQ,CAACE,OAAO,CAAC;MAC9C8B,UAAU,CAACE,MAAM,CAAC,UAAU,EAAElC,QAAQ,CAACG,QAAQ,CAAC;MAChD6B,UAAU,CAACE,MAAM,CAAC,UAAU,EAAElC,QAAQ,CAACI,QAAQ,CAAC;MAChD4B,UAAU,CAACE,MAAM,CAAC,aAAa,EAAElC,QAAQ,CAACK,WAAW,CAAC;;MAEtD;MACAL,QAAQ,CAACM,WAAW,CAAC6B,OAAO,CAAEC,IAAI,IAAK;QACrCJ,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEE,IAAI,CAAC;MACxC,CAAC,CAAC;MAEF,MAAMrB,QAAQ,GAAG,MAAMhC,UAAU,CAACsD,YAAY,CAACL,UAAU,CAAC;MAE1D,IAAIjB,QAAQ,CAACN,OAAO,EAAE;QACpBC,UAAU,CAAC,sEAAsE,CAAC;QAClFT,WAAW,CAAC;UACVC,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,QAAQ;UAClBC,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE;QACf,CAAC,CAAC;QACFO,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACLL,QAAQ,CAACO,QAAQ,CAACuB,OAAO,IAAI,yBAAyB,CAAC;MACzD;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,4CAA4C,CAAC;IACxD,CAAC,SAAS;MACRT,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMwC,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,YAAY,GAAG;MACnB,MAAM,EAAE;QAAEC,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAO,CAAC;MAC5C,aAAa,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MAC1D,UAAU,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC;MACpD,QAAQ,EAAE;QAAED,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAS;IACnD,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAAC,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAEH;IAAO,CAAC;IAC7E,oBAAOtD,OAAA,CAACb,KAAK;MAACwE,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,EAAEF,MAAM,CAACD;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACzD,CAAC;EAED,MAAMC,gBAAgB,GAAI/C,QAAQ,IAAK;IACrC,MAAMgD,cAAc,GAAG;MACrB,KAAK,EAAE;QAAEV,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC1C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAS,CAAC;MAChD,MAAM,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAO,CAAC;MAC3C,QAAQ,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAS;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGQ,cAAc,CAAChD,QAAQ,CAAC,IAAI;MAAEsC,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAEvC;IAAS,CAAC;IACnF,oBAAOlB,OAAA,CAACb,KAAK;MAACwE,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,EAAEF,MAAM,CAACD;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACzD,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EACzD,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,QAAQ,EAAE,mCAAmC;IAC7CC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,yCAAyC;IACnDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,gDAAgD;IAC1DC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,sCAAsC;IAChDC,MAAM,EAAE;EACV,CAAC,EACD;IACED,QAAQ,EAAE,yBAAyB;IACnCC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEzE,OAAA,CAACtB,SAAS;IAACgG,KAAK;IAACC,SAAS,EAAC,MAAM;IAAAf,QAAA,gBAC/B5D,OAAA,CAACrB,GAAG;MAACgG,SAAS,EAAC,MAAM;MAAAf,QAAA,eACnB5D,OAAA,CAACpB,GAAG;QAAAgF,QAAA,gBACF5D,OAAA;UAAI2E,SAAS,EAAC,MAAM;UAAAf,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzChE,OAAA;UAAG2E,SAAS,EAAC,YAAY;UAAAf,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzC,OAAO,iBACNvB,OAAA,CAAChB,KAAK;MAACwE,OAAO,EAAC,SAAS;MAACoB,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMrD,UAAU,CAAC,EAAE,CAAE;MAAAoC,QAAA,EAChErC;IAAO;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAEDhE,OAAA,CAACrB,GAAG;MAACgG,SAAS,EAAC,MAAM;MAAAf,QAAA,eACnB5D,OAAA,CAACpB,GAAG;QAAAgF,QAAA,eACF5D,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAf,QAAA,gBAChC5D,OAAA,CAAClB,MAAM;YACL0E,OAAO,EAAE/B,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,iBAAkB;YACjEqD,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,SAAS,CAAE;YAAAkC,QAAA,gBAEvC5D,OAAA,CAACL,SAAS;cAACgF,SAAS,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA,CAAClB,MAAM;YACL0E,OAAO,EAAE/B,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,iBAAkB;YAChEqD,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,QAAQ,CAAE;YAAAkC,QAAA,gBAEtC5D,OAAA,CAACP,WAAW;cAACkF,SAAS,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA,CAAClB,MAAM;YACL0E,OAAO,EAAE/B,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,iBAAkB;YACjEqD,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,SAAS,CAAE;YAAAkC,QAAA,gBAEvC5D,OAAA,CAACP,WAAW;cAACkF,SAAS,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA,CAAClB,MAAM;YACL0E,OAAO,EAAE/B,SAAS,KAAK,KAAK,GAAG,SAAS,GAAG,iBAAkB;YAC7DqD,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,KAAK,CAAE;YAAAkC,QAAA,gBAEnC5D,OAAA,CAACN,gBAAgB;cAACiF,SAAS,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,OAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELvC,SAAS,KAAK,SAAS,iBACtBzB,OAAA,CAACrB,GAAG;MAAAiF,QAAA,gBACF5D,OAAA,CAACpB,GAAG;QAACmG,EAAE,EAAE,CAAE;QAACJ,SAAS,EAAC,MAAM;QAAAf,QAAA,eAC1B5D,OAAA,CAACnB,IAAI;UAAC8F,SAAS,EAAC,OAAO;UAAAf,QAAA,gBACrB5D,OAAA,CAACnB,IAAI,CAACmG,MAAM;YAAApB,QAAA,eACV5D,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACdhE,OAAA,CAACnB,IAAI,CAACoG,IAAI;YAAArB,QAAA,gBACR5D,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAf,QAAA,gBACnB5D,OAAA,CAACX,OAAO;gBAACsF,SAAS,EAAC;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChE,OAAA;gBAAA4D,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,oBACzB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAf,QAAA,gBACnB5D,OAAA,CAACV,UAAU;gBAACqF,SAAS,EAAC;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5ChE,OAAA;gBAAA4D,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+BACzB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAf,QAAA,gBACnB5D,OAAA,CAACT,cAAc;gBAACoF,SAAS,EAAC;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDhE,OAAA;gBAAA4D,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,wBACX,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,yBACL,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,8BAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNhE,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAf,QAAA,gBACnB5D,OAAA,CAACR,OAAO;gBAACmF,SAAS,EAAC;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChE,OAAA;gBAAA4D,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,sCACJ,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,+BACb,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,kBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhE,OAAA,CAACpB,GAAG;QAACmG,EAAE,EAAE,CAAE;QAACJ,SAAS,EAAC,MAAM;QAAAf,QAAA,eAC1B5D,OAAA,CAACnB,IAAI;UAAC8F,SAAS,EAAC,OAAO;UAAAf,QAAA,gBACrB5D,OAAA,CAACnB,IAAI,CAACmG,MAAM;YAAApB,QAAA,eACV5D,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACdhE,OAAA,CAACnB,IAAI,CAACoG,IAAI;YAAArB,QAAA,gBACR5D,OAAA,CAAChB,KAAK;cAACwE,OAAO,EAAC,QAAQ;cAAAI,QAAA,gBACrB5D,OAAA;gBAAA4D,QAAA,EAAQ;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAhE,OAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9ChE,OAAA,CAACX,OAAO;gBAACsF,SAAS,EAAC;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BhE,OAAA;gBAAA4D,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACRhE,OAAA;cAAA4D,QAAA,EAAG;YAAkI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEzIhE,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxChE,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAf,QAAA,gBAC3B5D,OAAA,CAAClB,MAAM;gBAAC0E,OAAO,EAAC,iBAAiB;gBAACsB,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,QAAQ,CAAE;gBAAAkC,QAAA,EAAC;cAEzE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA,CAAClB,MAAM;gBAAC0E,OAAO,EAAC,iBAAiB;gBAACsB,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,KAAK,CAAE;gBAAAkC,QAAA,EAAC;cAEtE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvC,SAAS,KAAK,QAAQ,iBACrBzB,OAAA,CAACrB,GAAG;MAAAiF,QAAA,eACF5D,OAAA,CAACpB,GAAG;QAACsG,EAAE,EAAE,CAAE;QAACP,SAAS,EAAC,SAAS;QAAAf,QAAA,eAC7B5D,OAAA,CAACnB,IAAI;UAAA+E,QAAA,gBACH5D,OAAA,CAACnB,IAAI,CAACmG,MAAM;YAAApB,QAAA,eACV5D,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACdhE,OAAA,CAACnB,IAAI,CAACoG,IAAI;YAAArB,QAAA,GACPvC,KAAK,iBAAIrB,OAAA,CAAChB,KAAK;cAACwE,OAAO,EAAC,QAAQ;cAAAI,QAAA,EAAEvC;YAAK;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEjDhE,OAAA,CAACjB,IAAI;cAACoG,QAAQ,EAAEvC,YAAa;cAAAgB,QAAA,gBAC3B5D,OAAA,CAACrB,GAAG;gBAAAiF,QAAA,gBACF5D,OAAA,CAACpB,GAAG;kBAACmG,EAAE,EAAE,CAAE;kBAAAnB,QAAA,eACT5D,OAAA,CAACjB,IAAI,CAACqG,KAAK;oBAACT,SAAS,EAAC,MAAM;oBAAAf,QAAA,gBAC1B5D,OAAA,CAACjB,IAAI,CAACsG,KAAK;sBAAAzB,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClChE,OAAA,CAACjB,IAAI,CAACuG,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXnD,IAAI,EAAC,SAAS;sBACdC,KAAK,EAAEvB,QAAQ,CAACE,OAAQ;sBACxBwE,QAAQ,EAAEtD,iBAAkB;sBAC5BuD,WAAW,EAAC,iCAAiC;sBAC7CC,QAAQ;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNhE,OAAA,CAACpB,GAAG;kBAACmG,EAAE,EAAE,CAAE;kBAAAnB,QAAA,eACT5D,OAAA,CAACjB,IAAI,CAACqG,KAAK;oBAACT,SAAS,EAAC,MAAM;oBAAAf,QAAA,gBAC1B5D,OAAA,CAACjB,IAAI,CAACsG,KAAK;sBAAAzB,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACnChE,OAAA,CAACjB,IAAI,CAAC4G,MAAM;sBACVvD,IAAI,EAAC,UAAU;sBACfC,KAAK,EAAEvB,QAAQ,CAACG,QAAS;sBACzBuE,QAAQ,EAAEtD,iBAAkB;sBAC5BwD,QAAQ;sBAAA9B,QAAA,gBAER5D,OAAA;wBAAQqC,KAAK,EAAC,EAAE;wBAAAuB,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5ChE,OAAA;wBAAQqC,KAAK,EAAC,WAAW;wBAAAuB,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClDhE,OAAA;wBAAQqC,KAAK,EAAC,SAAS;wBAAAuB,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnDhE,OAAA;wBAAQqC,KAAK,EAAC,QAAQ;wBAAAuB,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC9ChE,OAAA;wBAAQqC,KAAK,EAAC,QAAQ;wBAAAuB,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClDhE,OAAA;wBAAQqC,KAAK,EAAC,SAAS;wBAAAuB,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhE,OAAA,CAACjB,IAAI,CAACqG,KAAK;gBAACT,SAAS,EAAC,MAAM;gBAAAf,QAAA,gBAC1B5D,OAAA,CAACjB,IAAI,CAACsG,KAAK;kBAAAzB,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjChE,OAAA,CAACjB,IAAI,CAAC4G,MAAM;kBACVvD,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACI,QAAS;kBACzBsE,QAAQ,EAAEtD,iBAAkB;kBAAA0B,QAAA,gBAE5B5D,OAAA;oBAAQqC,KAAK,EAAC,KAAK;oBAAAuB,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChChE,OAAA;oBAAQqC,KAAK,EAAC,QAAQ;oBAAAuB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtChE,OAAA;oBAAQqC,KAAK,EAAC,MAAM;oBAAAuB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClChE,OAAA;oBAAQqC,KAAK,EAAC,QAAQ;oBAAAuB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEbhE,OAAA,CAACjB,IAAI,CAACqG,KAAK;gBAACT,SAAS,EAAC,MAAM;gBAAAf,QAAA,gBAC1B5D,OAAA,CAACjB,IAAI,CAACsG,KAAK;kBAAAzB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtChE,OAAA,CAACjB,IAAI,CAACuG,OAAO;kBACXM,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE,CAAE;kBACRzD,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAEvB,QAAQ,CAACK,WAAY;kBAC5BqE,QAAQ,EAAEtD,iBAAkB;kBAC5BuD,WAAW,EAAC,yDAAyD;kBACrEC,QAAQ;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbhE,OAAA,CAACjB,IAAI,CAACqG,KAAK;gBAACT,SAAS,EAAC,MAAM;gBAAAf,QAAA,gBAC1B5D,OAAA,CAACjB,IAAI,CAACsG,KAAK;kBAAAzB,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpChE,OAAA,CAACjB,IAAI,CAACuG,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXO,QAAQ;kBACRC,MAAM,EAAC,iCAAiC;kBACxCP,QAAQ,EAAEhD;gBAAiB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACFhE,OAAA,CAACjB,IAAI,CAACiH,IAAI;kBAACrB,SAAS,EAAC,YAAY;kBAAAf,QAAA,EAAC;gBAElC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEbhE,OAAA;gBAAK2E,SAAS,EAAC,QAAQ;gBAAAf,QAAA,eACrB5D,OAAA,CAAClB,MAAM;kBACLyG,IAAI,EAAC,QAAQ;kBACb/B,OAAO,EAAC,SAAS;kBACjByC,IAAI,EAAC,IAAI;kBACTC,QAAQ,EAAEtF,UAAW;kBAAAgD,QAAA,EAEpBhD,UAAU,gBACTZ,OAAA,CAAAE,SAAA;oBAAA0D,QAAA,gBACE5D,OAAA,CAACf,OAAO;sBAACkH,SAAS,EAAC,QAAQ;sBAACF,IAAI,EAAC,IAAI;sBAACtB,SAAS,EAAC;oBAAM;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAE3D;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvC,SAAS,KAAK,SAAS,iBACtBzB,OAAA,CAACrB,GAAG;MAAAiF,QAAA,eACF5D,OAAA,CAACpB,GAAG;QAAAgF,QAAA,eACF5D,OAAA,CAACnB,IAAI;UAAA+E,QAAA,gBACH5D,OAAA,CAACnB,IAAI,CAACmG,MAAM;YAAApB,QAAA,eACV5D,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACdhE,OAAA,CAACnB,IAAI,CAACoG,IAAI;YAAArB,QAAA,EACPlD,OAAO,gBACNV,OAAA;cAAK2E,SAAS,EAAC,kBAAkB;cAAAf,QAAA,gBAC/B5D,OAAA,CAACf,OAAO;gBAACkH,SAAS,EAAC;cAAQ;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BhE,OAAA;gBAAG2E,SAAS,EAAC,MAAM;gBAAAf,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,GACJ1D,OAAO,CAAC8F,MAAM,KAAK,CAAC,gBACtBpG,OAAA;cAAK2E,SAAS,EAAC,kBAAkB;cAAAf,QAAA,gBAC/B5D,OAAA,CAACP,WAAW;gBAACwG,IAAI,EAAE,EAAG;gBAACtB,SAAS,EAAC;cAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDhE,OAAA;gBAAA4D,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBhE,OAAA;gBAAG2E,SAAS,EAAC,YAAY;gBAAAf,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5EhE,OAAA,CAAClB,MAAM;gBAAC0E,OAAO,EAAC,SAAS;gBAACsB,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,QAAQ,CAAE;gBAAAkC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENhE,OAAA,CAACd,KAAK;cAACmH,UAAU;cAACC,KAAK;cAAA1C,QAAA,gBACrB5D,OAAA;gBAAA4D,QAAA,eACE5D,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAA4D,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBhE,OAAA;oBAAA4D,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhE,OAAA;gBAAA4D,QAAA,EACGtD,OAAO,CAACiG,GAAG,CAAEC,MAAM,iBAClBxG,OAAA;kBAAA4D,QAAA,gBACE5D,OAAA;oBAAA4D,QAAA,eACE5D,OAAA;sBAAA4D,QAAA,GAAQ,GAAC,EAAC4C,MAAM,CAACC,QAAQ;oBAAA;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACLhE,OAAA;oBAAA4D,QAAA,EAAK4C,MAAM,CAACxF;kBAAO;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBhE,OAAA;oBAAA4D,QAAA,eACE5D,OAAA,CAACb,KAAK;sBAACwE,EAAE,EAAC,OAAO;sBAACF,IAAI,EAAC,MAAM;sBAAAG,QAAA,EAC1B4C,MAAM,CAACvF;oBAAQ;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLhE,OAAA;oBAAA4D,QAAA,EAAKK,gBAAgB,CAACuC,MAAM,CAACtF,QAAQ;kBAAC;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5ChE,OAAA;oBAAA4D,QAAA,EAAKP,cAAc,CAACmD,MAAM,CAAClD,MAAM;kBAAC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxChE,OAAA;oBAAA4D,QAAA,EAAKO,UAAU,CAACqC,MAAM,CAACE,SAAS;kBAAC;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvChE,OAAA;oBAAA4D,QAAA,EAAKO,UAAU,CAACqC,MAAM,CAACG,SAAS;kBAAC;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAbhCwC,MAAM,CAACI,GAAG;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcf,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvC,SAAS,KAAK,KAAK,iBAClBzB,OAAA,CAACrB,GAAG;MAAAiF,QAAA,eACF5D,OAAA,CAACpB,GAAG;QAACsG,EAAE,EAAE,EAAG;QAACP,SAAS,EAAC,SAAS;QAAAf,QAAA,eAC9B5D,OAAA,CAACnB,IAAI;UAAA+E,QAAA,gBACH5D,OAAA,CAACnB,IAAI,CAACmG,MAAM;YAAApB,QAAA,eACV5D,OAAA;cAAI2E,SAAS,EAAC,MAAM;cAAAf,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACdhE,OAAA,CAACnB,IAAI,CAACoG,IAAI;YAAArB,QAAA,gBACR5D,OAAA,CAACZ,SAAS;cAAAwE,QAAA,EACPW,OAAO,CAACgC,GAAG,CAAC,CAACM,GAAG,EAAEC,KAAK,kBACtB9G,OAAA,CAACZ,SAAS,CAAC2H,IAAI;gBAACC,QAAQ,EAAEF,KAAK,CAACG,QAAQ,CAAC,CAAE;gBAAArD,QAAA,gBACzC5D,OAAA,CAACZ,SAAS,CAAC4F,MAAM;kBAAApB,QAAA,EAAEiD,GAAG,CAACrC;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmB,CAAC,eACnDhE,OAAA,CAACZ,SAAS,CAAC6F,IAAI;kBAAArB,QAAA,EAAEiD,GAAG,CAACpC;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC;cAAA,GAFE8C,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGtC,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZhE,OAAA;cAAK2E,SAAS,EAAC,kBAAkB;cAAAf,QAAA,gBAC/B5D,OAAA;gBAAG2E,SAAS,EAAC,YAAY;gBAAAf,QAAA,EAAC;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEhE,OAAA,CAAClB,MAAM;gBAAC0E,OAAO,EAAC,SAAS;gBAACsB,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAAC,QAAQ,CAAE;gBAAAkC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAAC5D,EAAA,CAldID,cAAc;EAAA,QACDP,OAAO;AAAA;AAAAsH,EAAA,GADpB/G,cAAc;AAodpB,eAAeA,cAAc;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}