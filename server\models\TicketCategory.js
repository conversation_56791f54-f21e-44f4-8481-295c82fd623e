const mongoose = require('mongoose');

const ticketCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Ticket category name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: false // Optional - ticket categories can be independent
  },
  isActive: {
    type: Boolean,
    default: true
  },
  color: {
    type: String,
    default: '#007bff'
  },
  icon: {
    type: String,
    default: 'fas fa-ticket-alt'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  autoAssignment: {
    type: Boolean,
    default: false
  },
  defaultAssignee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  slaHours: {
    type: Number,
    default: 24 // Default SLA in hours
  },
  escalationHours: {
    type: Number,
    default: 48 // Hours before escalation
  },
  tags: [String],
  sortOrder: {
    type: Number,
    default: 0
  },
  // Statistics
  stats: {
    totalTickets: {
      type: Number,
      default: 0
    },
    openTickets: {
      type: Number,
      default: 0
    },
    resolvedTickets: {
      type: Number,
      default: 0
    },
    averageResolutionTime: {
      type: Number,
      default: 0 // in hours
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
ticketCategorySchema.index({ name: 1 });
ticketCategorySchema.index({ category: 1 });
ticketCategorySchema.index({ isActive: 1 });
ticketCategorySchema.index({ priority: 1 });
ticketCategorySchema.index({ sortOrder: 1 });

// Compound index for category and name uniqueness (if category is provided)
ticketCategorySchema.index({ category: 1, name: 1 }, { 
  unique: true,
  partialFilterExpression: { category: { $exists: true } }
});

// Virtual for tickets count
ticketCategorySchema.virtual('ticketsCount', {
  ref: 'Ticket',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Static method to find by category
ticketCategorySchema.statics.findByCategory = function(categoryId, activeOnly = true) {
  const query = categoryId ? { category: categoryId } : {};
  if (activeOnly) query.isActive = true;
  
  return this.find(query)
    .populate('category', 'name type')
    .populate('defaultAssignee', 'firstName lastName')
    .sort({ sortOrder: 1, name: 1 });
};

// Static method for search
ticketCategorySchema.statics.search = function(searchTerm, categoryId = null) {
  const query = {
    $or: [
      { name: { $regex: searchTerm, $options: 'i' } },
      { description: { $regex: searchTerm, $options: 'i' } },
      { tags: { $in: [new RegExp(searchTerm, 'i')] } }
    ]
  };
  
  if (categoryId) {
    query.category = categoryId;
  }
  
  return this.find(query)
    .populate('category', 'name type')
    .populate('defaultAssignee', 'firstName lastName')
    .sort({ name: 1 });
};

// Method to update statistics
ticketCategorySchema.methods.updateStats = async function() {
  const Ticket = mongoose.model('Ticket');
  
  const stats = await Ticket.aggregate([
    { $match: { category: this._id } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        open: {
          $sum: {
            $cond: [{ $in: ['$status', ['open', 'in-progress', 'pending']] }, 1, 0]
          }
        },
        resolved: {
          $sum: {
            $cond: [{ $in: ['$status', ['resolved', 'closed']] }, 1, 0]
          }
        },
        avgResolutionTime: {
          $avg: {
            $cond: [
              { $in: ['$status', ['resolved', 'closed']] },
              {
                $divide: [
                  { $subtract: ['$updatedAt', '$createdAt'] },
                  3600000 // Convert to hours
                ]
              },
              null
            ]
          }
        }
      }
    }
  ]);
  
  const data = stats[0] || { total: 0, open: 0, resolved: 0, avgResolutionTime: 0 };
  
  this.stats = {
    totalTickets: data.total,
    openTickets: data.open,
    resolvedTickets: data.resolved,
    averageResolutionTime: Math.round(data.avgResolutionTime || 0)
  };
  
  return this.save();
};

// Pre-save middleware
ticketCategorySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.name = this.name.trim();
  }
  next();
});

// Post-save middleware to update parent category stats if needed
ticketCategorySchema.post('save', async function(doc) {
  if (doc.category) {
    try {
      const Category = mongoose.model('Category');
      const category = await Category.findById(doc.category);
      if (category && typeof category.updateStats === 'function') {
        await category.updateStats();
      }
    } catch (error) {
      console.error('Error updating parent category stats:', error);
    }
  }
});

module.exports = mongoose.model('TicketCategory', ticketCategorySchema);
