import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useAuth } from '../../context/AuthContext';
import { reportsAPI } from '../../services/api';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalPolicyHolders: 0,
    pendingPolicies: 0,
    approvedPolicies: 0,
    deniedPolicies: 0,
    totalTickets: 0,
    pendingTickets: 0,
    closedTickets: 0,
    activeFields: 0,
    totalEmployees: 0,
    totalCustomers: 0,
    monthlyRevenue: 0,
    systemAlerts: 0,
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await reportsAPI.getDashboardStats();
        if (response.success) {
          const { overview } = response.data;
          setStats({
            totalPolicyHolders: overview.totalUsers || 0,
            pendingPolicies: overview.pendingPolicies || 0,
            approvedPolicies: overview.activePolicies || 0,
            deniedPolicies: 0, // Calculate from total - active - pending
            totalTickets: overview.totalTickets || 0,
            pendingTickets: overview.openTickets || 0,
            closedTickets: (overview.totalTickets || 0) - (overview.openTickets || 0),
            activeFields: 12, // Static for now
            totalEmployees: overview.totalEmployees || 0,
            totalCustomers: overview.totalCustomers || 0,
            monthlyRevenue: overview.monthlyRevenue || 0,
            systemAlerts: 3, // Static for now
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Fallback to mock data
        setStats({
          totalPolicyHolders: 1250,
          pendingPolicies: 45,
          approvedPolicies: 980,
          deniedPolicies: 125,
          totalTickets: 180,
          pendingTickets: 35,
          closedTickets: 145,
          activeFields: 12,
          totalEmployees: 25,
          totalCustomers: 1200,
          monthlyRevenue: 125000,
          systemAlerts: 3,
        });
      }
    };

    fetchDashboardData();
  }, []);

  const cardData = [
    { label: 'Total Customers', count: stats.totalCustomers, icon: 'bi-people-fill', color: 'primary' },
    { label: 'Total Employees', count: stats.totalEmployees, icon: 'bi-person-badge-fill', color: 'info' },
    { label: 'Policy Holders', count: stats.totalPolicyHolders, icon: 'bi-shield-fill-check', color: 'success' },
    { label: 'Monthly Revenue', count: `$${stats.monthlyRevenue.toLocaleString()}`, icon: 'bi-currency-dollar', color: 'warning' },
    { label: 'Pending Policies', count: stats.pendingPolicies, icon: 'bi-hourglass-split', color: 'warning' },
    { label: 'Approved Policies', count: stats.approvedPolicies, icon: 'bi-check-circle-fill', color: 'success' },
    { label: 'Support Tickets', count: stats.totalTickets, icon: 'bi-ticket-detailed-fill', color: 'info' },
    { label: 'System Alerts', count: stats.systemAlerts, icon: 'bi-exclamation-triangle-fill', color: 'danger' },
  ];

  const quickActions = [
    { title: 'Manage Users', description: 'Add, edit, or remove users', link: '/users', icon: 'bi-people' },
    { title: 'System Settings', description: 'Configure system parameters', link: '/system-settings', icon: 'bi-gear' },
    { title: 'View Reports', description: 'Generate and view reports', link: '/report-tool', icon: 'bi-graph-up' },
    { title: 'Manage Staff', description: 'Employee management', link: '/staff', icon: 'bi-person-workspace' },
  ];

  return (
    <Container className="mt-4">
      <div className="mb-4">
        <h2 className="fw-bold">Admin Dashboard</h2>
        <p className="lead">
          Welcome back, <span className="text-primary fw-semibold">{user.name}</span>! 
          Here's your system overview.
        </p>
      </div>

      {/* Stats Cards */}
      <Row className="g-4 mb-5">
        {cardData.map((item, idx) => (
          <Col lg={3} md={6} key={idx}>
            <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>
              <Card.Body className="d-flex align-items-center justify-content-between">
                <div>
                  <h6 className="card-title mb-2">{item.label}</h6>
                  <h4 className="fw-bold">{item.count}</h4>
                </div>
                <i className={`bi ${item.icon} fs-2`}></i>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Quick Actions */}
      <Row>
        <Col>
          <h4 className="fw-bold mb-4">Quick Actions</h4>
        </Col>
      </Row>
      <Row className="g-4 mb-5">
        {quickActions.map((action, idx) => (
          <Col lg={3} md={6} key={idx}>
            <Card className="shadow-sm border-0 h-100">
              <Card.Body className="text-center">
                <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>
                <h5 className="fw-bold">{action.title}</h5>
                <p className="text-muted mb-3">{action.description}</p>
                <Button variant="primary" href={action.link} className="w-100">
                  Access
                </Button>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Recent Activity */}
      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="fw-bold mb-0">Recent System Activity</h5>
            </Card.Header>
            <Card.Body>
              <div className="list-group list-group-flush">
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">New user registration</h6>
                    <small className="text-muted">John Doe registered as customer</small>
                  </div>
                  <small className="text-muted">2 hours ago</small>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Policy approved</h6>
                    <small className="text-muted">Life insurance policy #LP-2024-001</small>
                  </div>
                  <small className="text-muted">4 hours ago</small>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Support ticket resolved</h6>
                    <small className="text-muted">Ticket #ST-2024-045 closed</small>
                  </div>
                  <small className="text-muted">6 hours ago</small>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={4}>
          <Card className="shadow-sm">
            <Card.Header>
              <h5 className="fw-bold mb-0">System Health</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Server Status</span>
                  <span className="badge bg-success">Online</span>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Database</span>
                  <span className="badge bg-success">Connected</span>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Backup Status</span>
                  <span className="badge bg-warning">Pending</span>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between">
                  <span>Security Alerts</span>
                  <span className="badge bg-danger">{stats.systemAlerts}</span>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default AdminDashboard;
