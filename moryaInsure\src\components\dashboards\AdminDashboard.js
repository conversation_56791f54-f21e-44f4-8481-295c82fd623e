import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Table, Modal, Form, Badge } from 'react-bootstrap';
import { useAuth } from '../../context/AuthContext';
import { reportsAPI, userAPI } from '../../services/api';
import { FaEdit, FaTrash, FaPlus, FaEye } from 'react-icons/fa';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalPolicyHolders: 0,
    pendingPolicies: 0,
    approvedPolicies: 0,
    deniedPolicies: 0,
    totalTickets: 0,
    pendingTickets: 0,
    closedTickets: 0,
    activeFields: 0,
    totalEmployees: 0,
    totalCustomers: 0,
    monthlyRevenue: 0,
    systemAlerts: 0,
  });

  // User management state
  const [users, setUsers] = useState([]);
  const [staff, setStaff] = useState([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userForm, setUserForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: 'customer',
    phone: ''
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await reportsAPI.getDashboardStats();
        if (response.success) {
          const { overview } = response.data;
          setStats({
            totalPolicyHolders: overview.totalUsers || 0,
            pendingPolicies: overview.pendingPolicies || 0,
            approvedPolicies: overview.activePolicies || 0,
            deniedPolicies: 0, // Calculate from total - active - pending
            totalTickets: overview.totalTickets || 0,
            pendingTickets: overview.openTickets || 0,
            closedTickets: (overview.totalTickets || 0) - (overview.openTickets || 0),
            activeFields: 12, // Static for now
            totalEmployees: overview.totalEmployees || 0,
            totalCustomers: overview.totalCustomers || 0,
            monthlyRevenue: overview.monthlyRevenue || 0,
            systemAlerts: 3, // Static for now
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    };

    fetchDashboardData();
    fetchUsers();
    fetchStaff();
  }, []);

  // Fetch users data
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getUsers({ role: 'customer', limit: 10 });
      if (response.success) {
        setUsers(response.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch staff data
  const fetchStaff = async () => {
    try {
      const response = await userAPI.getUsers({ role: 'employee', limit: 10 });
      if (response.success) {
        setStaff(response.data.users);
      }
    } catch (error) {
      console.error('Error fetching staff:', error);
    }
  };

  // Handle user form
  const handleUserFormChange = (e) => {
    setUserForm({
      ...userForm,
      [e.target.name]: e.target.value
    });
  };

  // Create or update user
  const handleUserSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (selectedUser) {
        // Update user
        await userAPI.updateUser(selectedUser._id, userForm);
      } else {
        // Create user
        await userAPI.createUser(userForm);
      }
      setShowUserModal(false);
      setSelectedUser(null);
      setUserForm({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        role: 'customer',
        phone: ''
      });
      fetchUsers();
      fetchStaff();
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setLoading(false);
    }
  };

  // Delete user
  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await userAPI.deleteUser(userId);
        fetchUsers();
        fetchStaff();
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  // Edit user
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setUserForm({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      password: '',
      role: user.role,
      phone: user.phone || ''
    });
    setShowUserModal(true);
  };

  const cardData = [
    {
      label: 'Total Customers',
      count: users.length || stats.totalCustomers,
      icon: 'bi-people-fill',
      color: 'primary',
      subtitle: `${users.filter(u => u.isActive).length} active`
    },
    {
      label: 'Total Employees',
      count: staff.length || stats.totalEmployees,
      icon: 'bi-person-badge-fill',
      color: 'info',
      subtitle: `${staff.filter(s => s.isActive).length} active`
    },
    {
      label: 'Policy Holders',
      count: stats.totalPolicyHolders,
      icon: 'bi-shield-fill-check',
      color: 'success',
      subtitle: 'With active policies'
    },
    {
      label: 'Monthly Revenue',
      count: `$${stats.monthlyRevenue.toLocaleString()}`,
      icon: 'bi-currency-dollar',
      color: 'warning',
      subtitle: 'This month'
    },
    {
      label: 'Pending Policies',
      count: stats.pendingPolicies,
      icon: 'bi-hourglass-split',
      color: 'warning',
      subtitle: 'Awaiting review'
    },
    {
      label: 'Approved Policies',
      count: stats.approvedPolicies,
      icon: 'bi-check-circle-fill',
      color: 'success',
      subtitle: 'Active policies'
    },
    {
      label: 'Support Tickets',
      count: stats.totalTickets,
      icon: 'bi-ticket-detailed-fill',
      color: 'info',
      subtitle: `${stats.pendingTickets} pending`
    },
    {
      label: 'System Alerts',
      count: stats.systemAlerts,
      icon: 'bi-exclamation-triangle-fill',
      color: 'danger',
      subtitle: 'Require attention'
    },
  ];

  const quickActions = [
    { title: 'Manage Users', description: 'Add, edit, or remove users', link: '/users', icon: 'bi-people' },
    { title: 'System Settings', description: 'Configure system parameters', link: '/system-settings', icon: 'bi-gear' },
    { title: 'View Reports', description: 'Generate and view reports', link: '/report-tool', icon: 'bi-graph-up' },
    { title: 'Manage Staff', description: 'Employee management', link: '/staff', icon: 'bi-person-workspace' },
  ];

  return (
    <Container fluid className="py-4">
      <Row className="mb-4">
        <Col>
          <h2 className="text-primary mb-0">Admin Dashboard</h2>
          <p className="text-muted">Welcome back, {user?.firstName}! Here's your system overview.</p>
        </Col>
      </Row>

      {/* Navigation Tabs */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex gap-2">
            <Button
              variant={activeTab === 'overview' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </Button>
            <Button
              variant={activeTab === 'users' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('users')}
            >
              Users Management
            </Button>
            <Button
              variant={activeTab === 'staff' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('staff')}
            >
              Staff Management
            </Button>
          </div>
        </Col>
      </Row>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <>
          {/* Stats Cards */}
          <Row className="g-4 mb-5">
            {cardData.map((item, idx) => (
              <Col lg={3} md={6} key={idx}>
                <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>
                  <Card.Body className="d-flex align-items-center justify-content-between">
                    <div>
                      <h6 className="card-title mb-1">{item.label}</h6>
                      <h4 className="fw-bold mb-1">{item.count}</h4>
                      {item.subtitle && (
                        <small className="opacity-75">{item.subtitle}</small>
                      )}
                    </div>
                    <i className={`bi ${item.icon} fs-2`}></i>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Quick Actions */}
          <Row>
            <Col>
              <h4 className="fw-bold mb-4">Quick Actions</h4>
            </Col>
          </Row>
          <Row className="g-4 mb-5">
            {quickActions.map((action, idx) => (
              <Col lg={3} md={6} key={idx}>
                <Card className="shadow-sm border-0 h-100">
                  <Card.Body className="text-center">
                    <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>
                    <h5 className="fw-bold">{action.title}</h5>
                    <p className="text-muted mb-3">{action.description}</p>
                    <Button
                      variant="primary"
                      className="w-100"
                      onClick={() => {
                        if (action.title === 'Manage Users') setActiveTab('users');
                        else if (action.title === 'Manage Staff') setActiveTab('staff');
                      }}
                    >
                      Access
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Employee/Staff and Customer Summary */}
          <Row className="mb-5">
            <Col lg={6}>
              <Card className="shadow-sm">
                <Card.Header className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">
                    <i className="bi bi-people-fill text-primary me-2"></i>
                    Staff Overview ({staff.length})
                  </h5>
                  <Button
                    size="sm"
                    variant="outline-primary"
                    onClick={() => setActiveTab('staff')}
                  >
                    View All
                  </Button>
                </Card.Header>
                <Card.Body>
                  {loading ? (
                    <div className="text-center py-3">
                      <div className="spinner-border spinner-border-sm" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  ) : staff.length > 0 ? (
                    <div className="list-group list-group-flush">
                      {staff.slice(0, 5).map((employee) => (
                        <div key={employee._id} className="list-group-item d-flex justify-content-between align-items-center px-0">
                          <div>
                            <h6 className="mb-1">{employee.firstName} {employee.lastName}</h6>
                            <small className="text-muted">{employee.email}</small>
                          </div>
                          <Badge bg={employee.isActive ? 'success' : 'danger'}>
                            {employee.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                      ))}
                      {staff.length > 5 && (
                        <div className="list-group-item text-center px-0">
                          <small className="text-muted">
                            +{staff.length - 5} more employees
                          </small>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <i className="bi bi-person-plus text-muted fs-1 mb-2"></i>
                      <p className="text-muted mb-0">No staff members found</p>
                      <Button
                        size="sm"
                        variant="outline-primary"
                        className="mt-2"
                        onClick={() => setActiveTab('staff')}
                      >
                        Add First Employee
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
            <Col lg={6}>
              <Card className="shadow-sm">
                <Card.Header className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">
                    <i className="bi bi-person-check-fill text-success me-2"></i>
                    Customer Overview ({users.length})
                  </h5>
                  <Button
                    size="sm"
                    variant="outline-success"
                    onClick={() => setActiveTab('users')}
                  >
                    View All
                  </Button>
                </Card.Header>
                <Card.Body>
                  {loading ? (
                    <div className="text-center py-3">
                      <div className="spinner-border spinner-border-sm" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  ) : users.length > 0 ? (
                    <div className="list-group list-group-flush">
                      {users.slice(0, 5).map((customer) => (
                        <div key={customer._id} className="list-group-item d-flex justify-content-between align-items-center px-0">
                          <div>
                            <h6 className="mb-1">{customer.firstName} {customer.lastName}</h6>
                            <small className="text-muted">{customer.email}</small>
                          </div>
                          <Badge bg={customer.isActive ? 'success' : 'danger'}>
                            {customer.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                      ))}
                      {users.length > 5 && (
                        <div className="list-group-item text-center px-0">
                          <small className="text-muted">
                            +{users.length - 5} more customers
                          </small>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <i className="bi bi-person-plus text-muted fs-1 mb-2"></i>
                      <p className="text-muted mb-0">No customers found</p>
                      <Button
                        size="sm"
                        variant="outline-success"
                        className="mt-2"
                        onClick={() => setActiveTab('users')}
                      >
                        Add First Customer
                      </Button>
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {/* Users Management Tab */}
      {activeTab === 'users' && (
        <Row>
          <Col>
            <Card className="shadow-sm">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Customer Management</h5>
                <Button
                  variant="primary"
                  onClick={() => {
                    setSelectedUser(null);
                    setUserForm({
                      firstName: '',
                      lastName: '',
                      email: '',
                      password: '',
                      role: 'customer',
                      phone: ''
                    });
                    setShowUserModal(true);
                  }}
                >
                  <FaPlus className="me-2" />
                  Add Customer
                </Button>
              </Card.Header>
              <Card.Body>
                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-3 d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="mb-0">Total Customers: <span className="text-primary">{users.length}</span></h6>
                        <small className="text-muted">
                          Active: {users.filter(u => u.isActive).length} |
                          Inactive: {users.filter(u => !u.isActive).length}
                        </small>
                      </div>
                    </div>
                    <Table responsive striped hover>
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Status</th>
                        <th>Joined</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.length > 0 ? users.map((user) => (
                        <tr key={user._id}>
                          <td>
                            <div>
                              <strong>{user.firstName} {user.lastName}</strong>
                              {user.isEmailVerified && (
                                <i className="bi bi-patch-check-fill text-success ms-1" title="Email Verified"></i>
                              )}
                            </div>
                          </td>
                          <td>{user.email}</td>
                          <td>{user.phone || <span className="text-muted">Not provided</span>}</td>
                          <td>
                            <Badge bg={user.isActive ? 'success' : 'danger'}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </td>
                          <td>
                            <div>
                              {new Date(user.createdAt).toLocaleDateString()}
                              <br />
                              <small className="text-muted">
                                {new Date(user.createdAt).toLocaleTimeString()}
                              </small>
                            </div>
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <Button
                                size="sm"
                                variant="outline-primary"
                                onClick={() => handleEditUser(user)}
                                title="Edit User"
                              >
                                <FaEdit />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline-danger"
                                onClick={() => handleDeleteUser(user._id)}
                                title="Delete User"
                              >
                                <FaTrash />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="6" className="text-center py-4">
                            <i className="bi bi-person-plus text-muted fs-1 mb-2 d-block"></i>
                            <p className="text-muted mb-0">No customers found</p>
                            <small className="text-muted">Add your first customer to get started</small>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                  </>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Staff Management Tab */}
      {activeTab === 'staff' && (
        <Row>
          <Col>
            <Card className="shadow-sm">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Staff Management</h5>
                <Button
                  variant="primary"
                  onClick={() => {
                    setSelectedUser(null);
                    setUserForm({
                      firstName: '',
                      lastName: '',
                      email: '',
                      password: '',
                      role: 'employee',
                      phone: ''
                    });
                    setShowUserModal(true);
                  }}
                >
                  <FaPlus className="me-2" />
                  Add Employee
                </Button>
              </Card.Header>
              <Card.Body>
                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="mb-3 d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="mb-0">Total Staff: <span className="text-info">{staff.length}</span></h6>
                        <small className="text-muted">
                          Active: {staff.filter(s => s.isActive).length} |
                          Inactive: {staff.filter(s => !s.isActive).length}
                        </small>
                      </div>
                    </div>
                    <Table responsive striped hover>
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Phone</th>
                          <th>Status</th>
                          <th>Joined</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {staff.length > 0 ? staff.map((employee) => (
                          <tr key={employee._id}>
                            <td>
                              <div>
                                <strong>{employee.firstName} {employee.lastName}</strong>
                                {employee.isEmailVerified && (
                                  <i className="bi bi-patch-check-fill text-success ms-1" title="Email Verified"></i>
                                )}
                                <br />
                                <small className="text-muted">Employee</small>
                              </div>
                            </td>
                            <td>{employee.email}</td>
                            <td>{employee.phone || <span className="text-muted">Not provided</span>}</td>
                            <td>
                              <Badge bg={employee.isActive ? 'success' : 'danger'}>
                                {employee.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </td>
                            <td>
                              <div>
                                {new Date(employee.createdAt).toLocaleDateString()}
                                <br />
                                <small className="text-muted">
                                  {new Date(employee.createdAt).toLocaleTimeString()}
                                </small>
                              </div>
                            </td>
                            <td>
                              <div className="d-flex gap-1">
                                <Button
                                  size="sm"
                                  variant="outline-primary"
                                  onClick={() => handleEditUser(employee)}
                                  title="Edit Employee"
                                >
                                  <FaEdit />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline-danger"
                                  onClick={() => handleDeleteUser(employee._id)}
                                  title="Delete Employee"
                                >
                                  <FaTrash />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        )) : (
                          <tr>
                            <td colSpan="6" className="text-center py-4">
                              <i className="bi bi-person-workspace text-muted fs-1 mb-2 d-block"></i>
                              <p className="text-muted mb-0">No employees found</p>
                              <small className="text-muted">Add your first employee to get started</small>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </Table>
                  </>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* User Modal */}
      <Modal show={showUserModal} onHide={() => setShowUserModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {selectedUser ? 'Edit User' : 'Add New User'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleUserSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="firstName"
                    value={userForm.firstName}
                    onChange={handleUserFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="lastName"
                    value={userForm.lastName}
                    onChange={handleUserFormChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={userForm.email}
                    onChange={handleUserFormChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={userForm.phone}
                    onChange={handleUserFormChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Role</Form.Label>
                  <Form.Select
                    name="role"
                    value={userForm.role}
                    onChange={handleUserFormChange}
                    required
                  >
                    <option value="customer">Customer</option>
                    <option value="employee">Employee</option>
                    <option value="admin">Admin</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Password {selectedUser && '(leave blank to keep current)'}</Form.Label>
                  <Form.Control
                    type="password"
                    name="password"
                    value={userForm.password}
                    onChange={handleUserFormChange}
                    required={!selectedUser}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowUserModal(false)}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? 'Saving...' : selectedUser ? 'Update User' : 'Create User'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default AdminDashboard;
