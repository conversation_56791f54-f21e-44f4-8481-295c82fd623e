{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\contexts\\\\RealtimeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport socketService from '../services/socketService';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RealtimeContext = /*#__PURE__*/createContext();\nexport const useRealtime = () => {\n  _s();\n  const context = useContext(RealtimeContext);\n  if (!context) {\n    throw new Error('useRealtime must be used within a RealtimeProvider');\n  }\n  return context;\n};\n_s(useRealtime, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const RealtimeProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [realtimeData, setRealtimeData] = useState({\n    tasks: [],\n    policies: [],\n    users: [],\n    categories: [],\n    subCategories: [],\n    policyHolders: [],\n    tickets: [],\n    claims: []\n  });\n\n  // Connect to socket when user is authenticated\n  useEffect(() => {\n    if (user && token) {\n      console.log('🔌 Connecting to WebSocket...');\n      socketService.connect(token);\n\n      // Listen for connection status\n      socketService.on('connect', () => {\n        setIsConnected(true);\n        console.log('✅ Real-time connection established');\n      });\n      socketService.on('disconnect', () => {\n        setIsConnected(false);\n        console.log('❌ Real-time connection lost');\n      });\n\n      // Join user-specific room\n      socketService.joinRoom(`user_${user._id}`);\n\n      // Join role-specific room\n      socketService.joinRoom(`role_${user.role}`);\n      return () => {\n        socketService.disconnect();\n        setIsConnected(false);\n      };\n    }\n  }, [user, token]);\n\n  // Real-time event handlers\n  useEffect(() => {\n    if (!isConnected) return;\n\n    // Task updates\n    const handleTaskUpdate = data => {\n      console.log('📋 Task update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        tasks: updateArrayData(prev.tasks, data)\n      }));\n    };\n\n    // Policy updates\n    const handlePolicyUpdate = data => {\n      console.log('📄 Policy update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        policies: updateArrayData(prev.policies, data)\n      }));\n    };\n\n    // User updates\n    const handleUserUpdate = data => {\n      console.log('👤 User update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        users: updateArrayData(prev.users, data)\n      }));\n    };\n\n    // Category updates\n    const handleCategoryUpdate = data => {\n      console.log('📂 Category update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        categories: updateArrayData(prev.categories, data)\n      }));\n    };\n\n    // SubCategory updates\n    const handleSubCategoryUpdate = data => {\n      console.log('📁 SubCategory update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        subCategories: updateArrayData(prev.subCategories, data)\n      }));\n    };\n\n    // Policy Holder updates\n    const handlePolicyHolderUpdate = data => {\n      console.log('👥 Policy Holder update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        policyHolders: updateArrayData(prev.policyHolders, data)\n      }));\n    };\n\n    // Ticket updates\n    const handleTicketUpdate = data => {\n      console.log('🎫 Ticket update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        tickets: updateArrayData(prev.tickets, data)\n      }));\n    };\n\n    // Claim updates\n    const handleClaimUpdate = data => {\n      console.log('💰 Claim update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        claims: updateArrayData(prev.claims, data)\n      }));\n    };\n\n    // Notification handler\n    const handleNotification = notification => {\n      console.log('🔔 Notification received:', notification);\n      setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50\n    };\n\n    // Register all event listeners\n    socketService.onTaskUpdate(handleTaskUpdate);\n    socketService.onPolicyUpdate(handlePolicyUpdate);\n    socketService.onUserUpdate(handleUserUpdate);\n    socketService.onCategoryUpdate(handleCategoryUpdate);\n    socketService.onSubCategoryUpdate(handleSubCategoryUpdate);\n    socketService.onPolicyHolderUpdate(handlePolicyHolderUpdate);\n    socketService.onTicketUpdate(handleTicketUpdate);\n    socketService.onClaimUpdate(handleClaimUpdate);\n    socketService.onNotification(handleNotification);\n    return () => {\n      socketService.removeAllListeners();\n    };\n  }, [isConnected]);\n\n  // Helper function to update array data\n  const updateArrayData = (currentArray, updateData) => {\n    const {\n      type,\n      data\n    } = updateData;\n    switch (type) {\n      case 'created':\n        return [...currentArray, data];\n      case 'updated':\n        return currentArray.map(item => item._id === data._id ? {\n          ...item,\n          ...data\n        } : item);\n      case 'deleted':\n        return currentArray.filter(item => item._id !== data._id);\n      default:\n        return currentArray;\n    }\n  };\n\n  // Methods to trigger real-time updates\n  const triggerRefresh = useCallback(dataType => {\n    socketService.emit('request_refresh', {\n      dataType\n    });\n  }, []);\n  const markNotificationAsRead = useCallback(notificationId => {\n    setNotifications(prev => prev.map(notif => notif.id === notificationId ? {\n      ...notif,\n      read: true\n    } : notif));\n  }, []);\n  const clearNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Subscribe to specific data updates\n  const subscribeToUpdates = useCallback((dataType, callback) => {\n    const eventName = `${dataType}_updated`;\n    socketService.on(eventName, callback);\n    return () => {\n      socketService.off(eventName, callback);\n    };\n  }, []);\n  const value = {\n    isConnected,\n    realtimeData,\n    notifications,\n    triggerRefresh,\n    markNotificationAsRead,\n    clearNotifications,\n    subscribeToUpdates,\n    socketService\n  };\n  return /*#__PURE__*/_jsxDEV(RealtimeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n};\n_s2(RealtimeProvider, \"AC/HyBOmKQ5UHXcH4I1GntYXIkg=\", false, function () {\n  return [useAuth];\n});\n_c = RealtimeProvider;\nexport default RealtimeContext;\nvar _c;\n$RefreshReg$(_c, \"RealtimeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useCallback", "socketService", "useAuth", "jsxDEV", "_jsxDEV", "RealtimeContext", "useRealtime", "_s", "context", "Error", "RealtimeProvider", "children", "_s2", "user", "token", "isConnected", "setIsConnected", "notifications", "setNotifications", "realtimeData", "setRealtimeData", "tasks", "policies", "users", "categories", "subCategories", "policyHolders", "tickets", "claims", "console", "log", "connect", "on", "joinRoom", "_id", "role", "disconnect", "handleTaskUpdate", "data", "prev", "updateArrayData", "handlePolicyUpdate", "handleUserUpdate", "handleCategoryUpdate", "handleSubCategoryUpdate", "handlePolicyHolderUpdate", "handleTicketUpdate", "handleClaimUpdate", "handleNotification", "notification", "slice", "onTaskUpdate", "onPolicyUpdate", "onUserUpdate", "onCategoryUpdate", "onSubCategoryUpdate", "onPolicyHolderUpdate", "onTicketUpdate", "onClaimUpdate", "onNotification", "removeAllListeners", "currentArray", "updateData", "type", "map", "item", "filter", "triggerRefresh", "dataType", "emit", "markNotificationAsRead", "notificationId", "notif", "id", "read", "clearNotifications", "subscribeToUpdates", "callback", "eventName", "off", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/contexts/RealtimeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport socketService from '../services/socketService';\nimport { useAuth } from './AuthContext';\n\nconst RealtimeContext = createContext();\n\nexport const useRealtime = () => {\n  const context = useContext(RealtimeContext);\n  if (!context) {\n    throw new Error('useRealtime must be used within a RealtimeProvider');\n  }\n  return context;\n};\n\nexport const RealtimeProvider = ({ children }) => {\n  const { user, token } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [realtimeData, setRealtimeData] = useState({\n    tasks: [],\n    policies: [],\n    users: [],\n    categories: [],\n    subCategories: [],\n    policyHolders: [],\n    tickets: [],\n    claims: []\n  });\n\n  // Connect to socket when user is authenticated\n  useEffect(() => {\n    if (user && token) {\n      console.log('🔌 Connecting to WebSocket...');\n      socketService.connect(token);\n      \n      // Listen for connection status\n      socketService.on('connect', () => {\n        setIsConnected(true);\n        console.log('✅ Real-time connection established');\n      });\n\n      socketService.on('disconnect', () => {\n        setIsConnected(false);\n        console.log('❌ Real-time connection lost');\n      });\n\n      // Join user-specific room\n      socketService.joinRoom(`user_${user._id}`);\n      \n      // Join role-specific room\n      socketService.joinRoom(`role_${user.role}`);\n\n      return () => {\n        socketService.disconnect();\n        setIsConnected(false);\n      };\n    }\n  }, [user, token]);\n\n  // Real-time event handlers\n  useEffect(() => {\n    if (!isConnected) return;\n\n    // Task updates\n    const handleTaskUpdate = (data) => {\n      console.log('📋 Task update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        tasks: updateArrayData(prev.tasks, data)\n      }));\n    };\n\n    // Policy updates\n    const handlePolicyUpdate = (data) => {\n      console.log('📄 Policy update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        policies: updateArrayData(prev.policies, data)\n      }));\n    };\n\n    // User updates\n    const handleUserUpdate = (data) => {\n      console.log('👤 User update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        users: updateArrayData(prev.users, data)\n      }));\n    };\n\n    // Category updates\n    const handleCategoryUpdate = (data) => {\n      console.log('📂 Category update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        categories: updateArrayData(prev.categories, data)\n      }));\n    };\n\n    // SubCategory updates\n    const handleSubCategoryUpdate = (data) => {\n      console.log('📁 SubCategory update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        subCategories: updateArrayData(prev.subCategories, data)\n      }));\n    };\n\n    // Policy Holder updates\n    const handlePolicyHolderUpdate = (data) => {\n      console.log('👥 Policy Holder update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        policyHolders: updateArrayData(prev.policyHolders, data)\n      }));\n    };\n\n    // Ticket updates\n    const handleTicketUpdate = (data) => {\n      console.log('🎫 Ticket update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        tickets: updateArrayData(prev.tickets, data)\n      }));\n    };\n\n    // Claim updates\n    const handleClaimUpdate = (data) => {\n      console.log('💰 Claim update received:', data);\n      setRealtimeData(prev => ({\n        ...prev,\n        claims: updateArrayData(prev.claims, data)\n      }));\n    };\n\n    // Notification handler\n    const handleNotification = (notification) => {\n      console.log('🔔 Notification received:', notification);\n      setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50\n    };\n\n    // Register all event listeners\n    socketService.onTaskUpdate(handleTaskUpdate);\n    socketService.onPolicyUpdate(handlePolicyUpdate);\n    socketService.onUserUpdate(handleUserUpdate);\n    socketService.onCategoryUpdate(handleCategoryUpdate);\n    socketService.onSubCategoryUpdate(handleSubCategoryUpdate);\n    socketService.onPolicyHolderUpdate(handlePolicyHolderUpdate);\n    socketService.onTicketUpdate(handleTicketUpdate);\n    socketService.onClaimUpdate(handleClaimUpdate);\n    socketService.onNotification(handleNotification);\n\n    return () => {\n      socketService.removeAllListeners();\n    };\n  }, [isConnected]);\n\n  // Helper function to update array data\n  const updateArrayData = (currentArray, updateData) => {\n    const { type, data } = updateData;\n    \n    switch (type) {\n      case 'created':\n        return [...currentArray, data];\n      \n      case 'updated':\n        return currentArray.map(item => \n          item._id === data._id ? { ...item, ...data } : item\n        );\n      \n      case 'deleted':\n        return currentArray.filter(item => item._id !== data._id);\n      \n      default:\n        return currentArray;\n    }\n  };\n\n  // Methods to trigger real-time updates\n  const triggerRefresh = useCallback((dataType) => {\n    socketService.emit('request_refresh', { dataType });\n  }, []);\n\n  const markNotificationAsRead = useCallback((notificationId) => {\n    setNotifications(prev => \n      prev.map(notif => \n        notif.id === notificationId \n          ? { ...notif, read: true }\n          : notif\n      )\n    );\n  }, []);\n\n  const clearNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Subscribe to specific data updates\n  const subscribeToUpdates = useCallback((dataType, callback) => {\n    const eventName = `${dataType}_updated`;\n    socketService.on(eventName, callback);\n    \n    return () => {\n      socketService.off(eventName, callback);\n    };\n  }, []);\n\n  const value = {\n    isConnected,\n    realtimeData,\n    notifications,\n    triggerRefresh,\n    markNotificationAsRead,\n    clearNotifications,\n    subscribeToUpdates,\n    socketService\n  };\n\n  return (\n    <RealtimeContext.Provider value={value}>\n      {children}\n    </RealtimeContext.Provider>\n  );\n};\n\nexport default RealtimeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC1F,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,eAAe,gBAAGT,aAAa,CAAC,CAAC;AAEvC,OAAO,MAAMU,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,eAAe,CAAC;EAC3C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,WAAW;AAQxB,OAAO,MAAMI,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAChD,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACjC,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC;IAC/CsB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,IAAIC,KAAK,EAAE;MACjBe,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C7B,aAAa,CAAC8B,OAAO,CAACjB,KAAK,CAAC;;MAE5B;MACAb,aAAa,CAAC+B,EAAE,CAAC,SAAS,EAAE,MAAM;QAChChB,cAAc,CAAC,IAAI,CAAC;QACpBa,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC;MAEF7B,aAAa,CAAC+B,EAAE,CAAC,YAAY,EAAE,MAAM;QACnChB,cAAc,CAAC,KAAK,CAAC;QACrBa,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,CAAC;;MAEF;MACA7B,aAAa,CAACgC,QAAQ,CAAC,QAAQpB,IAAI,CAACqB,GAAG,EAAE,CAAC;;MAE1C;MACAjC,aAAa,CAACgC,QAAQ,CAAC,QAAQpB,IAAI,CAACsB,IAAI,EAAE,CAAC;MAE3C,OAAO,MAAM;QACXlC,aAAa,CAACmC,UAAU,CAAC,CAAC;QAC1BpB,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;IACH;EACF,CAAC,EAAE,CAACH,IAAI,EAAEC,KAAK,CAAC,CAAC;;EAEjB;EACAhB,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,WAAW,EAAE;;IAElB;IACA,MAAMsB,gBAAgB,GAAIC,IAAI,IAAK;MACjCT,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEQ,IAAI,CAAC;MAC7ClB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPlB,KAAK,EAAEmB,eAAe,CAACD,IAAI,CAAClB,KAAK,EAAEiB,IAAI;MACzC,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMG,kBAAkB,GAAIH,IAAI,IAAK;MACnCT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEQ,IAAI,CAAC;MAC/ClB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPjB,QAAQ,EAAEkB,eAAe,CAACD,IAAI,CAACjB,QAAQ,EAAEgB,IAAI;MAC/C,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMI,gBAAgB,GAAIJ,IAAI,IAAK;MACjCT,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEQ,IAAI,CAAC;MAC7ClB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPhB,KAAK,EAAEiB,eAAe,CAACD,IAAI,CAAChB,KAAK,EAAEe,IAAI;MACzC,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMK,oBAAoB,GAAIL,IAAI,IAAK;MACrCT,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEQ,IAAI,CAAC;MACjDlB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPf,UAAU,EAAEgB,eAAe,CAACD,IAAI,CAACf,UAAU,EAAEc,IAAI;MACnD,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMM,uBAAuB,GAAIN,IAAI,IAAK;MACxCT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,IAAI,CAAC;MACpDlB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPd,aAAa,EAAEe,eAAe,CAACD,IAAI,CAACd,aAAa,EAAEa,IAAI;MACzD,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMO,wBAAwB,GAAIP,IAAI,IAAK;MACzCT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEQ,IAAI,CAAC;MACtDlB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPb,aAAa,EAAEc,eAAe,CAACD,IAAI,CAACb,aAAa,EAAEY,IAAI;MACzD,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMQ,kBAAkB,GAAIR,IAAI,IAAK;MACnCT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEQ,IAAI,CAAC;MAC/ClB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPZ,OAAO,EAAEa,eAAe,CAACD,IAAI,CAACZ,OAAO,EAAEW,IAAI;MAC7C,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMS,iBAAiB,GAAIT,IAAI,IAAK;MAClCT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,IAAI,CAAC;MAC9ClB,eAAe,CAACmB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPX,MAAM,EAAEY,eAAe,CAACD,IAAI,CAACX,MAAM,EAAEU,IAAI;MAC3C,CAAC,CAAC,CAAC;IACL,CAAC;;IAED;IACA,MAAMU,kBAAkB,GAAIC,YAAY,IAAK;MAC3CpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmB,YAAY,CAAC;MACtD/B,gBAAgB,CAACqB,IAAI,IAAI,CAACU,YAAY,EAAE,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;;IAED;IACAjD,aAAa,CAACkD,YAAY,CAACd,gBAAgB,CAAC;IAC5CpC,aAAa,CAACmD,cAAc,CAACX,kBAAkB,CAAC;IAChDxC,aAAa,CAACoD,YAAY,CAACX,gBAAgB,CAAC;IAC5CzC,aAAa,CAACqD,gBAAgB,CAACX,oBAAoB,CAAC;IACpD1C,aAAa,CAACsD,mBAAmB,CAACX,uBAAuB,CAAC;IAC1D3C,aAAa,CAACuD,oBAAoB,CAACX,wBAAwB,CAAC;IAC5D5C,aAAa,CAACwD,cAAc,CAACX,kBAAkB,CAAC;IAChD7C,aAAa,CAACyD,aAAa,CAACX,iBAAiB,CAAC;IAC9C9C,aAAa,CAAC0D,cAAc,CAACX,kBAAkB,CAAC;IAEhD,OAAO,MAAM;MACX/C,aAAa,CAAC2D,kBAAkB,CAAC,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,CAAC7C,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMyB,eAAe,GAAGA,CAACqB,YAAY,EAAEC,UAAU,KAAK;IACpD,MAAM;MAAEC,IAAI;MAAEzB;IAAK,CAAC,GAAGwB,UAAU;IAEjC,QAAQC,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,CAAC,GAAGF,YAAY,EAAEvB,IAAI,CAAC;MAEhC,KAAK,SAAS;QACZ,OAAOuB,YAAY,CAACG,GAAG,CAACC,IAAI,IAC1BA,IAAI,CAAC/B,GAAG,KAAKI,IAAI,CAACJ,GAAG,GAAG;UAAE,GAAG+B,IAAI;UAAE,GAAG3B;QAAK,CAAC,GAAG2B,IACjD,CAAC;MAEH,KAAK,SAAS;QACZ,OAAOJ,YAAY,CAACK,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC/B,GAAG,KAAKI,IAAI,CAACJ,GAAG,CAAC;MAE3D;QACE,OAAO2B,YAAY;IACvB;EACF,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGnE,WAAW,CAAEoE,QAAQ,IAAK;IAC/CnE,aAAa,CAACoE,IAAI,CAAC,iBAAiB,EAAE;MAAED;IAAS,CAAC,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,sBAAsB,GAAGtE,WAAW,CAAEuE,cAAc,IAAK;IAC7DrD,gBAAgB,CAACqB,IAAI,IACnBA,IAAI,CAACyB,GAAG,CAACQ,KAAK,IACZA,KAAK,CAACC,EAAE,KAAKF,cAAc,GACvB;MAAE,GAAGC,KAAK;MAAEE,IAAI,EAAE;IAAK,CAAC,GACxBF,KACN,CACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,kBAAkB,GAAG3E,WAAW,CAAC,MAAM;IAC3CkB,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0D,kBAAkB,GAAG5E,WAAW,CAAC,CAACoE,QAAQ,EAAES,QAAQ,KAAK;IAC7D,MAAMC,SAAS,GAAG,GAAGV,QAAQ,UAAU;IACvCnE,aAAa,CAAC+B,EAAE,CAAC8C,SAAS,EAAED,QAAQ,CAAC;IAErC,OAAO,MAAM;MACX5E,aAAa,CAAC8E,GAAG,CAACD,SAAS,EAAED,QAAQ,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,KAAK,GAAG;IACZjE,WAAW;IACXI,YAAY;IACZF,aAAa;IACbkD,cAAc;IACdG,sBAAsB;IACtBK,kBAAkB;IAClBC,kBAAkB;IAClB3E;EACF,CAAC;EAED,oBACEG,OAAA,CAACC,eAAe,CAAC4E,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArE,QAAA,EACpCA;EAAQ;IAAAuE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;AAACzE,GAAA,CAjNWF,gBAAgB;EAAA,QACHR,OAAO;AAAA;AAAAoF,EAAA,GADpB5E,gBAAgB;AAmN7B,eAAeL,eAAe;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}