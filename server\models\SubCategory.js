const mongoose = require('mongoose');

const subCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Subcategory name is required'],
    trim: true,
    maxlength: [100, 'Subcategory name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Parent category is required']
  },
  code: {
    type: String,
    unique: true,
    trim: true,
    uppercase: true,
    maxlength: [10, 'Code cannot exceed 10 characters']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  metadata: {
    color: {
      type: String,
      default: '#28a745'
    },
    icon: {
      type: String,
      default: 'fas fa-tag'
    },
    image: String
  },
  // Insurance specific fields
  insuranceDetails: {
    coverageTypes: [String],
    defaultCoverageAmount: {
      min: Number,
      max: Number
    },
    defaultPremiumRate: Number, // percentage
    riskFactors: [String],
    eligibilityCriteria: [String],
    exclusions: [String],
    documents: [String] // required documents
  },
  // Business rules
  businessRules: {
    minAge: Number,
    maxAge: Number,
    minCoverage: Number,
    maxCoverage: Number,
    waitingPeriod: Number, // in days
    renewalPeriod: Number, // in days
    gracePeriod: Number // in days
  },
  // Pricing configuration
  pricing: {
    basePremium: Number,
    ageFactor: [{
      minAge: Number,
      maxAge: Number,
      multiplier: Number
    }],
    riskFactor: [{
      factor: String,
      multiplier: Number
    }],
    discounts: [{
      name: String,
      percentage: Number,
      conditions: [String]
    }]
  },
  // Statistics
  stats: {
    totalPolicies: {
      type: Number,
      default: 0
    },
    activePolicies: {
      type: Number,
      default: 0
    },
    totalClaims: {
      type: Number,
      default: 0
    },
    claimRatio: {
      type: Number,
      default: 0
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for category and name uniqueness
subCategorySchema.index({ category: 1, name: 1 }, { unique: true });

// Other indexes for performance (code already has unique index)
subCategorySchema.index({ category: 1 });
subCategorySchema.index({ isActive: 1 });
subCategorySchema.index({ sortOrder: 1 });

// Virtual for full category path
subCategorySchema.virtual('fullPath').get(function() {
  return this.populated('category') ? 
    `${this.category.name} > ${this.name}` : 
    this.name;
});

// Virtual for policies count
subCategorySchema.virtual('policiesCount', {
  ref: 'Policy',
  localField: '_id',
  foreignField: 'subCategory',
  count: true
});

// Pre-save middleware to generate code if not provided
subCategorySchema.pre('save', async function(next) {
  if (!this.code && this.isNew) {
    // Generate code from name
    const baseCode = this.name
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 6)
      .toUpperCase();
    
    let code = baseCode;
    let counter = 1;
    
    // Ensure uniqueness
    while (await this.constructor.findOne({ code })) {
      code = `${baseCode}${counter}`;
      counter++;
    }
    
    this.code = code;
  }
  next();
});

// Static method to find by category
subCategorySchema.statics.findByCategory = function(categoryId, activeOnly = true) {
  const query = { category: categoryId };
  if (activeOnly) query.isActive = true;
  
  return this.find(query)
    .populate('category', 'name type')
    .sort({ sortOrder: 1, name: 1 });
};

// Static method to find with full details
subCategorySchema.statics.findWithDetails = function(filter = {}) {
  return this.find(filter)
    .populate('category', 'name type description')
    .populate('createdBy', 'firstName lastName')
    .populate('updatedBy', 'firstName lastName')
    .sort({ 'category.name': 1, sortOrder: 1, name: 1 });
};

// Static method for search
subCategorySchema.statics.search = function(searchTerm, categoryId = null) {
  const query = {
    $or: [
      { name: { $regex: searchTerm, $options: 'i' } },
      { description: { $regex: searchTerm, $options: 'i' } },
      { code: { $regex: searchTerm, $options: 'i' } }
    ]
  };
  
  if (categoryId) {
    query.category = categoryId;
  }
  
  return this.find(query)
    .populate('category', 'name type')
    .sort({ name: 1 });
};

// Method to update statistics
subCategorySchema.methods.updateStats = async function() {
  const Policy = mongoose.model('Policy');
  const Claim = mongoose.model('Claim');
  
  // Get policy statistics
  const policyStats = await Policy.aggregate([
    { $match: { subCategory: this._id } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        active: {
          $sum: {
            $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
          }
        }
      }
    }
  ]);
  
  // Get claim statistics
  const claimStats = await Claim.aggregate([
    {
      $lookup: {
        from: 'policies',
        localField: 'policy',
        foreignField: '_id',
        as: 'policyInfo'
      }
    },
    { $unwind: '$policyInfo' },
    { $match: { 'policyInfo.subCategory': this._id } },
    {
      $group: {
        _id: null,
        totalClaims: { $sum: 1 }
      }
    }
  ]);
  
  const policyData = policyStats[0] || { total: 0, active: 0 };
  const claimData = claimStats[0] || { totalClaims: 0 };
  
  this.stats = {
    totalPolicies: policyData.total,
    activePolicies: policyData.active,
    totalClaims: claimData.totalClaims,
    claimRatio: policyData.total > 0 ? (claimData.totalClaims / policyData.total) * 100 : 0
  };
  
  return this.save();
};

// Method to calculate premium
subCategorySchema.methods.calculatePremium = function(coverageAmount, age, riskFactors = []) {
  if (!this.pricing || !this.pricing.basePremium) {
    return null;
  }
  
  let premium = this.pricing.basePremium;
  
  // Apply coverage amount factor
  if (coverageAmount) {
    premium = (premium * coverageAmount) / 100000; // base per 1 lakh
  }
  
  // Apply age factor
  if (age && this.pricing.ageFactor) {
    const ageFactor = this.pricing.ageFactor.find(
      factor => age >= factor.minAge && age <= factor.maxAge
    );
    if (ageFactor) {
      premium *= ageFactor.multiplier;
    }
  }
  
  // Apply risk factors
  if (riskFactors.length > 0 && this.pricing.riskFactor) {
    riskFactors.forEach(risk => {
      const riskFactor = this.pricing.riskFactor.find(
        factor => factor.factor === risk
      );
      if (riskFactor) {
        premium *= riskFactor.multiplier;
      }
    });
  }
  
  return Math.round(premium * 100) / 100; // Round to 2 decimal places
};

module.exports = mongoose.model('SubCategory', subCategorySchema);
