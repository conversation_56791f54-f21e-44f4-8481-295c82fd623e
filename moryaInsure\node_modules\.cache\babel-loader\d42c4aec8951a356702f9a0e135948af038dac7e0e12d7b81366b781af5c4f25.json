{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\InsurancePolicy.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Row, Col, Container, Card, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport } from 'react-icons/fa';\nimport { policiesAPI, categoriesAPI, subCategoriesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InsurancePolicy = () => {\n  _s();\n  const [policies, setPolicies] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [search, setSearch] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedPolicy, setSelectedPolicy] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\n\n  // Form state\n  const [form, setForm] = useState({\n    name: '',\n    description: '',\n    type: '',\n    category: '',\n    subCategory: '',\n    premiumAmount: '',\n    coverageAmount: '',\n    duration: 12,\n    terms: '',\n    eligibilityCriteria: '',\n    isActive: true\n  });\n  useEffect(() => {\n    fetchPolicies();\n    fetchCategories();\n    fetchSubCategories();\n  }, []);\n  const fetchPolicies = async () => {\n    try {\n      setLoading(true);\n      const response = await policiesAPI.getPolicies();\n      if (response.success) {\n        setPolicies(response.data.policies || []);\n      } else {\n        setError('Failed to fetch policies');\n      }\n    } catch (error) {\n      console.error('Error fetching policies:', error);\n      setError('Failed to fetch policies');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await categoriesAPI.getCategories();\n      if (response.success) {\n        setCategories(response.data.categories || []);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n  const fetchSubCategories = async () => {\n    try {\n      const response = await subCategoriesAPI.getSubCategories();\n      if (response.success) {\n        setSubCategories(response.data.subcategories || []);\n      }\n    } catch (error) {\n      console.error('Error fetching subcategories:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setForm(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    setError('');\n  };\n  const handleSave = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policiesAPI.createPolicy(form);\n      if (response.success) {\n        setSuccess('Policy created successfully!');\n        resetForm();\n        setShowModal(false);\n        fetchPolicies();\n      } else {\n        setError(response.message || 'Failed to create policy');\n      }\n    } catch (error) {\n      console.error('Error creating policy:', error);\n      setError('Failed to create policy');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policiesAPI.updatePolicy(selectedPolicy._id, form);\n      if (response.success) {\n        setSuccess('Policy updated successfully!');\n        setShowEditModal(false);\n        setSelectedPolicy(null);\n        fetchPolicies();\n      } else {\n        setError(response.message || 'Failed to update policy');\n      }\n    } catch (error) {\n      console.error('Error updating policy:', error);\n      setError('Failed to update policy');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDelete = async policyId => {\n    if (window.confirm('Are you sure you want to delete this policy?')) {\n      try {\n        const response = await policiesAPI.deletePolicy(policyId);\n        if (response.success) {\n          setSuccess('Policy deleted successfully!');\n          fetchPolicies();\n        } else {\n          setError(response.message || 'Failed to delete policy');\n        }\n      } catch (error) {\n        console.error('Error deleting policy:', error);\n        setError('Failed to delete policy');\n      }\n    }\n  };\n  const resetForm = () => {\n    setForm({\n      name: '',\n      description: '',\n      type: '',\n      category: '',\n      subCategory: '',\n      premiumAmount: '',\n      coverageAmount: '',\n      duration: 12,\n      terms: '',\n      eligibilityCriteria: '',\n      isActive: true\n    });\n  };\n  const openCreateModal = () => {\n    resetForm();\n    setModalMode('create');\n    setShowModal(true);\n  };\n  const openEditModal = policy => {\n    var _policy$category, _policy$subCategory;\n    setSelectedPolicy(policy);\n    setForm({\n      name: policy.name,\n      description: policy.description || '',\n      type: policy.type,\n      category: ((_policy$category = policy.category) === null || _policy$category === void 0 ? void 0 : _policy$category._id) || policy.category,\n      subCategory: ((_policy$subCategory = policy.subCategory) === null || _policy$subCategory === void 0 ? void 0 : _policy$subCategory._id) || policy.subCategory,\n      premiumAmount: policy.premiumAmount,\n      coverageAmount: policy.coverageAmount,\n      duration: policy.duration,\n      terms: policy.terms || '',\n      eligibilityCriteria: policy.eligibilityCriteria || '',\n      isActive: policy.isActive\n    });\n    setModalMode('edit');\n    setShowEditModal(true);\n  };\n  const openViewModal = policy => {\n    setSelectedPolicy(policy);\n    setShowViewModal(true);\n  };\n  const filteredPolicies = policies.filter(policy => {\n    var _policy$name;\n    return (_policy$name = policy.name) === null || _policy$name === void 0 ? void 0 : _policy$name.toLowerCase().includes(search.toLowerCase());\n  });\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'active': {\n        variant: 'success',\n        text: 'Active'\n      },\n      'inactive': {\n        variant: 'secondary',\n        text: 'Inactive'\n      },\n      'pending': {\n        variant: 'warning',\n        text: 'Pending'\n      },\n      'expired': {\n        variant: 'danger',\n        text: 'Expired'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 12\n    }, this);\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"mb-1\",\n              children: \"Insurance Policies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Manage insurance policy templates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: openCreateModal,\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), \"New Policy\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              children: [/*#__PURE__*/_jsxDEV(FaFileImport, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), \"Import\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"success\",\n      dismissible: true,\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      dismissible: true,\n      onClose: () => setError(''),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative\",\n          children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n            className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Search policies...\",\n            value: search,\n            onChange: e => setSearch(e.target.value),\n            className: \"ps-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2\",\n                children: \"Loading policies...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this) : filteredPolicies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                size: 48,\n                className: \"text-muted mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"No Policies Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: search ? 'No policies match your search.' : 'Start by creating your first policy.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), !search && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: openCreateModal,\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), \"Create First Policy\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Policy Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Premium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Coverage\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: filteredPolicies.map(policy => {\n                  var _policy$category2;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: policy.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: policy.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"info\",\n                        className: \"text-capitalize\",\n                        children: policy.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: ((_policy$category2 = policy.category) === null || _policy$category2 === void 0 ? void 0 : _policy$category2.name) || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatCurrency(policy.premiumAmount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatCurrency(policy.coverageAmount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [policy.duration, \" months\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: getStatusBadge(policy.isActive ? 'active' : 'inactive')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-info\",\n                          size: \"sm\",\n                          onClick: () => openViewModal(policy),\n                          title: \"View Policy\",\n                          children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 337,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 331,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-warning\",\n                          size: \"sm\",\n                          onClick: () => openEditModal(policy),\n                          title: \"Edit Policy\",\n                          children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 345,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 339,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-danger\",\n                          size: \"sm\",\n                          onClick: () => handleDelete(policy._id),\n                          title: \"Delete Policy\",\n                          children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 353,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 25\n                    }, this)]\n                  }, policy._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Create New Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"name\",\n                  value: form.name,\n                  onChange: handleChange,\n                  placeholder: \"Enter policy name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"type\",\n                  value: form.type,\n                  onChange: handleChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select type...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"life\",\n                    children: \"Life Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"health\",\n                    children: \"Health Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"auto\",\n                    children: \"Auto Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"home\",\n                    children: \"Home Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"travel\",\n                    children: \"Travel Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"business\",\n                    children: \"Business Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"description\",\n              value: form.description,\n              onChange: handleChange,\n              placeholder: \"Enter policy description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"category\",\n                  value: form.category,\n                  onChange: handleChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select category...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category._id,\n                    children: category.name\n                  }, category._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Sub Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"subCategory\",\n                  value: form.subCategory,\n                  onChange: handleChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select subcategory...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this), subCategories.filter(sub => {\n                    var _sub$category;\n                    return ((_sub$category = sub.category) === null || _sub$category === void 0 ? void 0 : _sub$category._id) === form.category;\n                  }).map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: subCategory._id,\n                    children: subCategory.name\n                  }, subCategory._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Premium Amount (\\u20B9) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"premiumAmount\",\n                  value: form.premiumAmount,\n                  onChange: handleChange,\n                  placeholder: \"Enter premium amount\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Coverage Amount (\\u20B9) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"coverageAmount\",\n                  value: form.coverageAmount,\n                  onChange: handleChange,\n                  placeholder: \"Enter coverage amount\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Duration (months) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"duration\",\n                  value: form.duration,\n                  onChange: handleChange,\n                  min: \"1\",\n                  max: \"120\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  name: \"isActive\",\n                  label: \"Active\",\n                  checked: form.isActive,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Terms & Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"terms\",\n              value: form.terms,\n              onChange: handleChange,\n              placeholder: \"Enter terms and conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Eligibility Criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 2,\n              name: \"eligibilityCriteria\",\n              value: form.eligibilityCriteria,\n              onChange: handleChange,\n              placeholder: \"Enter eligibility criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: submitting,\n          children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), \"Creating...\"]\n          }, void 0, true) : 'Create Policy'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showEditModal,\n      onHide: () => setShowEditModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Edit Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 21\n        }, this), selectedPolicy && /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"name\",\n                  value: form.name,\n                  onChange: handleChange,\n                  placeholder: \"Enter policy name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Type *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"type\",\n                  value: form.type,\n                  onChange: handleChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select type...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"life\",\n                    children: \"Life Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"health\",\n                    children: \"Health Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"auto\",\n                    children: \"Auto Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"home\",\n                    children: \"Home Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"travel\",\n                    children: \"Travel Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"business\",\n                    children: \"Business Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"description\",\n              value: form.description,\n              onChange: handleChange,\n              placeholder: \"Enter policy description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Premium Amount (\\u20B9) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"premiumAmount\",\n                  value: form.premiumAmount,\n                  onChange: handleChange,\n                  placeholder: \"Enter premium amount\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Coverage Amount (\\u20B9) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"coverageAmount\",\n                  value: form.coverageAmount,\n                  onChange: handleChange,\n                  placeholder: \"Enter coverage amount\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Duration (months) *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"duration\",\n                  value: form.duration,\n                  onChange: handleChange,\n                  min: \"1\",\n                  max: \"120\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                  type: \"checkbox\",\n                  name: \"isActive\",\n                  label: \"Active\",\n                  checked: form.isActive,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowEditModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleEdit,\n          disabled: submitting,\n          children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this), \"Updating...\"]\n          }, void 0, true) : 'Update Policy'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showViewModal,\n      onHide: () => setShowViewModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Policy Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedPolicy && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 22\n                }, this), \" \", selectedPolicy.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 22\n                }, this), \" \", selectedPolicy.type]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 22\n                }, this), \" \", selectedPolicy.description || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 22\n                }, this), \" \", getStatusBadge(selectedPolicy.isActive ? 'active' : 'inactive')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Financial Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Premium:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 22\n                }, this), \" \", formatCurrency(selectedPolicy.premiumAmount)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Coverage:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 22\n                }, this), \" \", formatCurrency(selectedPolicy.coverageAmount)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Duration:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 22\n                }, this), \" \", selectedPolicy.duration, \" months\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Created:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 22\n                }, this), \" \", new Date(selectedPolicy.createdAt).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 15\n          }, this), selectedPolicy.terms && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Terms & Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: selectedPolicy.terms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 17\n          }, this), selectedPolicy.eligibilityCriteria && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Eligibility Criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: selectedPolicy.eligibilityCriteria\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowViewModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 705,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n};\n_s(InsurancePolicy, \"DBzV3qBiTAPlCzUc4uCARx0ggHY=\");\n_c = InsurancePolicy;\nexport default InsurancePolicy;\nvar _c;\n$RefreshReg$(_c, \"InsurancePolicy\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Row", "Col", "Container", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaSearch", "FaFileImport", "policiesAPI", "categoriesAPI", "subCategoriesAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InsurancePolicy", "_s", "policies", "setPolicies", "categories", "setCategories", "subCategories", "setSubCategories", "search", "setSearch", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "showEditModal", "setShowEditModal", "showViewModal", "setShowViewModal", "selectedPolicy", "setSelectedPolicy", "submitting", "setSubmitting", "modalMode", "setModalMode", "form", "setForm", "name", "description", "type", "category", "subCategory", "premiumAmount", "coverageAmount", "duration", "terms", "eligibilityCriteria", "isActive", "fetchPolicies", "fetchCategories", "fetchSubCategories", "response", "getPolicies", "data", "console", "getCategories", "getSubCategories", "subcategories", "handleChange", "e", "value", "checked", "target", "prev", "handleSave", "createPolicy", "resetForm", "message", "handleEdit", "updatePolicy", "_id", "handleDelete", "policyId", "window", "confirm", "deletePolicy", "openCreateModal", "openEditModal", "policy", "_policy$category", "_policy$subCategory", "openViewModal", "filteredPolicies", "filter", "_policy$name", "toLowerCase", "includes", "getStatusBadge", "status", "statusConfig", "variant", "text", "config", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "fluid", "className", "onClick", "dismissible", "onClose", "md", "Control", "placeholder", "onChange", "Body", "animation", "length", "size", "responsive", "hover", "map", "_policy$category2", "title", "show", "onHide", "Header", "closeButton", "Title", "Group", "Label", "required", "Select", "as", "rows", "sub", "_sub$category", "min", "max", "Check", "label", "Footer", "disabled", "Date", "createdAt", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/InsurancePolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { <PERSON>, But<PERSON>, Modal, Form, Row, Col, Container, <PERSON>, Al<PERSON>, Spin<PERSON>, Badge } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport } from 'react-icons/fa';\r\nimport { policiesAPI, categoriesAPI, subCategoriesAPI } from '../services/api';\r\n\r\nconst InsurancePolicy = () => {\r\n  const [policies, setPolicies] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [subCategories, setSubCategories] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedPolicy, setSelectedPolicy] = useState(null);\r\n  const [submitting, setSubmitting] = useState(false);\r\n  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'\r\n\r\n  // Form state\r\n  const [form, setForm] = useState({\r\n    name: '',\r\n    description: '',\r\n    type: '',\r\n    category: '',\r\n    subCategory: '',\r\n    premiumAmount: '',\r\n    coverageAmount: '',\r\n    duration: 12,\r\n    terms: '',\r\n    eligibilityCriteria: '',\r\n    isActive: true\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchPolicies();\r\n    fetchCategories();\r\n    fetchSubCategories();\r\n  }, []);\r\n\r\n  const fetchPolicies = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await policiesAPI.getPolicies();\r\n      if (response.success) {\r\n        setPolicies(response.data.policies || []);\r\n      } else {\r\n        setError('Failed to fetch policies');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policies:', error);\r\n      setError('Failed to fetch policies');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchCategories = async () => {\r\n    try {\r\n      const response = await categoriesAPI.getCategories();\r\n      if (response.success) {\r\n        setCategories(response.data.categories || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching categories:', error);\r\n    }\r\n  };\r\n\r\n  const fetchSubCategories = async () => {\r\n    try {\r\n      const response = await subCategoriesAPI.getSubCategories();\r\n      if (response.success) {\r\n        setSubCategories(response.data.subcategories || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching subcategories:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setForm(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n    setError('');\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policiesAPI.createPolicy(form);\r\n      if (response.success) {\r\n        setSuccess('Policy created successfully!');\r\n        resetForm();\r\n        setShowModal(false);\r\n        fetchPolicies();\r\n      } else {\r\n        setError(response.message || 'Failed to create policy');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating policy:', error);\r\n      setError('Failed to create policy');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleEdit = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policiesAPI.updatePolicy(selectedPolicy._id, form);\r\n      if (response.success) {\r\n        setSuccess('Policy updated successfully!');\r\n        setShowEditModal(false);\r\n        setSelectedPolicy(null);\r\n        fetchPolicies();\r\n      } else {\r\n        setError(response.message || 'Failed to update policy');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating policy:', error);\r\n      setError('Failed to update policy');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (policyId) => {\r\n    if (window.confirm('Are you sure you want to delete this policy?')) {\r\n      try {\r\n        const response = await policiesAPI.deletePolicy(policyId);\r\n        if (response.success) {\r\n          setSuccess('Policy deleted successfully!');\r\n          fetchPolicies();\r\n        } else {\r\n          setError(response.message || 'Failed to delete policy');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error deleting policy:', error);\r\n        setError('Failed to delete policy');\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setForm({\r\n      name: '',\r\n      description: '',\r\n      type: '',\r\n      category: '',\r\n      subCategory: '',\r\n      premiumAmount: '',\r\n      coverageAmount: '',\r\n      duration: 12,\r\n      terms: '',\r\n      eligibilityCriteria: '',\r\n      isActive: true\r\n    });\r\n  };\r\n\r\n  const openCreateModal = () => {\r\n    resetForm();\r\n    setModalMode('create');\r\n    setShowModal(true);\r\n  };\r\n\r\n  const openEditModal = (policy) => {\r\n    setSelectedPolicy(policy);\r\n    setForm({\r\n      name: policy.name,\r\n      description: policy.description || '',\r\n      type: policy.type,\r\n      category: policy.category?._id || policy.category,\r\n      subCategory: policy.subCategory?._id || policy.subCategory,\r\n      premiumAmount: policy.premiumAmount,\r\n      coverageAmount: policy.coverageAmount,\r\n      duration: policy.duration,\r\n      terms: policy.terms || '',\r\n      eligibilityCriteria: policy.eligibilityCriteria || '',\r\n      isActive: policy.isActive\r\n    });\r\n    setModalMode('edit');\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const openViewModal = (policy) => {\r\n    setSelectedPolicy(policy);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const filteredPolicies = policies.filter((policy) =>\r\n    policy.name?.toLowerCase().includes(search.toLowerCase())\r\n  );\r\n\r\n  const getStatusBadge = (status) => {\r\n    const statusConfig = {\r\n      'active': { variant: 'success', text: 'Active' },\r\n      'inactive': { variant: 'secondary', text: 'Inactive' },\r\n      'pending': { variant: 'warning', text: 'Pending' },\r\n      'expired': { variant: 'danger', text: 'Expired' }\r\n    };\r\n\r\n    const config = statusConfig[status] || { variant: 'secondary', text: status };\r\n    return <Badge bg={config.variant}>{config.text}</Badge>;\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    return new Intl.NumberFormat('en-IN', {\r\n      style: 'currency',\r\n      currency: 'INR'\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <Container fluid className=\"py-4\">\r\n      <Row className=\"mb-4\">\r\n        <Col>\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <div>\r\n              <h2 className=\"mb-1\">Insurance Policies</h2>\r\n              <p className=\"text-muted\">Manage insurance policy templates</p>\r\n            </div>\r\n            <div className=\"d-flex gap-2\">\r\n              <Button variant=\"primary\" onClick={openCreateModal}>\r\n                <FaPlus className=\"me-2\" />\r\n                New Policy\r\n              </Button>\r\n              <Button variant=\"secondary\">\r\n                <FaFileImport className=\"me-2\" />\r\n                Import\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n\r\n      {success && (\r\n        <Alert variant=\"success\" dismissible onClose={() => setSuccess('')}>\r\n          {success}\r\n        </Alert>\r\n      )}\r\n\r\n      {error && (\r\n        <Alert variant=\"danger\" dismissible onClose={() => setError('')}>\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Row className=\"mb-3\">\r\n        <Col md={6}>\r\n          <div className=\"position-relative\">\r\n            <FaSearch className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\r\n            <Form.Control\r\n              type=\"text\"\r\n              placeholder=\"Search policies...\"\r\n              value={search}\r\n              onChange={(e) => setSearch(e.target.value)}\r\n              className=\"ps-5\"\r\n            />\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row>\r\n        <Col>\r\n          <Card>\r\n            <Card.Body>\r\n              {loading ? (\r\n                <div className=\"text-center py-4\">\r\n                  <Spinner animation=\"border\" />\r\n                  <p className=\"mt-2\">Loading policies...</p>\r\n                </div>\r\n              ) : filteredPolicies.length === 0 ? (\r\n                <div className=\"text-center py-4\">\r\n                  <FaPlus size={48} className=\"text-muted mb-3\" />\r\n                  <h5>No Policies Found</h5>\r\n                  <p className=\"text-muted\">\r\n                    {search ? 'No policies match your search.' : 'Start by creating your first policy.'}\r\n                  </p>\r\n                  {!search && (\r\n                    <Button variant=\"primary\" onClick={openCreateModal}>\r\n                      <FaPlus className=\"me-2\" />\r\n                      Create First Policy\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <Table responsive hover>\r\n                  <thead>\r\n                    <tr>\r\n                      <th>Policy Name</th>\r\n                      <th>Type</th>\r\n                      <th>Category</th>\r\n                      <th>Premium</th>\r\n                      <th>Coverage</th>\r\n                      <th>Duration</th>\r\n                      <th>Status</th>\r\n                      <th>Actions</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {filteredPolicies.map((policy) => (\r\n                      <tr key={policy._id}>\r\n                        <td>\r\n                          <strong>{policy.name}</strong>\r\n                          <br />\r\n                          <small className=\"text-muted\">{policy.description}</small>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"info\" className=\"text-capitalize\">\r\n                            {policy.type}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>{policy.category?.name || 'N/A'}</td>\r\n                        <td>{formatCurrency(policy.premiumAmount)}</td>\r\n                        <td>{formatCurrency(policy.coverageAmount)}</td>\r\n                        <td>{policy.duration} months</td>\r\n                        <td>\r\n                          {getStatusBadge(policy.isActive ? 'active' : 'inactive')}\r\n                        </td>\r\n                        <td>\r\n                          <div className=\"d-flex gap-1\">\r\n                            <Button\r\n                              variant=\"outline-info\"\r\n                              size=\"sm\"\r\n                              onClick={() => openViewModal(policy)}\r\n                              title=\"View Policy\"\r\n                            >\r\n                              <FaEye />\r\n                            </Button>\r\n                            <Button\r\n                              variant=\"outline-warning\"\r\n                              size=\"sm\"\r\n                              onClick={() => openEditModal(policy)}\r\n                              title=\"Edit Policy\"\r\n                            >\r\n                              <FaEdit />\r\n                            </Button>\r\n                            <Button\r\n                              variant=\"outline-danger\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleDelete(policy._id)}\r\n                              title=\"Delete Policy\"\r\n                            >\r\n                              <FaTrash />\r\n                            </Button>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </Table>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Create Policy Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Create New Policy</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {error && <Alert variant=\"danger\">{error}</Alert>}\r\n          <Form>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Policy Name *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={form.name}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter policy name\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Policy Type *</Form.Label>\r\n                  <Form.Select\r\n                    name=\"type\"\r\n                    value={form.type}\r\n                    onChange={handleChange}\r\n                    required\r\n                  >\r\n                    <option value=\"\">Select type...</option>\r\n                    <option value=\"life\">Life Insurance</option>\r\n                    <option value=\"health\">Health Insurance</option>\r\n                    <option value=\"auto\">Auto Insurance</option>\r\n                    <option value=\"home\">Home Insurance</option>\r\n                    <option value=\"travel\">Travel Insurance</option>\r\n                    <option value=\"business\">Business Insurance</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Description</Form.Label>\r\n              <Form.Control\r\n                as=\"textarea\"\r\n                rows={3}\r\n                name=\"description\"\r\n                value={form.description}\r\n                onChange={handleChange}\r\n                placeholder=\"Enter policy description\"\r\n              />\r\n            </Form.Group>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Category</Form.Label>\r\n                  <Form.Select\r\n                    name=\"category\"\r\n                    value={form.category}\r\n                    onChange={handleChange}\r\n                  >\r\n                    <option value=\"\">Select category...</option>\r\n                    {categories.map((category) => (\r\n                      <option key={category._id} value={category._id}>\r\n                        {category.name}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Sub Category</Form.Label>\r\n                  <Form.Select\r\n                    name=\"subCategory\"\r\n                    value={form.subCategory}\r\n                    onChange={handleChange}\r\n                  >\r\n                    <option value=\"\">Select subcategory...</option>\r\n                    {subCategories\r\n                      .filter(sub => sub.category?._id === form.category)\r\n                      .map((subCategory) => (\r\n                        <option key={subCategory._id} value={subCategory._id}>\r\n                          {subCategory.name}\r\n                        </option>\r\n                      ))}\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Premium Amount (₹) *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"premiumAmount\"\r\n                    value={form.premiumAmount}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter premium amount\"\r\n                    min=\"0\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Coverage Amount (₹) *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"coverageAmount\"\r\n                    value={form.coverageAmount}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter coverage amount\"\r\n                    min=\"0\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Duration (months) *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"duration\"\r\n                    value={form.duration}\r\n                    onChange={handleChange}\r\n                    min=\"1\"\r\n                    max=\"120\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Check\r\n                    type=\"checkbox\"\r\n                    name=\"isActive\"\r\n                    label=\"Active\"\r\n                    checked={form.isActive}\r\n                    onChange={handleChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Terms & Conditions</Form.Label>\r\n              <Form.Control\r\n                as=\"textarea\"\r\n                rows={3}\r\n                name=\"terms\"\r\n                value={form.terms}\r\n                onChange={handleChange}\r\n                placeholder=\"Enter terms and conditions\"\r\n              />\r\n            </Form.Group>\r\n\r\n            <Form.Group className=\"mb-3\">\r\n              <Form.Label>Eligibility Criteria</Form.Label>\r\n              <Form.Control\r\n                as=\"textarea\"\r\n                rows={2}\r\n                name=\"eligibilityCriteria\"\r\n                value={form.eligibilityCriteria}\r\n                onChange={handleChange}\r\n                placeholder=\"Enter eligibility criteria\"\r\n              />\r\n            </Form.Group>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={handleSave}\r\n            disabled={submitting}\r\n          >\r\n            {submitting ? (\r\n              <>\r\n                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                Creating...\r\n              </>\r\n            ) : (\r\n              'Create Policy'\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* Edit Policy Modal */}\r\n      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Edit Policy</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {error && <Alert variant=\"danger\">{error}</Alert>}\r\n          {selectedPolicy && (\r\n            <Form>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Policy Name *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={form.name}\r\n                      onChange={handleChange}\r\n                      placeholder=\"Enter policy name\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Policy Type *</Form.Label>\r\n                    <Form.Select\r\n                      name=\"type\"\r\n                      value={form.type}\r\n                      onChange={handleChange}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select type...</option>\r\n                      <option value=\"life\">Life Insurance</option>\r\n                      <option value=\"health\">Health Insurance</option>\r\n                      <option value=\"auto\">Auto Insurance</option>\r\n                      <option value=\"home\">Home Insurance</option>\r\n                      <option value=\"travel\">Travel Insurance</option>\r\n                      <option value=\"business\">Business Insurance</option>\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Description</Form.Label>\r\n                <Form.Control\r\n                  as=\"textarea\"\r\n                  rows={3}\r\n                  name=\"description\"\r\n                  value={form.description}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter policy description\"\r\n                />\r\n              </Form.Group>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Premium Amount (₹) *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"number\"\r\n                      name=\"premiumAmount\"\r\n                      value={form.premiumAmount}\r\n                      onChange={handleChange}\r\n                      placeholder=\"Enter premium amount\"\r\n                      min=\"0\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Coverage Amount (₹) *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"number\"\r\n                      name=\"coverageAmount\"\r\n                      value={form.coverageAmount}\r\n                      onChange={handleChange}\r\n                      placeholder=\"Enter coverage amount\"\r\n                      min=\"0\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Duration (months) *</Form.Label>\r\n                    <Form.Control\r\n                      type=\"number\"\r\n                      name=\"duration\"\r\n                      value={form.duration}\r\n                      onChange={handleChange}\r\n                      min=\"1\"\r\n                      max=\"120\"\r\n                      required\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Check\r\n                      type=\"checkbox\"\r\n                      name=\"isActive\"\r\n                      label=\"Active\"\r\n                      checked={form.isActive}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n            </Form>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowEditModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={handleEdit}\r\n            disabled={submitting}\r\n          >\r\n            {submitting ? (\r\n              <>\r\n                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                Updating...\r\n              </>\r\n            ) : (\r\n              'Update Policy'\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      {/* View Policy Modal */}\r\n      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Policy Details</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {selectedPolicy && (\r\n            <div>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <h6>Basic Information</h6>\r\n                  <p><strong>Name:</strong> {selectedPolicy.name}</p>\r\n                  <p><strong>Type:</strong> {selectedPolicy.type}</p>\r\n                  <p><strong>Description:</strong> {selectedPolicy.description || 'N/A'}</p>\r\n                  <p><strong>Status:</strong> {getStatusBadge(selectedPolicy.isActive ? 'active' : 'inactive')}</p>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <h6>Financial Details</h6>\r\n                  <p><strong>Premium:</strong> {formatCurrency(selectedPolicy.premiumAmount)}</p>\r\n                  <p><strong>Coverage:</strong> {formatCurrency(selectedPolicy.coverageAmount)}</p>\r\n                  <p><strong>Duration:</strong> {selectedPolicy.duration} months</p>\r\n                  <p><strong>Created:</strong> {new Date(selectedPolicy.createdAt).toLocaleDateString()}</p>\r\n                </Col>\r\n              </Row>\r\n\r\n              {selectedPolicy.terms && (\r\n                <div className=\"mt-3\">\r\n                  <h6>Terms & Conditions</h6>\r\n                  <p>{selectedPolicy.terms}</p>\r\n                </div>\r\n              )}\r\n\r\n              {selectedPolicy.eligibilityCriteria && (\r\n                <div className=\"mt-3\">\r\n                  <h6>Eligibility Criteria</h6>\r\n                  <p>{selectedPolicy.eligibilityCriteria}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowViewModal(false)}>\r\n            Close\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default InsurancePolicy;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC9G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,gBAAgB;AACvF,SAASC,WAAW,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC;IAC/BwD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,mBAAmB,EAAE,EAAE;IACvBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFjE,SAAS,CAAC,MAAM;IACdkE,aAAa,CAAC,CAAC;IACfC,eAAe,CAAC,CAAC;IACjBC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,QAAQ,GAAG,MAAMnD,WAAW,CAACoD,WAAW,CAAC,CAAC;MAChD,IAAID,QAAQ,CAAC9B,OAAO,EAAE;QACpBX,WAAW,CAACyC,QAAQ,CAACE,IAAI,CAAC5C,QAAQ,IAAI,EAAE,CAAC;MAC3C,CAAC,MAAM;QACLW,QAAQ,CAAC,0BAA0B,CAAC;MACtC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMlD,aAAa,CAACsD,aAAa,CAAC,CAAC;MACpD,IAAIJ,QAAQ,CAAC9B,OAAO,EAAE;QACpBT,aAAa,CAACuC,QAAQ,CAACE,IAAI,CAAC1C,UAAU,IAAI,EAAE,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjD,gBAAgB,CAACsD,gBAAgB,CAAC,CAAC;MAC1D,IAAIL,QAAQ,CAAC9B,OAAO,EAAE;QACpBP,gBAAgB,CAACqC,QAAQ,CAACE,IAAI,CAACI,aAAa,IAAI,EAAE,CAAC;MACrD;IACF,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMuC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEtB,IAAI;MAAEuB,KAAK;MAAErB,IAAI;MAAEsB;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/C1B,OAAO,CAAC2B,IAAI,KAAK;MACf,GAAGA,IAAI;MACP,CAAC1B,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGsB,OAAO,GAAGD;IAC1C,CAAC,CAAC,CAAC;IACHxC,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM4C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFhC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAM+B,QAAQ,GAAG,MAAMnD,WAAW,CAACiE,YAAY,CAAC9B,IAAI,CAAC;MACrD,IAAIgB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,8BAA8B,CAAC;QAC1C4C,SAAS,CAAC,CAAC;QACX1C,YAAY,CAAC,KAAK,CAAC;QACnBwB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL5B,QAAQ,CAAC+B,QAAQ,CAACgB,OAAO,IAAI,yBAAyB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAM+B,QAAQ,GAAG,MAAMnD,WAAW,CAACqE,YAAY,CAACxC,cAAc,CAACyC,GAAG,EAAEnC,IAAI,CAAC;MACzE,IAAIgB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CI,gBAAgB,CAAC,KAAK,CAAC;QACvBI,iBAAiB,CAAC,IAAI,CAAC;QACvBkB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL5B,QAAQ,CAAC+B,QAAQ,CAACgB,OAAO,IAAI,yBAAyB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAIC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAClE,IAAI;QACF,MAAMvB,QAAQ,GAAG,MAAMnD,WAAW,CAAC2E,YAAY,CAACH,QAAQ,CAAC;QACzD,IAAIrB,QAAQ,CAAC9B,OAAO,EAAE;UACpBC,UAAU,CAAC,8BAA8B,CAAC;UAC1C0B,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACL5B,QAAQ,CAAC+B,QAAQ,CAACgB,OAAO,IAAI,yBAAyB,CAAC;QACzD;MACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;QACdmC,OAAO,CAACnC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CC,QAAQ,CAAC,yBAAyB,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAM8C,SAAS,GAAGA,CAAA,KAAM;IACtB9B,OAAO,CAAC;MACNC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,mBAAmB,EAAE,EAAE;MACvBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BV,SAAS,CAAC,CAAC;IACXhC,YAAY,CAAC,QAAQ,CAAC;IACtBV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMqD,aAAa,GAAIC,MAAM,IAAK;IAAA,IAAAC,gBAAA,EAAAC,mBAAA;IAChClD,iBAAiB,CAACgD,MAAM,CAAC;IACzB1C,OAAO,CAAC;MACNC,IAAI,EAAEyC,MAAM,CAACzC,IAAI;MACjBC,WAAW,EAAEwC,MAAM,CAACxC,WAAW,IAAI,EAAE;MACrCC,IAAI,EAAEuC,MAAM,CAACvC,IAAI;MACjBC,QAAQ,EAAE,EAAAuC,gBAAA,GAAAD,MAAM,CAACtC,QAAQ,cAAAuC,gBAAA,uBAAfA,gBAAA,CAAiBT,GAAG,KAAIQ,MAAM,CAACtC,QAAQ;MACjDC,WAAW,EAAE,EAAAuC,mBAAA,GAAAF,MAAM,CAACrC,WAAW,cAAAuC,mBAAA,uBAAlBA,mBAAA,CAAoBV,GAAG,KAAIQ,MAAM,CAACrC,WAAW;MAC1DC,aAAa,EAAEoC,MAAM,CAACpC,aAAa;MACnCC,cAAc,EAAEmC,MAAM,CAACnC,cAAc;MACrCC,QAAQ,EAAEkC,MAAM,CAAClC,QAAQ;MACzBC,KAAK,EAAEiC,MAAM,CAACjC,KAAK,IAAI,EAAE;MACzBC,mBAAmB,EAAEgC,MAAM,CAAChC,mBAAmB,IAAI,EAAE;MACrDC,QAAQ,EAAE+B,MAAM,CAAC/B;IACnB,CAAC,CAAC;IACFb,YAAY,CAAC,MAAM,CAAC;IACpBR,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMuD,aAAa,GAAIH,MAAM,IAAK;IAChChD,iBAAiB,CAACgD,MAAM,CAAC;IACzBlD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMsD,gBAAgB,GAAGzE,QAAQ,CAAC0E,MAAM,CAAEL,MAAM;IAAA,IAAAM,YAAA;IAAA,QAAAA,YAAA,GAC9CN,MAAM,CAACzC,IAAI,cAAA+C,YAAA,uBAAXA,YAAA,CAAaC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvE,MAAM,CAACsE,WAAW,CAAC,CAAC,CAAC;EAAA,CAC3D,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,YAAY,GAAG;MACnB,QAAQ,EAAE;QAAEC,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAS,CAAC;MAChD,UAAU,EAAE;QAAED,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAW,CAAC;MACtD,SAAS,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC;MAClD,SAAS,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAU;IAClD,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAAC,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAEH;IAAO,CAAC;IAC7E,oBAAOpF,OAAA,CAACX,KAAK;MAACoG,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,EAAEF,MAAM,CAACD;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACzD,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,oBACEhG,OAAA,CAACf,SAAS;IAACqH,KAAK;IAACC,SAAS,EAAC,MAAM;IAAAb,QAAA,gBAC/B1F,OAAA,CAACjB,GAAG;MAACwH,SAAS,EAAC,MAAM;MAAAb,QAAA,eACnB1F,OAAA,CAAChB,GAAG;QAAA0G,QAAA,eACF1F,OAAA;UAAKuG,SAAS,EAAC,mDAAmD;UAAAb,QAAA,gBAChE1F,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAIuG,SAAS,EAAC,MAAM;cAAAb,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9F,OAAA;cAAGuG,SAAS,EAAC,YAAY;cAAAb,QAAA,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN9F,OAAA;YAAKuG,SAAS,EAAC,cAAc;YAAAb,QAAA,gBAC3B1F,OAAA,CAACpB,MAAM;cAAC0G,OAAO,EAAC,SAAS;cAACkB,OAAO,EAAEhC,eAAgB;cAAAkB,QAAA,gBACjD1F,OAAA,CAACV,MAAM;gBAACiH,SAAS,EAAC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9F,OAAA,CAACpB,MAAM;cAAC0G,OAAO,EAAC,WAAW;cAAAI,QAAA,gBACzB1F,OAAA,CAACL,YAAY;gBAAC4G,SAAS,EAAC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7E,OAAO,iBACNjB,OAAA,CAACb,KAAK;MAACmG,OAAO,EAAC,SAAS;MAACmB,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMxF,UAAU,CAAC,EAAE,CAAE;MAAAwE,QAAA,EAChEzE;IAAO;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,EAEA/E,KAAK,iBACJf,OAAA,CAACb,KAAK;MAACmG,OAAO,EAAC,QAAQ;MAACmB,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAM1F,QAAQ,CAAC,EAAE,CAAE;MAAA0E,QAAA,EAC7D3E;IAAK;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED9F,OAAA,CAACjB,GAAG;MAACwH,SAAS,EAAC,MAAM;MAAAb,QAAA,eACnB1F,OAAA,CAAChB,GAAG;QAAC2H,EAAE,EAAE,CAAE;QAAAjB,QAAA,eACT1F,OAAA;UAAKuG,SAAS,EAAC,mBAAmB;UAAAb,QAAA,gBAChC1F,OAAA,CAACN,QAAQ;YAAC6G,SAAS,EAAC;UAAqE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5F9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;YACXzE,IAAI,EAAC,MAAM;YACX0E,WAAW,EAAC,oBAAoB;YAChCrD,KAAK,EAAE7C,MAAO;YACdmG,QAAQ,EAAGvD,CAAC,IAAK3C,SAAS,CAAC2C,CAAC,CAACG,MAAM,CAACF,KAAK,CAAE;YAC3C+C,SAAS,EAAC;UAAM;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9F,OAAA,CAACjB,GAAG;MAAA2G,QAAA,eACF1F,OAAA,CAAChB,GAAG;QAAA0G,QAAA,eACF1F,OAAA,CAACd,IAAI;UAAAwG,QAAA,eACH1F,OAAA,CAACd,IAAI,CAAC6H,IAAI;YAAArB,QAAA,EACP7E,OAAO,gBACNb,OAAA;cAAKuG,SAAS,EAAC,kBAAkB;cAAAb,QAAA,gBAC/B1F,OAAA,CAACZ,OAAO;gBAAC4H,SAAS,EAAC;cAAQ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B9F,OAAA;gBAAGuG,SAAS,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,GACJhB,gBAAgB,CAACmC,MAAM,KAAK,CAAC,gBAC/BjH,OAAA;cAAKuG,SAAS,EAAC,kBAAkB;cAAAb,QAAA,gBAC/B1F,OAAA,CAACV,MAAM;gBAAC4H,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAiB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChD9F,OAAA;gBAAA0F,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B9F,OAAA;gBAAGuG,SAAS,EAAC,YAAY;gBAAAb,QAAA,EACtB/E,MAAM,GAAG,gCAAgC,GAAG;cAAsC;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,EACH,CAACnF,MAAM,iBACNX,OAAA,CAACpB,MAAM;gBAAC0G,OAAO,EAAC,SAAS;gBAACkB,OAAO,EAAEhC,eAAgB;gBAAAkB,QAAA,gBACjD1F,OAAA,CAACV,MAAM;kBAACiH,SAAS,EAAC;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEN9F,OAAA,CAACrB,KAAK;cAACwI,UAAU;cAACC,KAAK;cAAA1B,QAAA,gBACrB1F,OAAA;gBAAA0F,QAAA,eACE1F,OAAA;kBAAA0F,QAAA,gBACE1F,OAAA;oBAAA0F,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf9F,OAAA;oBAAA0F,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR9F,OAAA;gBAAA0F,QAAA,EACGZ,gBAAgB,CAACuC,GAAG,CAAE3C,MAAM;kBAAA,IAAA4C,iBAAA;kBAAA,oBAC3BtH,OAAA;oBAAA0F,QAAA,gBACE1F,OAAA;sBAAA0F,QAAA,gBACE1F,OAAA;wBAAA0F,QAAA,EAAShB,MAAM,CAACzC;sBAAI;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAC9B9F,OAAA;wBAAA2F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN9F,OAAA;wBAAOuG,SAAS,EAAC,YAAY;wBAAAb,QAAA,EAAEhB,MAAM,CAACxC;sBAAW;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC,eACL9F,OAAA;sBAAA0F,QAAA,eACE1F,OAAA,CAACX,KAAK;wBAACoG,EAAE,EAAC,MAAM;wBAACc,SAAS,EAAC,iBAAiB;wBAAAb,QAAA,EACzChB,MAAM,CAACvC;sBAAI;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACL9F,OAAA;sBAAA0F,QAAA,EAAK,EAAA4B,iBAAA,GAAA5C,MAAM,CAACtC,QAAQ,cAAAkF,iBAAA,uBAAfA,iBAAA,CAAiBrF,IAAI,KAAI;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzC9F,OAAA;sBAAA0F,QAAA,EAAKK,cAAc,CAACrB,MAAM,CAACpC,aAAa;oBAAC;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/C9F,OAAA;sBAAA0F,QAAA,EAAKK,cAAc,CAACrB,MAAM,CAACnC,cAAc;oBAAC;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChD9F,OAAA;sBAAA0F,QAAA,GAAKhB,MAAM,CAAClC,QAAQ,EAAC,SAAO;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjC9F,OAAA;sBAAA0F,QAAA,EACGP,cAAc,CAACT,MAAM,CAAC/B,QAAQ,GAAG,QAAQ,GAAG,UAAU;oBAAC;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACL9F,OAAA;sBAAA0F,QAAA,eACE1F,OAAA;wBAAKuG,SAAS,EAAC,cAAc;wBAAAb,QAAA,gBAC3B1F,OAAA,CAACpB,MAAM;0BACL0G,OAAO,EAAC,cAAc;0BACtB4B,IAAI,EAAC,IAAI;0BACTV,OAAO,EAAEA,CAAA,KAAM3B,aAAa,CAACH,MAAM,CAAE;0BACrC6C,KAAK,EAAC,aAAa;0BAAA7B,QAAA,eAEnB1F,OAAA,CAACP,KAAK;4BAAAkG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACT9F,OAAA,CAACpB,MAAM;0BACL0G,OAAO,EAAC,iBAAiB;0BACzB4B,IAAI,EAAC,IAAI;0BACTV,OAAO,EAAEA,CAAA,KAAM/B,aAAa,CAACC,MAAM,CAAE;0BACrC6C,KAAK,EAAC,aAAa;0BAAA7B,QAAA,eAEnB1F,OAAA,CAACT,MAAM;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACT9F,OAAA,CAACpB,MAAM;0BACL0G,OAAO,EAAC,gBAAgB;0BACxB4B,IAAI,EAAC,IAAI;0BACTV,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAACO,MAAM,CAACR,GAAG,CAAE;0BACxCqD,KAAK,EAAC,eAAe;0BAAA7B,QAAA,eAErB1F,OAAA,CAACR,OAAO;4BAAAmG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GA7CEpB,MAAM,CAACR,GAAG;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA8Cf,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9F,OAAA,CAACnB,KAAK;MAAC2I,IAAI,EAAErG,SAAU;MAACsG,MAAM,EAAEA,CAAA,KAAMrG,YAAY,CAAC,KAAK,CAAE;MAAC8F,IAAI,EAAC,IAAI;MAAAxB,QAAA,gBAClE1F,OAAA,CAACnB,KAAK,CAAC6I,MAAM;QAACC,WAAW;QAAAjC,QAAA,eACvB1F,OAAA,CAACnB,KAAK,CAAC+I,KAAK;UAAAlC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACf9F,OAAA,CAACnB,KAAK,CAACkI,IAAI;QAAArB,QAAA,GACR3E,KAAK,iBAAIf,OAAA,CAACb,KAAK;UAACmG,OAAO,EAAC,QAAQ;UAAAI,QAAA,EAAE3E;QAAK;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjD9F,OAAA,CAAClB,IAAI;UAAA4G,QAAA,gBACH1F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,MAAM;kBACXF,IAAI,EAAC,MAAM;kBACXuB,KAAK,EAAEzB,IAAI,CAACE,IAAK;kBACjB6E,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,mBAAmB;kBAC/BkB,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC9F,OAAA,CAAClB,IAAI,CAACkJ,MAAM;kBACV/F,IAAI,EAAC,MAAM;kBACXuB,KAAK,EAAEzB,IAAI,CAACI,IAAK;kBACjB2E,QAAQ,EAAExD,YAAa;kBACvByE,QAAQ;kBAAArC,QAAA,gBAER1F,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD9F,OAAA;oBAAQwD,KAAK,EAAC,UAAU;oBAAAkC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;YAACtB,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;cAAApC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;cACXqB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRjG,IAAI,EAAC,aAAa;cAClBuB,KAAK,EAAEzB,IAAI,CAACG,WAAY;cACxB4E,QAAQ,EAAExD,YAAa;cACvBuD,WAAW,EAAC;YAA0B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb9F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjC9F,OAAA,CAAClB,IAAI,CAACkJ,MAAM;kBACV/F,IAAI,EAAC,UAAU;kBACfuB,KAAK,EAAEzB,IAAI,CAACK,QAAS;kBACrB0E,QAAQ,EAAExD,YAAa;kBAAAoC,QAAA,gBAEvB1F,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAAkC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC3CvF,UAAU,CAAC8G,GAAG,CAAEjF,QAAQ,iBACvBpC,OAAA;oBAA2BwD,KAAK,EAAEpB,QAAQ,CAAC8B,GAAI;oBAAAwB,QAAA,EAC5CtD,QAAQ,CAACH;kBAAI,GADHG,QAAQ,CAAC8B,GAAG;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC9F,OAAA,CAAClB,IAAI,CAACkJ,MAAM;kBACV/F,IAAI,EAAC,aAAa;kBAClBuB,KAAK,EAAEzB,IAAI,CAACM,WAAY;kBACxByE,QAAQ,EAAExD,YAAa;kBAAAoC,QAAA,gBAEvB1F,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAAkC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC9CrF,aAAa,CACXsE,MAAM,CAACoD,GAAG;oBAAA,IAAAC,aAAA;oBAAA,OAAI,EAAAA,aAAA,GAAAD,GAAG,CAAC/F,QAAQ,cAAAgG,aAAA,uBAAZA,aAAA,CAAclE,GAAG,MAAKnC,IAAI,CAACK,QAAQ;kBAAA,EAAC,CAClDiF,GAAG,CAAEhF,WAAW,iBACfrC,OAAA;oBAA8BwD,KAAK,EAAEnB,WAAW,CAAC6B,GAAI;oBAAAwB,QAAA,EAClDrD,WAAW,CAACJ;kBAAI,GADNI,WAAW,CAAC6B,GAAG;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,eAAe;kBACpBuB,KAAK,EAAEzB,IAAI,CAACO,aAAc;kBAC1BwE,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,sBAAsB;kBAClCwB,GAAG,EAAC,GAAG;kBACPN,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,gBAAgB;kBACrBuB,KAAK,EAAEzB,IAAI,CAACQ,cAAe;kBAC3BuE,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,uBAAuB;kBACnCwB,GAAG,EAAC,GAAG;kBACPN,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,UAAU;kBACfuB,KAAK,EAAEzB,IAAI,CAACS,QAAS;kBACrBsE,QAAQ,EAAExD,YAAa;kBACvB+E,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAC,KAAK;kBACTP,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,eAC1B1F,OAAA,CAAClB,IAAI,CAACyJ,KAAK;kBACTpG,IAAI,EAAC,UAAU;kBACfF,IAAI,EAAC,UAAU;kBACfuG,KAAK,EAAC,QAAQ;kBACd/E,OAAO,EAAE1B,IAAI,CAACY,QAAS;kBACvBmE,QAAQ,EAAExD;gBAAa;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;YAACtB,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;cAAApC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;cACXqB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRjG,IAAI,EAAC,OAAO;cACZuB,KAAK,EAAEzB,IAAI,CAACU,KAAM;cAClBqE,QAAQ,EAAExD,YAAa;cACvBuD,WAAW,EAAC;YAA4B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb9F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;YAACtB,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;cAAApC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;cACXqB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRjG,IAAI,EAAC,qBAAqB;cAC1BuB,KAAK,EAAEzB,IAAI,CAACW,mBAAoB;cAChCoE,QAAQ,EAAExD,YAAa;cACvBuD,WAAW,EAAC;YAA4B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb9F,OAAA,CAACnB,KAAK,CAAC4J,MAAM;QAAA/C,QAAA,gBACX1F,OAAA,CAACpB,MAAM;UAAC0G,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEA,CAAA,KAAMpF,YAAY,CAAC,KAAK,CAAE;UAAAsE,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9F,OAAA,CAACpB,MAAM;UACL0G,OAAO,EAAC,SAAS;UACjBkB,OAAO,EAAE5C,UAAW;UACpB8E,QAAQ,EAAE/G,UAAW;UAAA+D,QAAA,EAEpB/D,UAAU,gBACT3B,OAAA,CAAAE,SAAA;YAAAwF,QAAA,gBACE1F,OAAA,CAACZ,OAAO;cAAC4H,SAAS,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAACX,SAAS,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3D;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR9F,OAAA,CAACnB,KAAK;MAAC2I,IAAI,EAAEnG,aAAc;MAACoG,MAAM,EAAEA,CAAA,KAAMnG,gBAAgB,CAAC,KAAK,CAAE;MAAC4F,IAAI,EAAC,IAAI;MAAAxB,QAAA,gBAC1E1F,OAAA,CAACnB,KAAK,CAAC6I,MAAM;QAACC,WAAW;QAAAjC,QAAA,eACvB1F,OAAA,CAACnB,KAAK,CAAC+I,KAAK;UAAAlC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACf9F,OAAA,CAACnB,KAAK,CAACkI,IAAI;QAAArB,QAAA,GACR3E,KAAK,iBAAIf,OAAA,CAACb,KAAK;UAACmG,OAAO,EAAC,QAAQ;UAAAI,QAAA,EAAE3E;QAAK;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAChDrE,cAAc,iBACbzB,OAAA,CAAClB,IAAI;UAAA4G,QAAA,gBACH1F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,MAAM;kBACXF,IAAI,EAAC,MAAM;kBACXuB,KAAK,EAAEzB,IAAI,CAACE,IAAK;kBACjB6E,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,mBAAmB;kBAC/BkB,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC9F,OAAA,CAAClB,IAAI,CAACkJ,MAAM;kBACV/F,IAAI,EAAC,MAAM;kBACXuB,KAAK,EAAEzB,IAAI,CAACI,IAAK;kBACjB2E,QAAQ,EAAExD,YAAa;kBACvByE,QAAQ;kBAAArC,QAAA,gBAER1F,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAAkC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9F,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD9F,OAAA;oBAAQwD,KAAK,EAAC,UAAU;oBAAAkC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;YAACtB,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;cAAApC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;cACXqB,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRjG,IAAI,EAAC,aAAa;cAClBuB,KAAK,EAAEzB,IAAI,CAACG,WAAY;cACxB4E,QAAQ,EAAExD,YAAa;cACvBuD,WAAW,EAAC;YAA0B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAEb9F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,eAAe;kBACpBuB,KAAK,EAAEzB,IAAI,CAACO,aAAc;kBAC1BwE,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,sBAAsB;kBAClCwB,GAAG,EAAC,GAAG;kBACPN,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,gBAAgB;kBACrBuB,KAAK,EAAEzB,IAAI,CAACQ,cAAe;kBAC3BuE,QAAQ,EAAExD,YAAa;kBACvBuD,WAAW,EAAC,uBAAuB;kBACnCwB,GAAG,EAAC,GAAG;kBACPN,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1B1F,OAAA,CAAClB,IAAI,CAACgJ,KAAK;kBAAApC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C9F,OAAA,CAAClB,IAAI,CAAC8H,OAAO;kBACXzE,IAAI,EAAC,QAAQ;kBACbF,IAAI,EAAC,UAAU;kBACfuB,KAAK,EAAEzB,IAAI,CAACS,QAAS;kBACrBsE,QAAQ,EAAExD,YAAa;kBACvB+E,GAAG,EAAC,GAAG;kBACPC,GAAG,EAAC,KAAK;kBACTP,QAAQ;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,eACT1F,OAAA,CAAClB,IAAI,CAAC+I,KAAK;gBAACtB,SAAS,EAAC,MAAM;gBAAAb,QAAA,eAC1B1F,OAAA,CAAClB,IAAI,CAACyJ,KAAK;kBACTpG,IAAI,EAAC,UAAU;kBACfF,IAAI,EAAC,UAAU;kBACfuG,KAAK,EAAC,QAAQ;kBACd/E,OAAO,EAAE1B,IAAI,CAACY,QAAS;kBACvBmE,QAAQ,EAAExD;gBAAa;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb9F,OAAA,CAACnB,KAAK,CAAC4J,MAAM;QAAA/C,QAAA,gBACX1F,OAAA,CAACpB,MAAM;UAAC0G,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEA,CAAA,KAAMlF,gBAAgB,CAAC,KAAK,CAAE;UAAAoE,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9F,OAAA,CAACpB,MAAM;UACL0G,OAAO,EAAC,SAAS;UACjBkB,OAAO,EAAExC,UAAW;UACpB0E,QAAQ,EAAE/G,UAAW;UAAA+D,QAAA,EAEpB/D,UAAU,gBACT3B,OAAA,CAAAE,SAAA;YAAAwF,QAAA,gBACE1F,OAAA,CAACZ,OAAO;cAAC4H,SAAS,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAACX,SAAS,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3D;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGR9F,OAAA,CAACnB,KAAK;MAAC2I,IAAI,EAAEjG,aAAc;MAACkG,MAAM,EAAEA,CAAA,KAAMjG,gBAAgB,CAAC,KAAK,CAAE;MAAC0F,IAAI,EAAC,IAAI;MAAAxB,QAAA,gBAC1E1F,OAAA,CAACnB,KAAK,CAAC6I,MAAM;QAACC,WAAW;QAAAjC,QAAA,eACvB1F,OAAA,CAACnB,KAAK,CAAC+I,KAAK;UAAAlC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACf9F,OAAA,CAACnB,KAAK,CAACkI,IAAI;QAAArB,QAAA,EACRjE,cAAc,iBACbzB,OAAA;UAAA0F,QAAA,gBACE1F,OAAA,CAACjB,GAAG;YAAA2G,QAAA,gBACF1F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACT1F,OAAA;gBAAA0F,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrE,cAAc,CAACQ,IAAI;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrE,cAAc,CAACU,IAAI;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrE,cAAc,CAACS,WAAW,IAAI,KAAK;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1E9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,cAAc,CAAC1D,cAAc,CAACkB,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC;cAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACN9F,OAAA,CAAChB,GAAG;cAAC2H,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACT1F,OAAA;gBAAA0F,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACC,cAAc,CAACtE,cAAc,CAACa,aAAa,CAAC;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACC,cAAc,CAACtE,cAAc,CAACc,cAAc,CAAC;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjF9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrE,cAAc,CAACe,QAAQ,EAAC,SAAO;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClE9F,OAAA;gBAAA0F,QAAA,gBAAG1F,OAAA;kBAAA0F,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI6C,IAAI,CAAClH,cAAc,CAACmH,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELrE,cAAc,CAACgB,KAAK,iBACnBzC,OAAA;YAAKuG,SAAS,EAAC,MAAM;YAAAb,QAAA,gBACnB1F,OAAA;cAAA0F,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3B9F,OAAA;cAAA0F,QAAA,EAAIjE,cAAc,CAACgB;YAAK;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CACN,EAEArE,cAAc,CAACiB,mBAAmB,iBACjC1C,OAAA;YAAKuG,SAAS,EAAC,MAAM;YAAAb,QAAA,gBACnB1F,OAAA;cAAA0F,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B9F,OAAA;cAAA0F,QAAA,EAAIjE,cAAc,CAACiB;YAAmB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb9F,OAAA,CAACnB,KAAK,CAAC4J,MAAM;QAAA/C,QAAA,eACX1F,OAAA,CAACpB,MAAM;UAAC0G,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEA,CAAA,KAAMhF,gBAAgB,CAAC,KAAK,CAAE;UAAAkE,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC1F,EAAA,CA3uBID,eAAe;AAAA2I,EAAA,GAAf3I,eAAe;AA6uBrB,eAAeA,eAAe;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}