#!/bin/bash

echo "========================================"
echo "   Morya Insurance Management System"
echo "========================================"
echo

echo "Starting MongoDB..."
echo "Please ensure MongoDB is installed and running"
echo "If using Docker: docker run -d -p 27017:27017 --name mongodb mongo:latest"
echo

echo "Starting Backend Server..."
cd server
gnome-terminal --title="Backend Server" -- bash -c "npm run dev; exec bash" 2>/dev/null || \
xterm -title "Backend Server" -e "npm run dev; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm run dev"' 2>/dev/null || \
echo "Please manually start the backend server with: cd server && npm run dev"
cd ..

echo
echo "Starting Frontend Application..."
cd moryaInsure
gnome-terminal --title="Frontend App" -- bash -c "npm start; exec bash" 2>/dev/null || \
xterm -title "Frontend App" -e "npm start; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd '$(pwd)' && npm start"' 2>/dev/null || \
echo "Please manually start the frontend with: cd moryaInsure && npm start"
cd ..

echo
echo "========================================"
echo "    Application Starting..."
echo "========================================"
echo
echo "Backend will be available at: http://localhost:5000"
echo "Frontend will be available at: http://localhost:3000"
echo
echo "Demo Credentials:"
echo "Admin: <EMAIL> / admin123"
echo "Employee: <EMAIL> / emp123"
echo "Customer: <EMAIL> / cust123"
echo
echo "Press Enter to continue..."
read
