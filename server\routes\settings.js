const express = require('express');
const { body, validationResult } = require('express-validator');
const SystemSettings = require('../models/SystemSettings');
const { authenticate, adminOnly } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/settings
// @desc    Get system settings
// @access  Private (Admin only)
router.get('/', authenticate, adminOnly, async (req, res) => {
  try {
    const settings = await SystemSettings.getSettings();
    
    res.json({
      success: true,
      data: { settings }
    });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching settings'
    });
  }
});

// @route   PUT /api/settings
// @desc    Update system settings
// @access  Private (Admin only)
router.put('/', authenticate, adminOnly, [
  body('organizationName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Organization name must be between 1 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Please provide a valid phone number'),
  body('address.street')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Street address must be between 1 and 200 characters'),
  body('address.city')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City must be between 1 and 100 characters'),
  body('address.country')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Get current settings or create default
    let settings = await SystemSettings.findOne();
    if (!settings) {
      settings = await SystemSettings.getSettings();
    }

    // Update fields from request body
    const { organizationName, email, phone, address } = req.body;

    if (organizationName) settings.organizationName = organizationName;
    if (email) settings.email = email;
    if (phone) settings.phone = phone;

    if (address) {
      if (address.street) settings.address.street = address.street;
      if (address.city) settings.address.city = address.city;
      if (address.state) settings.address.state = address.state;
      if (address.zipCode) settings.address.zipCode = address.zipCode;
      if (address.country) settings.address.country = address.country;
    }

    settings.lastUpdatedBy = req.user._id;
    settings.lastUpdatedAt = new Date();

    await settings.save();

    res.json({
      success: true,
      message: 'Settings saved successfully',
      data: { settings }
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating settings'
    });
  }
});

// @route   GET /api/settings/public
// @desc    Get public system settings (no auth required)
// @access  Public
router.get('/public', async (req, res) => {
  try {
    const settings = await SystemSettings.getSettings();
    
    // Return only public information
    const publicSettings = {
      organizationName: settings.organizationName,
      organizationLogo: settings.organizationLogo,
      email: settings.email,
      phone: settings.phone,
      website: settings.website,
      address: settings.address,
      businessHours: settings.businessHours,
      theme: settings.theme,
      localization: {
        currency: settings.localization.currency,
        currencySymbol: settings.localization.currencySymbol,
        dateFormat: settings.localization.dateFormat,
        timeFormat: settings.localization.timeFormat,
        timezone: settings.localization.timezone,
        language: settings.localization.language
      }
    };
    
    res.json({
      success: true,
      data: { settings: publicSettings }
    });
  } catch (error) {
    console.error('Get public settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching public settings'
    });
  }
});

// @route   PUT /api/settings/theme
// @desc    Update theme settings
// @access  Private (Admin only)
router.put('/theme', authenticate, adminOnly, [
  body('primaryColor')
    .optional()
    .isHexColor()
    .withMessage('Primary color must be a valid hex color'),
  body('secondaryColor')
    .optional()
    .isHexColor()
    .withMessage('Secondary color must be a valid hex color')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { primaryColor, secondaryColor, logoUrl, faviconUrl } = req.body;
    
    const settings = await SystemSettings.getSettings();
    
    if (primaryColor) settings.theme.primaryColor = primaryColor;
    if (secondaryColor) settings.theme.secondaryColor = secondaryColor;
    if (logoUrl) settings.theme.logoUrl = logoUrl;
    if (faviconUrl) settings.theme.faviconUrl = faviconUrl;
    
    settings.lastUpdatedBy = req.user._id;
    settings.lastUpdatedAt = new Date();
    
    await settings.save();

    res.json({
      success: true,
      message: 'Theme settings updated successfully',
      data: { theme: settings.theme }
    });
  } catch (error) {
    console.error('Update theme error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating theme'
    });
  }
});

// @route   PUT /api/settings/security
// @desc    Update security settings
// @access  Private (Admin only)
router.put('/security', authenticate, adminOnly, [
  body('passwordMinLength')
    .optional()
    .isInt({ min: 6, max: 20 })
    .withMessage('Password minimum length must be between 6 and 20'),
  body('maxLoginAttempts')
    .optional()
    .isInt({ min: 3, max: 10 })
    .withMessage('Max login attempts must be between 3 and 10'),
  body('sessionTimeout')
    .optional()
    .isInt({ min: 300, max: 86400 })
    .withMessage('Session timeout must be between 5 minutes and 24 hours')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const securityUpdates = req.body;
    const settings = await SystemSettings.getSettings();
    
    Object.keys(securityUpdates).forEach(key => {
      if (securityUpdates[key] !== undefined) {
        settings.securitySettings[key] = securityUpdates[key];
      }
    });
    
    settings.lastUpdatedBy = req.user._id;
    settings.lastUpdatedAt = new Date();
    
    await settings.save();

    res.json({
      success: true,
      message: 'Security settings updated successfully',
      data: { securitySettings: settings.securitySettings }
    });
  } catch (error) {
    console.error('Update security settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating security settings'
    });
  }
});

// @route   PUT /api/settings/notifications
// @desc    Update notification settings
// @access  Private (Admin only)
router.put('/notifications', authenticate, adminOnly, async (req, res) => {
  try {
    const { emailNotifications, smsNotifications, pushNotifications } = req.body;
    
    const settings = await SystemSettings.getSettings();
    
    if (emailNotifications !== undefined) {
      settings.notificationSettings.emailNotifications = emailNotifications;
    }
    if (smsNotifications !== undefined) {
      settings.notificationSettings.smsNotifications = smsNotifications;
    }
    if (pushNotifications !== undefined) {
      settings.notificationSettings.pushNotifications = pushNotifications;
    }
    
    settings.lastUpdatedBy = req.user._id;
    settings.lastUpdatedAt = new Date();
    
    await settings.save();

    res.json({
      success: true,
      message: 'Notification settings updated successfully',
      data: { notificationSettings: settings.notificationSettings }
    });
  } catch (error) {
    console.error('Update notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating notification settings'
    });
  }
});

// @route   POST /api/settings/reset
// @desc    Reset settings to default
// @access  Private (Admin only)
router.post('/reset', authenticate, adminOnly, async (req, res) => {
  try {
    // Delete existing settings
    await SystemSettings.deleteMany({});
    
    // Create new default settings
    const settings = await SystemSettings.getSettings();
    settings.lastUpdatedBy = req.user._id;
    await settings.save();

    res.json({
      success: true,
      message: 'Settings reset to default successfully',
      data: { settings }
    });
  } catch (error) {
    console.error('Reset settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while resetting settings'
    });
  }
});

module.exports = router;
