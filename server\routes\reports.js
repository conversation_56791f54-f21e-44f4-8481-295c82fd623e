const express = require('express');
const Policy = require('../models/Policy');
const Ticket = require('../models/Ticket');
const User = require('../models/User');
const { authenticate, adminOrEmployee } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/reports/dashboard
// @desc    Get dashboard statistics
// @access  Private (Admin/Employee)
router.get('/dashboard', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const [
      totalPolicies,
      activePolicies,
      pendingPolicies,
      totalTickets,
      openTickets,
      totalUsers,
      totalCustomers,
      totalEmployees
    ] = await Promise.all([
      Policy.countDocuments(),
      Policy.countDocuments({ status: 'active' }),
      Policy.countDocuments({ status: 'pending' }),
      Ticket.countDocuments(),
      Ticket.countDocuments({ status: { $in: ['open', 'in-progress'] } }),
      User.countDocuments({ isActive: true }),
      User.countDocuments({ role: 'customer', isActive: true }),
      User.countDocuments({ role: 'employee', isActive: true })
    ]);

    // Get monthly revenue (sum of premium amounts for active policies)
    const monthlyRevenue = await Policy.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: null, total: { $sum: '$premiumAmount' } } }
    ]);

    // Get policy distribution by type
    const policyDistribution = await Policy.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    // Get recent activities (last 10 policies and tickets)
    const recentPolicies = await Policy.find()
      .populate('policyHolder', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5);

    const recentTickets = await Ticket.find()
      .populate('submittedBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5);

    res.json({
      success: true,
      data: {
        overview: {
          totalPolicies,
          activePolicies,
          pendingPolicies,
          totalTickets,
          openTickets,
          totalUsers,
          totalCustomers,
          totalEmployees,
          monthlyRevenue: monthlyRevenue[0]?.total || 0
        },
        policyDistribution,
        recentActivities: {
          policies: recentPolicies,
          tickets: recentTickets
        }
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard statistics'
    });
  }
});

// @route   GET /api/reports/policies
// @desc    Get policy reports
// @access  Private (Admin/Employee)
router.get('/policies', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Policies created in the period
    const newPolicies = await Policy.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Policies by status
    const statusDistribution = await Policy.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Policies by type
    const typeDistribution = await Policy.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    // Expiring policies (next 30 days)
    const expiringPolicies = await Policy.findExpiringSoon(30);

    // Top agents by policies
    const topAgents = await Policy.aggregate([
      { $match: { assignedAgent: { $exists: true } } },
      { $group: { _id: '$assignedAgent', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'agent'
        }
      },
      { $unwind: '$agent' },
      {
        $project: {
          name: { $concat: ['$agent.firstName', ' ', '$agent.lastName'] },
          count: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          newPolicies,
          expiringCount: expiringPolicies.length
        },
        distributions: {
          status: statusDistribution,
          type: typeDistribution
        },
        expiringPolicies: expiringPolicies.slice(0, 10), // Limit to 10 for display
        topAgents
      }
    });
  } catch (error) {
    console.error('Policy reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating policy reports'
    });
  }
});

// @route   GET /api/reports/tickets
// @desc    Get ticket reports
// @access  Private (Admin/Employee)
router.get('/tickets', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Tickets created in the period
    const newTickets = await Ticket.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Tickets resolved in the period
    const resolvedTickets = await Ticket.countDocuments({
      status: 'resolved',
      'resolution.resolvedAt': { $gte: startDate }
    });

    // Tickets by status
    const statusDistribution = await Ticket.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Tickets by priority
    const priorityDistribution = await Ticket.aggregate([
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ]);

    // Average resolution time
    const avgResolutionTime = await Ticket.aggregate([
      { $match: { 'resolution.resolutionTime': { $exists: true } } },
      { $group: { _id: null, avgTime: { $avg: '$resolution.resolutionTime' } } }
    ]);

    // Overdue tickets
    const overdueTickets = await Ticket.findOverdue();

    res.json({
      success: true,
      data: {
        summary: {
          newTickets,
          resolvedTickets,
          overdueCount: overdueTickets.length,
          avgResolutionTime: avgResolutionTime[0]?.avgTime || 0
        },
        distributions: {
          status: statusDistribution,
          priority: priorityDistribution
        },
        overdueTickets: overdueTickets.slice(0, 10)
      }
    });
  } catch (error) {
    console.error('Ticket reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating ticket reports'
    });
  }
});

module.exports = router;
