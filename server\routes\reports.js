const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Policy = require('../models/Policy');
const Ticket = require('../models/Ticket');
const User = require('../models/User');
const Claim = require('../models/Claim');
const { authenticate, adminOnly, adminOrEmployee } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/reports/dashboard
// @desc    Get dashboard statistics (role-based)
// @access  Private
router.get('/dashboard', authenticate, async (req, res) => {
  try {
    let dashboardData = {};

    if (req.user.role === 'admin') {
      // Admin dashboard - full system overview
      const [
        totalPolicies,
        activePolicies,
        pendingPolicies,
        totalTickets,
        openTickets,
        totalUsers,
        totalCustomers,
        totalEmployees
      ] = await Promise.all([
        Policy.countDocuments(),
        Policy.countDocuments({ status: 'active' }),
        Policy.countDocuments({ status: 'pending' }),
        Ticket.countDocuments(),
        Ticket.countDocuments({ status: { $in: ['open', 'in-progress'] } }),
        User.countDocuments({ isActive: true }),
        User.countDocuments({ role: 'customer', isActive: true }),
        User.countDocuments({ role: 'employee', isActive: true })
      ]);

      // Get monthly revenue
      const monthlyRevenue = await Policy.aggregate([
        { $match: { status: 'active' } },
        { $group: { _id: null, total: { $sum: '$premiumAmount' } } }
      ]);

      // Get policy distribution by type
      const policyDistribution = await Policy.aggregate([
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]);

      // Get recent activities
      const recentPolicies = await Policy.find()
        .populate('policyHolder', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(5);

      const recentTickets = await Ticket.find()
        .populate('submittedBy', 'firstName lastName')
        .sort({ createdAt: -1 })
        .limit(5);

      dashboardData = {
        overview: {
          totalPolicies,
          activePolicies,
          pendingPolicies,
          totalTickets,
          openTickets,
          totalUsers,
          totalCustomers,
          totalEmployees,
          monthlyRevenue: monthlyRevenue[0]?.total || 0
        },
        policyDistribution,
        recentActivities: {
          policies: recentPolicies,
          tickets: recentTickets
        }
      };

    } else if (req.user.role === 'employee') {
      // Employee dashboard - assigned work overview
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const [
        assignedPolicies,
        pendingReviews,
        assignedTickets,
        resolvedTickets,
        completedToday,
        assignedClaims,
        monthlyCompleted
      ] = await Promise.all([
        Policy.countDocuments({ assignedAgent: req.user._id }),
        Policy.countDocuments({
          assignedAgent: req.user._id,
          status: { $in: ['pending', 'draft'] }
        }),
        Ticket.countDocuments({ assignedTo: req.user._id }),
        Ticket.countDocuments({
          assignedTo: req.user._id,
          status: { $in: ['resolved', 'closed'] }
        }),
        // Count work completed today
        Promise.all([
          Policy.countDocuments({
            assignedAgent: req.user._id,
            updatedAt: { $gte: today, $lt: tomorrow },
            status: { $in: ['approved', 'active'] }
          }),
          Ticket.countDocuments({
            assignedTo: req.user._id,
            updatedAt: { $gte: today, $lt: tomorrow },
            status: { $in: ['resolved', 'closed'] }
          })
        ]).then(([policies, tickets]) => policies + tickets),
        // Claims assigned to employee (if Claim model exists)
        (() => {
          try {
            const Claim = require('../models/Claim');
            return Claim.countDocuments({ assignedAdjuster: req.user._id });
          } catch (error) {
            return 0;
          }
        })(),
        // Monthly completed work
        Promise.all([
          Policy.countDocuments({
            assignedAgent: req.user._id,
            updatedAt: { $gte: currentMonth },
            status: { $in: ['approved', 'active'] }
          }),
          Ticket.countDocuments({
            assignedTo: req.user._id,
            updatedAt: { $gte: currentMonth },
            status: { $in: ['resolved', 'closed'] }
          })
        ]).then(([policies, tickets]) => policies + tickets)
      ]);

      const monthlyTarget = 50; // This could be configurable per employee
      const achievement = Math.round((monthlyCompleted / monthlyTarget) * 100);

      // Get assigned work for employee
      const myPolicies = await Policy.find({ assignedAgent: req.user._id })
        .populate('policyHolder', 'firstName lastName email')
        .populate('category', 'name')
        .sort({ updatedAt: -1 })
        .limit(10);

      const myTickets = await Ticket.find({ assignedTo: req.user._id })
        .populate('submittedBy', 'firstName lastName email')
        .populate('category', 'name')
        .sort({ updatedAt: -1 })
        .limit(10);

      dashboardData = {
        overview: {
          assignedPolicies,
          pendingReviews,
          completedToday,
          assignedTickets,
          resolvedTickets,
          assignedClaims,
          monthlyTarget,
          achievement,
          monthlyCompleted
        },
        myWork: {
          policies: myPolicies,
          tickets: myTickets
        }
      };

    } else if (req.user.role === 'customer') {
      // Customer dashboard - personal overview
      const [
        myPolicies,
        activePolicies,
        myTickets,
        openTickets
      ] = await Promise.all([
        Policy.countDocuments({ policyHolder: req.user._id }),
        Policy.countDocuments({ policyHolder: req.user._id, status: 'active' }),
        Ticket.countDocuments({ submittedBy: req.user._id }),
        Ticket.countDocuments({ submittedBy: req.user._id, status: { $in: ['open', 'in-progress'] } })
      ]);

      // Get customer's policies and tickets
      const policies = await Policy.find({ policyHolder: req.user._id })
        .sort({ createdAt: -1 })
        .limit(5);

      const tickets = await Ticket.find({ submittedBy: req.user._id })
        .sort({ createdAt: -1 })
        .limit(5);

      // Calculate premium due (mock calculation)
      const premiumDue = activePolicies * 500;
      const nextPaymentDate = new Date();
      nextPaymentDate.setMonth(nextPaymentDate.getMonth() + 1);

      dashboardData = {
        overview: {
          activePolicies,
          totalPolicies: myPolicies,
          openTickets,
          totalTickets: myTickets,
          premiumDue,
          nextPaymentDate: nextPaymentDate.toISOString().split('T')[0],
          coverageAmount: activePolicies * 100000,
          loyaltyPoints: Math.floor(Math.random() * 5000),
          membershipLevel: activePolicies > 3 ? 'Gold' : activePolicies > 1 ? 'Silver' : 'Bronze'
        },
        myData: {
          policies,
          tickets
        }
      };
    }

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard statistics'
    });
  }
});

// @route   GET /api/reports/policies
// @desc    Get policy reports
// @access  Private (Admin/Employee)
router.get('/policies', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Policies created in the period
    const newPolicies = await Policy.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Policies by status
    const statusDistribution = await Policy.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Policies by type
    const typeDistribution = await Policy.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    // Expiring policies (next 30 days)
    const expiringPolicies = await Policy.findExpiringSoon(30);

    // Top agents by policies
    const topAgents = await Policy.aggregate([
      { $match: { assignedAgent: { $exists: true } } },
      { $group: { _id: '$assignedAgent', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'agent'
        }
      },
      { $unwind: '$agent' },
      {
        $project: {
          name: { $concat: ['$agent.firstName', ' ', '$agent.lastName'] },
          count: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          newPolicies,
          expiringCount: expiringPolicies.length
        },
        distributions: {
          status: statusDistribution,
          type: typeDistribution
        },
        expiringPolicies: expiringPolicies.slice(0, 10), // Limit to 10 for display
        topAgents
      }
    });
  } catch (error) {
    console.error('Policy reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating policy reports'
    });
  }
});

// @route   GET /api/reports/tickets
// @desc    Get ticket reports
// @access  Private (Admin/Employee)
router.get('/tickets', authenticate, adminOrEmployee, async (req, res) => {
  try {
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Tickets created in the period
    const newTickets = await Ticket.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Tickets resolved in the period
    const resolvedTickets = await Ticket.countDocuments({
      status: 'resolved',
      'resolution.resolvedAt': { $gte: startDate }
    });

    // Tickets by status
    const statusDistribution = await Ticket.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Tickets by priority
    const priorityDistribution = await Ticket.aggregate([
      { $group: { _id: '$priority', count: { $sum: 1 } } }
    ]);

    // Average resolution time
    const avgResolutionTime = await Ticket.aggregate([
      { $match: { 'resolution.resolutionTime': { $exists: true } } },
      { $group: { _id: null, avgTime: { $avg: '$resolution.resolutionTime' } } }
    ]);

    // Overdue tickets
    const overdueTickets = await Ticket.findOverdue();

    res.json({
      success: true,
      data: {
        summary: {
          newTickets,
          resolvedTickets,
          overdueCount: overdueTickets.length,
          avgResolutionTime: avgResolutionTime[0]?.avgTime || 0
        },
        distributions: {
          status: statusDistribution,
          priority: priorityDistribution
        },
        overdueTickets: overdueTickets.slice(0, 10)
      }
    });
  } catch (error) {
    console.error('Ticket reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating ticket reports'
    });
  }
});

// @route   GET /api/reports/performance
// @desc    Get performance and profitability reports (Admin only)
// @access  Private (Admin)
router.get('/performance', authenticate, adminOnly, async (req, res) => {
  try {
    const { startDate, endDate, employeeId } = req.query;

    // Build date filter
    let dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
    }

    // Build employee filter
    let employeeFilter = {};
    if (employeeId) {
      employeeFilter.assignedAgent = employeeId;
    }

    // Get revenue data (sum of premiums from active policies)
    const revenueData = await Policy.aggregate([
      {
        $match: {
          status: 'active',
          ...dateFilter,
          ...employeeFilter
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$premiumAmount' },
          totalPolicies: { $sum: 1 },
          averagePremium: { $avg: '$premiumAmount' }
        }
      }
    ]);

    // Get employee performance data
    const employeePerformance = await Policy.aggregate([
      {
        $match: {
          status: { $in: ['active', 'approved'] },
          assignedAgent: { $exists: true },
          ...dateFilter
        }
      },
      {
        $group: {
          _id: '$assignedAgent',
          totalPolicies: { $sum: 1 },
          totalRevenue: { $sum: '$premiumAmount' },
          averagePremium: { $avg: '$premiumAmount' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'employee'
        }
      },
      { $unwind: '$employee' },
      {
        $project: {
          employeeId: '$_id',
          employeeName: {
            $concat: ['$employee.firstName', ' ', '$employee.lastName']
          },
          totalPolicies: 1,
          totalRevenue: 1,
          averagePremium: 1
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Calculate profit margins (simplified calculation)
    const revenue = revenueData[0]?.totalRevenue || 0;
    const estimatedCosts = revenue * 0.3; // Assume 30% costs
    const profit = revenue - estimatedCosts;
    const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;

    // Get policy type distribution
    const policyTypeDistribution = await Policy.aggregate([
      {
        $match: {
          status: 'active',
          ...dateFilter,
          ...employeeFilter
        }
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          revenue: { $sum: '$premiumAmount' }
        }
      },
      { $sort: { revenue: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalRevenue: revenue,
          totalPolicies: revenueData[0]?.totalPolicies || 0,
          averagePremium: revenueData[0]?.averagePremium || 0,
          estimatedCosts,
          profit,
          profitMargin: Math.round(profitMargin * 100) / 100
        },
        employeePerformance,
        policyTypeDistribution,
        dateRange: {
          startDate: startDate || null,
          endDate: endDate || null
        }
      }
    });
  } catch (error) {
    console.error('Performance report error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating performance report'
    });
  }
});

// @route   PUT /api/reports/assign-policy/:policyId
// @desc    Assign policy to staff member (Admin only)
// @access  Private (Admin)
router.put('/assign-policy/:policyId', authenticate, adminOnly, async (req, res) => {
  try {
    const { staffId } = req.body;

    if (!staffId) {
      return res.status(400).json({
        success: false,
        message: 'Staff ID is required'
      });
    }

    // Check if staff member exists and is active
    const staff = await User.findOne({
      _id: staffId,
      role: { $in: ['employee', 'admin'] },
      isActive: true
    });

    if (!staff) {
      return res.status(400).json({
        success: false,
        message: 'Staff member not found or inactive'
      });
    }

    // Update policy assignment
    const policy = await Policy.findByIdAndUpdate(
      req.params.policyId,
      { assignedAgent: staffId },
      { new: true }
    ).populate('assignedAgent', 'firstName lastName email');

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }

    res.json({
      success: true,
      message: `Policy assigned to ${staff.firstName} ${staff.lastName}`,
      data: { policy }
    });
  } catch (error) {
    console.error('Assign policy error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while assigning policy'
    });
  }
});

// @route   PUT /api/reports/assign-ticket/:ticketId
// @desc    Assign ticket to staff member (Admin only)
// @access  Private (Admin)
router.put('/assign-ticket/:ticketId', authenticate, adminOnly, async (req, res) => {
  try {
    const { staffId } = req.body;

    if (!staffId) {
      return res.status(400).json({
        success: false,
        message: 'Staff ID is required'
      });
    }

    // Check if staff member exists and is active
    const staff = await User.findOne({
      _id: staffId,
      role: { $in: ['employee', 'admin'] },
      isActive: true
    });

    if (!staff) {
      return res.status(400).json({
        success: false,
        message: 'Staff member not found or inactive'
      });
    }

    // Update ticket assignment
    const ticket = await Ticket.findByIdAndUpdate(
      req.params.ticketId,
      {
        assignedTo: staffId,
        status: 'in-progress' // Update status when assigning
      },
      { new: true }
    ).populate('assignedTo', 'firstName lastName email');

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    res.json({
      success: true,
      message: `Ticket assigned to ${staff.firstName} ${staff.lastName}`,
      data: { ticket }
    });
  } catch (error) {
    console.error('Assign ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while assigning ticket'
    });
  }
});

// @route   POST /api/reports/generate
// @desc    Generate reports in PDF/Excel/CSV format
// @access  Private (Admin/Employee)
router.post('/generate', authenticate, [
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('endDate').isISO8601().withMessage('Valid end date is required'),
  body('format').isIn(['pdf', 'excel', 'csv']).withMessage('Invalid format'),
  body('reportType').isIn(['policies', 'claims', 'tickets', 'revenue', 'performance']).withMessage('Invalid report type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { startDate, endDate, format, reportType, filters = {} } = req.body;

    const start = new Date(startDate);
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999); // Include the entire end date

    let data = [];
    let reportTitle = '';
    let filename = '';

    // Generate data based on report type
    switch (reportType) {
      case 'policies':
        reportTitle = 'Policies Report';
        filename = `policies_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}`;

        let policyQuery = {
          createdAt: { $gte: start, $lte: end }
        };

        // Role-based filtering
        if (req.user.role === 'employee') {
          policyQuery.assignedAgent = req.user._id;
        }

        if (filters.status) policyQuery.status = filters.status;
        if (filters.type) policyQuery.type = filters.type;
        if (filters.assignedAgent && req.user.role === 'admin') {
          policyQuery.assignedAgent = filters.assignedAgent;
        }

        data = await Policy.find(policyQuery)
          .populate('policyHolder', 'firstName lastName email phone')
          .populate('category', 'name')
          .populate('subCategory', 'name')
          .populate('assignedAgent', 'firstName lastName')
          .sort({ createdAt: -1 });
        break;

      case 'claims':
        reportTitle = 'Claims Report';
        filename = `claims_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}`;

        let claimQuery = {
          createdAt: { $gte: start, $lte: end }
        };

        if (req.user.role === 'employee') {
          claimQuery.assignedAdjuster = req.user._id;
        }

        if (filters.status) claimQuery.status = filters.status;

        const Claim = require('../models/Claim');
        data = await Claim.find(claimQuery)
          .populate('claimant', 'firstName lastName email phone')
          .populate('policy', 'policyNumber type')
          .populate('assignedAdjuster', 'firstName lastName')
          .sort({ createdAt: -1 });
        break;

      case 'tickets':
        reportTitle = 'Support Tickets Report';
        filename = `tickets_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}`;

        let ticketQuery = {
          createdAt: { $gte: start, $lte: end }
        };

        if (req.user.role === 'employee') {
          ticketQuery.assignedTo = req.user._id;
        } else if (req.user.role === 'customer') {
          ticketQuery.submittedBy = req.user._id;
        }

        if (filters.status) ticketQuery.status = filters.status;
        if (filters.priority) ticketQuery.priority = filters.priority;

        data = await Ticket.find(ticketQuery)
          .populate('submittedBy', 'firstName lastName email phone')
          .populate('assignedTo', 'firstName lastName')
          .populate('category', 'name')
          .sort({ createdAt: -1 });
        break;

      case 'revenue':
        if (req.user.role !== 'admin') {
          return res.status(403).json({
            success: false,
            message: 'Access denied. Admin privileges required.'
          });
        }

        reportTitle = 'Revenue Report';
        filename = `revenue_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}`;

        data = await Policy.aggregate([
          {
            $match: {
              createdAt: { $gte: start, $lte: end },
              status: { $in: ['active', 'approved'] }
            }
          },
          {
            $group: {
              _id: {
                year: { $year: '$createdAt' },
                month: { $month: '$createdAt' },
                type: '$type'
              },
              totalPremium: { $sum: '$premiumAmount' },
              totalCoverage: { $sum: '$coverageAmount' },
              policyCount: { $sum: 1 }
            }
          },
          {
            $sort: { '_id.year': 1, '_id.month': 1, '_id.type': 1 }
          }
        ]);
        break;

      case 'performance':
        if (req.user.role !== 'admin') {
          return res.status(403).json({
            success: false,
            message: 'Access denied. Admin privileges required.'
          });
        }

        reportTitle = 'Employee Performance Report';
        filename = `performance_report_${start.toISOString().split('T')[0]}_to_${end.toISOString().split('T')[0]}`;

        data = await Policy.aggregate([
          {
            $match: {
              createdAt: { $gte: start, $lte: end },
              assignedAgent: { $exists: true }
            }
          },
          {
            $group: {
              _id: '$assignedAgent',
              totalPolicies: { $sum: 1 },
              totalPremium: { $sum: '$premiumAmount' },
              totalCoverage: { $sum: '$coverageAmount' },
              activePolicies: {
                $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
              }
            }
          },
          {
            $lookup: {
              from: 'users',
              localField: '_id',
              foreignField: '_id',
              as: 'agent'
            }
          },
          { $unwind: '$agent' },
          {
            $project: {
              agentName: { $concat: ['$agent.firstName', ' ', '$agent.lastName'] },
              agentEmail: '$agent.email',
              totalPolicies: 1,
              totalPremium: 1,
              totalCoverage: 1,
              activePolicies: 1,
              conversionRate: {
                $multiply: [
                  { $divide: ['$activePolicies', '$totalPolicies'] },
                  100
                ]
              }
            }
          },
          { $sort: { totalPremium: -1 } }
        ]);
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type'
        });
    }

    // Generate file based on format
    if (format === 'csv') {
      const csv = require('csv-stringify');

      // Convert data to CSV format
      let csvData = [];
      let headers = [];

      if (reportType === 'policies') {
        headers = ['Policy Number', 'Policy Holder', 'Type', 'Category', 'Premium', 'Coverage', 'Status', 'Created Date'];
        csvData = data.map(policy => [
          policy.policyNumber,
          `${policy.policyHolder?.firstName} ${policy.policyHolder?.lastName}`,
          policy.type,
          policy.category?.name,
          policy.premiumAmount,
          policy.coverageAmount,
          policy.status,
          policy.createdAt.toLocaleDateString()
        ]);
      } else if (reportType === 'tickets') {
        headers = ['Ticket ID', 'Subject', 'Customer', 'Status', 'Priority', 'Created Date'];
        csvData = data.map(ticket => [
          ticket.ticketId,
          ticket.subject,
          `${ticket.submittedBy?.firstName} ${ticket.submittedBy?.lastName}`,
          ticket.status,
          ticket.priority,
          ticket.createdAt.toLocaleDateString()
        ]);
      }

      csvData.unshift(headers);

      csv.stringify(csvData, (err, output) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: 'Error generating CSV report'
          });
        }

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
        res.send(output);
      });
    } else {
      // For now, return JSON data for PDF and Excel (can be enhanced with actual file generation)
      res.json({
        success: true,
        message: `${reportTitle} generated successfully`,
        data: {
          reportType,
          format,
          dateRange: { startDate, endDate },
          recordCount: data.length,
          data: data.slice(0, 100) // Limit response size
        }
      });
    }
  } catch (error) {
    console.error('Generate report error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while generating report'
    });
  }
});

module.exports = router;
