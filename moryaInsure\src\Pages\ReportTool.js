import React, { useState } from 'react';
import { Container, Row, Col, Form, But<PERSON>, Card } from 'react-bootstrap';

const ReportTool = () => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const handleGenerate = () => {
    alert(`Generating report from ${startDate} to ${endDate}`);
    // API call can go here
  };

  return (
    <Container className="mt-4">
      {/* Top Right User Display */}
      
      <div className="d-flex justify-content-end align-items-center mb-3">
        <i className="bi bi-person-fill me-2" style={{ fontSize: '1.2rem' }}></i>
        <span className="fw-semibold">Username</span>
      </div>

      {/* Main Report Section */}
      <Card className="shadow-sm p-4">
        <h4 className="mb-4 fw-bold text-uppercase">Generate Report</h4>

        <Row className="mb-3">
          <Col md={6}>
            <Form.Group>
              <Form.Label>Start Date</Form.Label>
              <Form.Control
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group>
              <Form.Label>End Date</Form.Label>
              <Form.Control
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </Form.Group>
          </Col>
        </Row>

        <Button variant="primary" onClick={handleGenerate}>
          Generate Report
        </Button>
      </Card>
    </Container>
  );
};

export default ReportTool;
