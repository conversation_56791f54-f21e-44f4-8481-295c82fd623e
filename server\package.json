{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test-server": "node test-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.10"}}