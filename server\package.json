{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test-server": "node test-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "express-winston": "^4.2.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "redis": "^5.5.6", "socket.io": "^4.8.1", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}