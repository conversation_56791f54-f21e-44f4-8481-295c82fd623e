{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\Pages\\\\PolicyHolder.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Form, Modal, Row, Col, Container, Card, Alert, Spinner, Badge } from 'react-bootstrap';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PolicyHolder = () => {\n  _s();\n  const [policyHolders, setPolicyHolders] = useState([]);\n  const [policies, setPolicies] = useState([]);\n  const [search, setSearch] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Modal states\n  const [showModal, setShowModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedHolder, setSelectedHolder] = useState(null);\n  const [submitting, setSubmitting] = useState(false);\n\n  // Form state\n  const [form, setForm] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    dateOfBirth: '',\n    gender: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'India'\n    },\n    emergencyContact: {\n      name: '',\n      relationship: '',\n      phone: ''\n    },\n    occupation: '',\n    annualIncome: '',\n    status: 'active'\n  });\n  useEffect(() => {\n    fetchPolicyHolders();\n    fetchPolicies();\n  }, []);\n  const fetchPolicyHolders = async () => {\n    try {\n      setLoading(true);\n      const response = await policyHoldersAPI.getPolicyHolders();\n      if (response.success) {\n        setPolicyHolders(response.data.policyHolders || []);\n      } else {\n        setError('Failed to fetch policy holders');\n      }\n    } catch (error) {\n      console.error('Error fetching policy holders:', error);\n      setError('Failed to fetch policy holders');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchPolicies = async () => {\n    try {\n      const response = await policiesAPI.getPolicies();\n      if (response.success) {\n        setPolicies(response.data.policies || []);\n      }\n    } catch (error) {\n      console.error('Error fetching policies:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.includes('.')) {\n      const [parent, child] = name.split('.');\n      setForm(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent],\n          [child]: value\n        }\n      }));\n    } else {\n      setForm(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n    setError('');\n  };\n  const handleSave = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policyHoldersAPI.createPolicyHolder(form);\n      if (response.success) {\n        setSuccess('Policy holder created successfully!');\n        resetForm();\n        setShowModal(false);\n        fetchPolicyHolders();\n      } else {\n        setError(response.message || 'Failed to create policy holder');\n      }\n    } catch (error) {\n      console.error('Error creating policy holder:', error);\n      setError('Failed to create policy holder');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleEdit = async () => {\n    try {\n      setSubmitting(true);\n      setError('');\n      const response = await policyHoldersAPI.updatePolicyHolder(selectedHolder._id, form);\n      if (response.success) {\n        setSuccess('Policy holder updated successfully!');\n        setShowEditModal(false);\n        setSelectedHolder(null);\n        fetchPolicyHolders();\n      } else {\n        setError(response.message || 'Failed to update policy holder');\n      }\n    } catch (error) {\n      console.error('Error updating policy holder:', error);\n      setError('Failed to update policy holder');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleDelete = async holderId => {\n    if (window.confirm('Are you sure you want to delete this policy holder?')) {\n      try {\n        const response = await policyHoldersAPI.deletePolicyHolder(holderId);\n        if (response.success) {\n          setSuccess('Policy holder deleted successfully!');\n          fetchPolicyHolders();\n        } else {\n          setError(response.message || 'Failed to delete policy holder');\n        }\n      } catch (error) {\n        console.error('Error deleting policy holder:', error);\n        setError('Failed to delete policy holder');\n      }\n    }\n  };\n  const resetForm = () => {\n    setForm({\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      dateOfBirth: '',\n      gender: '',\n      address: {\n        street: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        country: 'India'\n      },\n      emergencyContact: {\n        name: '',\n        relationship: '',\n        phone: ''\n      },\n      occupation: '',\n      annualIncome: '',\n      status: 'active'\n    });\n  };\n  const openCreateModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n  const openEditModal = holder => {\n    var _holder$address, _holder$address2, _holder$address3, _holder$address4, _holder$address5, _holder$emergencyCont, _holder$emergencyCont2, _holder$emergencyCont3;\n    setSelectedHolder(holder);\n    setForm({\n      firstName: holder.firstName,\n      lastName: holder.lastName,\n      email: holder.email,\n      phone: holder.phone,\n      dateOfBirth: holder.dateOfBirth ? new Date(holder.dateOfBirth).toISOString().split('T')[0] : '',\n      gender: holder.gender || '',\n      address: {\n        street: ((_holder$address = holder.address) === null || _holder$address === void 0 ? void 0 : _holder$address.street) || '',\n        city: ((_holder$address2 = holder.address) === null || _holder$address2 === void 0 ? void 0 : _holder$address2.city) || '',\n        state: ((_holder$address3 = holder.address) === null || _holder$address3 === void 0 ? void 0 : _holder$address3.state) || '',\n        zipCode: ((_holder$address4 = holder.address) === null || _holder$address4 === void 0 ? void 0 : _holder$address4.zipCode) || '',\n        country: ((_holder$address5 = holder.address) === null || _holder$address5 === void 0 ? void 0 : _holder$address5.country) || 'India'\n      },\n      emergencyContact: {\n        name: ((_holder$emergencyCont = holder.emergencyContact) === null || _holder$emergencyCont === void 0 ? void 0 : _holder$emergencyCont.name) || '',\n        relationship: ((_holder$emergencyCont2 = holder.emergencyContact) === null || _holder$emergencyCont2 === void 0 ? void 0 : _holder$emergencyCont2.relationship) || '',\n        phone: ((_holder$emergencyCont3 = holder.emergencyContact) === null || _holder$emergencyCont3 === void 0 ? void 0 : _holder$emergencyCont3.phone) || ''\n      },\n      occupation: holder.occupation || '',\n      annualIncome: holder.annualIncome || '',\n      status: holder.status || 'active'\n    });\n    setShowEditModal(true);\n  };\n  const openViewModal = holder => {\n    setSelectedHolder(holder);\n    setShowViewModal(true);\n  };\n  const filteredHolders = policyHolders.filter(holder => {\n    var _holder$email, _holder$phone;\n    return `${holder.firstName} ${holder.lastName}`.toLowerCase().includes(search.toLowerCase()) || ((_holder$email = holder.email) === null || _holder$email === void 0 ? void 0 : _holder$email.toLowerCase().includes(search.toLowerCase())) || ((_holder$phone = holder.phone) === null || _holder$phone === void 0 ? void 0 : _holder$phone.includes(search));\n  });\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'active': {\n        variant: 'success',\n        text: 'Active'\n      },\n      'inactive': {\n        variant: 'secondary',\n        text: 'Inactive'\n      },\n      'suspended': {\n        variant: 'warning',\n        text: 'Suspended'\n      },\n      'blocked': {\n        variant: 'danger',\n        text: 'Blocked'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDate = dateString => {\n    return dateString ? new Date(dateString).toLocaleDateString('en-IN') : 'N/A';\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"mb-1\",\n              children: \"Policy Holders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Manage insurance policy holders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: openCreateModal,\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), \"New Policy Holder\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"secondary\",\n              children: [/*#__PURE__*/_jsxDEV(FaFileImport, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), \"Import\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"success\",\n      dismissible: true,\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      dismissible: true,\n      onClose: () => setError(''),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative\",\n          children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n            className: \"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Search policy holders...\",\n            value: search,\n            onChange: e => setSearch(e.target.value),\n            className: \"ps-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2\",\n                children: \"Loading policy holders...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this) : filteredHolders.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                size: 48,\n                className: \"text-muted mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"No Policy Holders Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: search ? 'No policy holders match your search.' : 'Start by adding your first policy holder.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), !search && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: openCreateModal,\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this), \"Add First Policy Holder\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Contact\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Occupation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Created\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: filteredHolders.map(holder => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [holder.firstName, \" \", holder.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        className: \"text-muted\",\n                        children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 341,\n                          columnNumber: 31\n                        }, this), holder.gender || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaPhone, {\n                        className: \"me-1 text-muted\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 29\n                      }, this), holder.phone]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                        className: \"me-1 text-muted\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 29\n                      }, this), holder.email]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(holder.dateOfBirth)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: holder.occupation || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(holder.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatDate(holder.createdAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-info\",\n                        size: \"sm\",\n                        onClick: () => openViewModal(holder),\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-warning\",\n                        size: \"sm\",\n                        onClick: () => openEditModal(holder),\n                        title: \"Edit Policy Holder\",\n                        children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 372,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleDelete(holder._id),\n                        title: \"Delete Policy Holder\",\n                        children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 386,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this)]\n                }, holder._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showModal,\n      onHide: () => setShowModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Add New Policy Holder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"danger\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"First Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"firstName\",\n                  value: form.firstName,\n                  onChange: handleChange,\n                  placeholder: \"Enter first name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Last Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"lastName\",\n                  value: form.lastName,\n                  onChange: handleChange,\n                  placeholder: \"Enter last name\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Email *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  name: \"email\",\n                  value: form.email,\n                  onChange: handleChange,\n                  placeholder: \"Enter email address\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Phone *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"tel\",\n                  name: \"phone\",\n                  value: form.phone,\n                  onChange: handleChange,\n                  placeholder: \"Enter phone number\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Date of Birth\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"date\",\n                  name: \"dateOfBirth\",\n                  value: form.dateOfBirth,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Gender\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"gender\",\n                  value: form.gender,\n                  onChange: handleChange,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select gender...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"male\",\n                    children: \"Male\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"female\",\n                    children: \"Female\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"other\",\n                    children: \"Other\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Occupation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"occupation\",\n                  value: form.occupation,\n                  onChange: handleChange,\n                  placeholder: \"Enter occupation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Annual Income\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"annualIncome\",\n                  value: form.annualIncome,\n                  onChange: handleChange,\n                  placeholder: \"Enter annual income\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleSave,\n          disabled: submitting,\n          children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              animation: \"border\",\n              size: \"sm\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), \"Creating...\"]\n          }, void 0, true) : 'Create Policy Holder'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(PolicyHolder, \"jySNE3FZ0lBwq294NSYCFHi4cCY=\");\n_c = PolicyHolder;\nexport default PolicyHolder;\nvar _c;\n$RefreshReg$(_c, \"PolicyHolder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Form", "Modal", "Row", "Col", "Container", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaSearch", "FaFileImport", "FaUser", "FaPhone", "FaEnvelope", "policyHoldersAPI", "policiesAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PolicyHolder", "_s", "policyHolders", "setPolicyHolders", "policies", "setPolicies", "search", "setSearch", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showModal", "setShowModal", "showEditModal", "setShowEditModal", "showViewModal", "setShowViewModal", "selectedHolder", "setSelectedHolder", "submitting", "setSubmitting", "form", "setForm", "firstName", "lastName", "email", "phone", "dateOfBirth", "gender", "address", "street", "city", "state", "zipCode", "country", "emergencyContact", "name", "relationship", "occupation", "annualIncome", "status", "fetchPolicyHolders", "fetchPolicies", "response", "getPolicyHolders", "data", "console", "getPolicies", "handleChange", "e", "value", "target", "includes", "parent", "child", "split", "prev", "handleSave", "createPolicyHolder", "resetForm", "message", "handleEdit", "updatePolicyHolder", "_id", "handleDelete", "holderId", "window", "confirm", "deletePolicyHolder", "openCreateModal", "openEditModal", "holder", "_holder$address", "_holder$address2", "_holder$address3", "_holder$address4", "_holder$address5", "_holder$emergencyCont", "_holder$emergencyCont2", "_holder$emergencyCont3", "Date", "toISOString", "openViewModal", "filteredHolders", "filter", "_holder$email", "_holder$phone", "toLowerCase", "getStatusBadge", "statusConfig", "variant", "text", "config", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "toLocaleDateString", "fluid", "className", "onClick", "dismissible", "onClose", "md", "Control", "type", "placeholder", "onChange", "Body", "animation", "length", "size", "responsive", "hover", "map", "createdAt", "title", "show", "onHide", "Header", "closeButton", "Title", "Group", "Label", "required", "Select", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/Pages/PolicyHolder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>, Form, Modal, <PERSON>, Col, Con<PERSON>er, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from 'react-bootstrap';\r\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaSearch, FaFileImport, FaUser, FaPhone, FaEnvelope } from 'react-icons/fa';\r\nimport { policyHoldersAPI, policiesAPI } from '../services/api';\r\n\r\nconst PolicyHolder = () => {\r\n  const [policyHolders, setPolicyHolders] = useState([]);\r\n  const [policies, setPolicies] = useState([]);\r\n  const [search, setSearch] = useState('');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedHolder, setSelectedHolder] = useState(null);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  // Form state\r\n  const [form, setForm] = useState({\r\n    firstName: '',\r\n    lastName: '',\r\n    email: '',\r\n    phone: '',\r\n    dateOfBirth: '',\r\n    gender: '',\r\n    address: {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: 'India'\r\n    },\r\n    emergencyContact: {\r\n      name: '',\r\n      relationship: '',\r\n      phone: ''\r\n    },\r\n    occupation: '',\r\n    annualIncome: '',\r\n    status: 'active'\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchPolicyHolders();\r\n    fetchPolicies();\r\n  }, []);\r\n\r\n  const fetchPolicyHolders = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await policyHoldersAPI.getPolicyHolders();\r\n      if (response.success) {\r\n        setPolicyHolders(response.data.policyHolders || []);\r\n      } else {\r\n        setError('Failed to fetch policy holders');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policy holders:', error);\r\n      setError('Failed to fetch policy holders');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchPolicies = async () => {\r\n    try {\r\n      const response = await policiesAPI.getPolicies();\r\n      if (response.success) {\r\n        setPolicies(response.data.policies || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching policies:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      setForm(prev => ({\r\n        ...prev,\r\n        [parent]: {\r\n          ...prev[parent],\r\n          [child]: value\r\n        }\r\n      }));\r\n    } else {\r\n      setForm(prev => ({\r\n        ...prev,\r\n        [name]: value\r\n      }));\r\n    }\r\n    setError('');\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policyHoldersAPI.createPolicyHolder(form);\r\n      if (response.success) {\r\n        setSuccess('Policy holder created successfully!');\r\n        resetForm();\r\n        setShowModal(false);\r\n        fetchPolicyHolders();\r\n      } else {\r\n        setError(response.message || 'Failed to create policy holder');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating policy holder:', error);\r\n      setError('Failed to create policy holder');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleEdit = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      setError('');\r\n\r\n      const response = await policyHoldersAPI.updatePolicyHolder(selectedHolder._id, form);\r\n      if (response.success) {\r\n        setSuccess('Policy holder updated successfully!');\r\n        setShowEditModal(false);\r\n        setSelectedHolder(null);\r\n        fetchPolicyHolders();\r\n      } else {\r\n        setError(response.message || 'Failed to update policy holder');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating policy holder:', error);\r\n      setError('Failed to update policy holder');\r\n    } finally {\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (holderId) => {\r\n    if (window.confirm('Are you sure you want to delete this policy holder?')) {\r\n      try {\r\n        const response = await policyHoldersAPI.deletePolicyHolder(holderId);\r\n        if (response.success) {\r\n          setSuccess('Policy holder deleted successfully!');\r\n          fetchPolicyHolders();\r\n        } else {\r\n          setError(response.message || 'Failed to delete policy holder');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error deleting policy holder:', error);\r\n        setError('Failed to delete policy holder');\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetForm = () => {\r\n    setForm({\r\n      firstName: '',\r\n      lastName: '',\r\n      email: '',\r\n      phone: '',\r\n      dateOfBirth: '',\r\n      gender: '',\r\n      address: {\r\n        street: '',\r\n        city: '',\r\n        state: '',\r\n        zipCode: '',\r\n        country: 'India'\r\n      },\r\n      emergencyContact: {\r\n        name: '',\r\n        relationship: '',\r\n        phone: ''\r\n      },\r\n      occupation: '',\r\n      annualIncome: '',\r\n      status: 'active'\r\n    });\r\n  };\r\n\r\n  const openCreateModal = () => {\r\n    resetForm();\r\n    setShowModal(true);\r\n  };\r\n\r\n  const openEditModal = (holder) => {\r\n    setSelectedHolder(holder);\r\n    setForm({\r\n      firstName: holder.firstName,\r\n      lastName: holder.lastName,\r\n      email: holder.email,\r\n      phone: holder.phone,\r\n      dateOfBirth: holder.dateOfBirth ? new Date(holder.dateOfBirth).toISOString().split('T')[0] : '',\r\n      gender: holder.gender || '',\r\n      address: {\r\n        street: holder.address?.street || '',\r\n        city: holder.address?.city || '',\r\n        state: holder.address?.state || '',\r\n        zipCode: holder.address?.zipCode || '',\r\n        country: holder.address?.country || 'India'\r\n      },\r\n      emergencyContact: {\r\n        name: holder.emergencyContact?.name || '',\r\n        relationship: holder.emergencyContact?.relationship || '',\r\n        phone: holder.emergencyContact?.phone || ''\r\n      },\r\n      occupation: holder.occupation || '',\r\n      annualIncome: holder.annualIncome || '',\r\n      status: holder.status || 'active'\r\n    });\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const openViewModal = (holder) => {\r\n    setSelectedHolder(holder);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const filteredHolders = policyHolders.filter((holder) =>\r\n    `${holder.firstName} ${holder.lastName}`.toLowerCase().includes(search.toLowerCase()) ||\r\n    holder.email?.toLowerCase().includes(search.toLowerCase()) ||\r\n    holder.phone?.includes(search)\r\n  );\r\n\r\n  const getStatusBadge = (status) => {\r\n    const statusConfig = {\r\n      'active': { variant: 'success', text: 'Active' },\r\n      'inactive': { variant: 'secondary', text: 'Inactive' },\r\n      'suspended': { variant: 'warning', text: 'Suspended' },\r\n      'blocked': { variant: 'danger', text: 'Blocked' }\r\n    };\r\n\r\n    const config = statusConfig[status] || { variant: 'secondary', text: status };\r\n    return <Badge bg={config.variant}>{config.text}</Badge>;\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    return dateString ? new Date(dateString).toLocaleDateString('en-IN') : 'N/A';\r\n  };\r\n\r\n  return (\r\n    <Container fluid className=\"py-4\">\r\n      <Row className=\"mb-4\">\r\n        <Col>\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <div>\r\n              <h2 className=\"mb-1\">Policy Holders</h2>\r\n              <p className=\"text-muted\">Manage insurance policy holders</p>\r\n            </div>\r\n            <div className=\"d-flex gap-2\">\r\n              <Button variant=\"primary\" onClick={openCreateModal}>\r\n                <FaPlus className=\"me-2\" />\r\n                New Policy Holder\r\n              </Button>\r\n              <Button variant=\"secondary\">\r\n                <FaFileImport className=\"me-2\" />\r\n                Import\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n\r\n      {success && (\r\n        <Alert variant=\"success\" dismissible onClose={() => setSuccess('')}>\r\n          {success}\r\n        </Alert>\r\n      )}\r\n\r\n      {error && (\r\n        <Alert variant=\"danger\" dismissible onClose={() => setError('')}>\r\n          {error}\r\n        </Alert>\r\n      )}\r\n\r\n      <Row className=\"mb-3\">\r\n        <Col md={6}>\r\n          <div className=\"position-relative\">\r\n            <FaSearch className=\"position-absolute top-50 start-0 translate-middle-y ms-3 text-muted\" />\r\n            <Form.Control\r\n              type=\"text\"\r\n              placeholder=\"Search policy holders...\"\r\n              value={search}\r\n              onChange={(e) => setSearch(e.target.value)}\r\n              className=\"ps-5\"\r\n            />\r\n          </div>\r\n        </Col>\r\n      </Row>\r\n\r\n      <Row>\r\n        <Col>\r\n          <Card>\r\n            <Card.Body>\r\n              {loading ? (\r\n                <div className=\"text-center py-4\">\r\n                  <Spinner animation=\"border\" />\r\n                  <p className=\"mt-2\">Loading policy holders...</p>\r\n                </div>\r\n              ) : filteredHolders.length === 0 ? (\r\n                <div className=\"text-center py-4\">\r\n                  <FaUser size={48} className=\"text-muted mb-3\" />\r\n                  <h5>No Policy Holders Found</h5>\r\n                  <p className=\"text-muted\">\r\n                    {search ? 'No policy holders match your search.' : 'Start by adding your first policy holder.'}\r\n                  </p>\r\n                  {!search && (\r\n                    <Button variant=\"primary\" onClick={openCreateModal}>\r\n                      <FaPlus className=\"me-2\" />\r\n                      Add First Policy Holder\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <Table responsive hover>\r\n                  <thead>\r\n                    <tr>\r\n                      <th>Name</th>\r\n                      <th>Contact</th>\r\n                      <th>Email</th>\r\n                      <th>Date of Birth</th>\r\n                      <th>Occupation</th>\r\n                      <th>Status</th>\r\n                      <th>Created</th>\r\n                      <th>Actions</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {filteredHolders.map((holder) => (\r\n                      <tr key={holder._id}>\r\n                        <td>\r\n                          <div>\r\n                            <strong>{holder.firstName} {holder.lastName}</strong>\r\n                            <br />\r\n                            <small className=\"text-muted\">\r\n                              <FaUser className=\"me-1\" />\r\n                              {holder.gender || 'N/A'}\r\n                            </small>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div>\r\n                            <FaPhone className=\"me-1 text-muted\" />\r\n                            {holder.phone}\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <div>\r\n                            <FaEnvelope className=\"me-1 text-muted\" />\r\n                            {holder.email}\r\n                          </div>\r\n                        </td>\r\n                        <td>{formatDate(holder.dateOfBirth)}</td>\r\n                        <td>{holder.occupation || 'N/A'}</td>\r\n                        <td>{getStatusBadge(holder.status)}</td>\r\n                        <td>{formatDate(holder.createdAt)}</td>\r\n                        <td>\r\n                          <div className=\"d-flex gap-1\">\r\n                            <Button\r\n                              variant=\"outline-info\"\r\n                              size=\"sm\"\r\n                              onClick={() => openViewModal(holder)}\r\n                              title=\"View Details\"\r\n                            >\r\n                              <FaEye />\r\n                            </Button>\r\n                            <Button\r\n                              variant=\"outline-warning\"\r\n                              size=\"sm\"\r\n                              onClick={() => openEditModal(holder)}\r\n                              title=\"Edit Policy Holder\"\r\n                            >\r\n                              <FaEdit />\r\n                            </Button>\r\n                            <Button\r\n                              variant=\"outline-danger\"\r\n                              size=\"sm\"\r\n                              onClick={() => handleDelete(holder._id)}\r\n                              title=\"Delete Policy Holder\"\r\n                            >\r\n                              <FaTrash />\r\n                            </Button>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                </Table>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* Create Policy Holder Modal */}\r\n      <Modal show={showModal} onHide={() => setShowModal(false)} size=\"lg\">\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Add New Policy Holder</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          {error && <Alert variant=\"danger\">{error}</Alert>}\r\n          <Form>\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>First Name *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"firstName\"\r\n                    value={form.firstName}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter first name\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Last Name *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"lastName\"\r\n                    value={form.lastName}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter last name\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Email *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={form.email}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter email address\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Phone *</Form.Label>\r\n                  <Form.Control\r\n                    type=\"tel\"\r\n                    name=\"phone\"\r\n                    value={form.phone}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter phone number\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Date of Birth</Form.Label>\r\n                  <Form.Control\r\n                    type=\"date\"\r\n                    name=\"dateOfBirth\"\r\n                    value={form.dateOfBirth}\r\n                    onChange={handleChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Gender</Form.Label>\r\n                  <Form.Select\r\n                    name=\"gender\"\r\n                    value={form.gender}\r\n                    onChange={handleChange}\r\n                  >\r\n                    <option value=\"\">Select gender...</option>\r\n                    <option value=\"male\">Male</option>\r\n                    <option value=\"female\">Female</option>\r\n                    <option value=\"other\">Other</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Occupation</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"occupation\"\r\n                    value={form.occupation}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter occupation\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Annual Income</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"annualIncome\"\r\n                    value={form.annualIncome}\r\n                    onChange={handleChange}\r\n                    placeholder=\"Enter annual income\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n          </Form>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={() => setShowModal(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={handleSave}\r\n            disabled={submitting}\r\n          >\r\n            {submitting ? (\r\n              <>\r\n                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                Creating...\r\n              </>\r\n            ) : (\r\n              'Create Policy Holder'\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PolicyHolder;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC9G,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AACpH,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACoD,IAAI,EAAEC,OAAO,CAAC,GAAGrD,QAAQ,CAAC;IAC/BsD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,EAAE;MAChBX,KAAK,EAAE;IACT,CAAC;IACDY,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEFtE,SAAS,CAAC,MAAM;IACduE,kBAAkB,CAAC,CAAC;IACpBC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,QAAQ,GAAG,MAAMpD,gBAAgB,CAACqD,gBAAgB,CAAC,CAAC;MAC1D,IAAID,QAAQ,CAAClC,OAAO,EAAE;QACpBT,gBAAgB,CAAC2C,QAAQ,CAACE,IAAI,CAAC9C,aAAa,IAAI,EAAE,CAAC;MACrD,CAAC,MAAM;QACLS,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnD,WAAW,CAACuD,WAAW,CAAC,CAAC;MAChD,IAAIJ,QAAQ,CAAClC,OAAO,EAAE;QACpBP,WAAW,CAACyC,QAAQ,CAACE,IAAI,CAAC5C,QAAQ,IAAI,EAAE,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEb,IAAI;MAAEc;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIf,IAAI,CAACgB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGlB,IAAI,CAACmB,KAAK,CAAC,GAAG,CAAC;MACvCjC,OAAO,CAACkC,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACH,MAAM,GAAG;UACR,GAAGG,IAAI,CAACH,MAAM,CAAC;UACf,CAACC,KAAK,GAAGJ;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL5B,OAAO,CAACkC,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACpB,IAAI,GAAGc;MACV,CAAC,CAAC,CAAC;IACL;IACA1C,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMiD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMmC,QAAQ,GAAG,MAAMpD,gBAAgB,CAACmE,kBAAkB,CAACrC,IAAI,CAAC;MAChE,IAAIsB,QAAQ,CAAClC,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjDiD,SAAS,CAAC,CAAC;QACX/C,YAAY,CAAC,KAAK,CAAC;QACnB6B,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzC,aAAa,CAAC,IAAI,CAAC;MACnBZ,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMmC,QAAQ,GAAG,MAAMpD,gBAAgB,CAACuE,kBAAkB,CAAC7C,cAAc,CAAC8C,GAAG,EAAE1C,IAAI,CAAC;MACpF,IAAIsB,QAAQ,CAAClC,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjDI,gBAAgB,CAAC,KAAK,CAAC;QACvBI,iBAAiB,CAAC,IAAI,CAAC;QACvBuB,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDC,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAIC,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MACzE,IAAI;QACF,MAAMxB,QAAQ,GAAG,MAAMpD,gBAAgB,CAAC6E,kBAAkB,CAACH,QAAQ,CAAC;QACpE,IAAItB,QAAQ,CAAClC,OAAO,EAAE;UACpBC,UAAU,CAAC,qCAAqC,CAAC;UACjD+B,kBAAkB,CAAC,CAAC;QACtB,CAAC,MAAM;UACLjC,QAAQ,CAACmC,QAAQ,CAACiB,OAAO,IAAI,gCAAgC,CAAC;QAChE;MACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;QACduC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDC,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMmD,SAAS,GAAGA,CAAA,KAAM;IACtBrC,OAAO,CAAC;MACNC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;MACX,CAAC;MACDC,gBAAgB,EAAE;QAChBC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChBX,KAAK,EAAE;MACT,CAAC;MACDY,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5BV,SAAS,CAAC,CAAC;IACX/C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0D,aAAa,GAAIC,MAAM,IAAK;IAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChC7D,iBAAiB,CAACqD,MAAM,CAAC;IACzBjD,OAAO,CAAC;MACNC,SAAS,EAAEgD,MAAM,CAAChD,SAAS;MAC3BC,QAAQ,EAAE+C,MAAM,CAAC/C,QAAQ;MACzBC,KAAK,EAAE8C,MAAM,CAAC9C,KAAK;MACnBC,KAAK,EAAE6C,MAAM,CAAC7C,KAAK;MACnBC,WAAW,EAAE4C,MAAM,CAAC5C,WAAW,GAAG,IAAIqD,IAAI,CAACT,MAAM,CAAC5C,WAAW,CAAC,CAACsD,WAAW,CAAC,CAAC,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MAC/F3B,MAAM,EAAE2C,MAAM,CAAC3C,MAAM,IAAI,EAAE;MAC3BC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAA0C,eAAA,GAAAD,MAAM,CAAC1C,OAAO,cAAA2C,eAAA,uBAAdA,eAAA,CAAgB1C,MAAM,KAAI,EAAE;QACpCC,IAAI,EAAE,EAAA0C,gBAAA,GAAAF,MAAM,CAAC1C,OAAO,cAAA4C,gBAAA,uBAAdA,gBAAA,CAAgB1C,IAAI,KAAI,EAAE;QAChCC,KAAK,EAAE,EAAA0C,gBAAA,GAAAH,MAAM,CAAC1C,OAAO,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgB1C,KAAK,KAAI,EAAE;QAClCC,OAAO,EAAE,EAAA0C,gBAAA,GAAAJ,MAAM,CAAC1C,OAAO,cAAA8C,gBAAA,uBAAdA,gBAAA,CAAgB1C,OAAO,KAAI,EAAE;QACtCC,OAAO,EAAE,EAAA0C,gBAAA,GAAAL,MAAM,CAAC1C,OAAO,cAAA+C,gBAAA,uBAAdA,gBAAA,CAAgB1C,OAAO,KAAI;MACtC,CAAC;MACDC,gBAAgB,EAAE;QAChBC,IAAI,EAAE,EAAAyC,qBAAA,GAAAN,MAAM,CAACpC,gBAAgB,cAAA0C,qBAAA,uBAAvBA,qBAAA,CAAyBzC,IAAI,KAAI,EAAE;QACzCC,YAAY,EAAE,EAAAyC,sBAAA,GAAAP,MAAM,CAACpC,gBAAgB,cAAA2C,sBAAA,uBAAvBA,sBAAA,CAAyBzC,YAAY,KAAI,EAAE;QACzDX,KAAK,EAAE,EAAAqD,sBAAA,GAAAR,MAAM,CAACpC,gBAAgB,cAAA4C,sBAAA,uBAAvBA,sBAAA,CAAyBrD,KAAK,KAAI;MAC3C,CAAC;MACDY,UAAU,EAAEiC,MAAM,CAACjC,UAAU,IAAI,EAAE;MACnCC,YAAY,EAAEgC,MAAM,CAAChC,YAAY,IAAI,EAAE;MACvCC,MAAM,EAAE+B,MAAM,CAAC/B,MAAM,IAAI;IAC3B,CAAC,CAAC;IACF1B,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMoE,aAAa,GAAIX,MAAM,IAAK;IAChCrD,iBAAiB,CAACqD,MAAM,CAAC;IACzBvD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmE,eAAe,GAAGpF,aAAa,CAACqF,MAAM,CAAEb,MAAM;IAAA,IAAAc,aAAA,EAAAC,aAAA;IAAA,OAClD,GAAGf,MAAM,CAAChD,SAAS,IAAIgD,MAAM,CAAC/C,QAAQ,EAAE,CAAC+D,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACjD,MAAM,CAACoF,WAAW,CAAC,CAAC,CAAC,MAAAF,aAAA,GACrFd,MAAM,CAAC9C,KAAK,cAAA4D,aAAA,uBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACnC,QAAQ,CAACjD,MAAM,CAACoF,WAAW,CAAC,CAAC,CAAC,OAAAD,aAAA,GAC1Df,MAAM,CAAC7C,KAAK,cAAA4D,aAAA,uBAAZA,aAAA,CAAclC,QAAQ,CAACjD,MAAM,CAAC;EAAA,CAChC,CAAC;EAED,MAAMqF,cAAc,GAAIhD,MAAM,IAAK;IACjC,MAAMiD,YAAY,GAAG;MACnB,QAAQ,EAAE;QAAEC,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAS,CAAC;MAChD,UAAU,EAAE;QAAED,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAW,CAAC;MACtD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAY,CAAC;MACtD,SAAS,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAU;IAClD,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACjD,MAAM,CAAC,IAAI;MAAEkD,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAEnD;IAAO,CAAC;IAC7E,oBAAO9C,OAAA,CAACb,KAAK;MAACgH,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,EAAEF,MAAM,CAACD;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACzD,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAOA,UAAU,GAAG,IAAIpB,IAAI,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,GAAG,KAAK;EAC9E,CAAC;EAED,oBACE3G,OAAA,CAACjB,SAAS;IAAC6H,KAAK;IAACC,SAAS,EAAC,MAAM;IAAAT,QAAA,gBAC/BpG,OAAA,CAACnB,GAAG;MAACgI,SAAS,EAAC,MAAM;MAAAT,QAAA,eACnBpG,OAAA,CAAClB,GAAG;QAAAsH,QAAA,eACFpG,OAAA;UAAK6G,SAAS,EAAC,mDAAmD;UAAAT,QAAA,gBAChEpG,OAAA;YAAAoG,QAAA,gBACEpG,OAAA;cAAI6G,SAAS,EAAC,MAAM;cAAAT,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxCxG,OAAA;cAAG6G,SAAS,EAAC,YAAY;cAAAT,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNxG,OAAA;YAAK6G,SAAS,EAAC,cAAc;YAAAT,QAAA,gBAC3BpG,OAAA,CAACtB,MAAM;cAACsH,OAAO,EAAC,SAAS;cAACc,OAAO,EAAEnC,eAAgB;cAAAyB,QAAA,gBACjDpG,OAAA,CAACZ,MAAM;gBAACyH,SAAS,EAAC;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qBAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxG,OAAA,CAACtB,MAAM;cAACsH,OAAO,EAAC,WAAW;cAAAI,QAAA,gBACzBpG,OAAA,CAACP,YAAY;gBAACoH,SAAS,EAAC;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELzF,OAAO,iBACNf,OAAA,CAACf,KAAK;MAAC+G,OAAO,EAAC,SAAS;MAACe,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMhG,UAAU,CAAC,EAAE,CAAE;MAAAoF,QAAA,EAChErF;IAAO;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,EAEA3F,KAAK,iBACJb,OAAA,CAACf,KAAK;MAAC+G,OAAO,EAAC,QAAQ;MAACe,WAAW;MAACC,OAAO,EAAEA,CAAA,KAAMlG,QAAQ,CAAC,EAAE,CAAE;MAAAsF,QAAA,EAC7DvF;IAAK;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDxG,OAAA,CAACnB,GAAG;MAACgI,SAAS,EAAC,MAAM;MAAAT,QAAA,eACnBpG,OAAA,CAAClB,GAAG;QAACmI,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTpG,OAAA;UAAK6G,SAAS,EAAC,mBAAmB;UAAAT,QAAA,gBAChCpG,OAAA,CAACR,QAAQ;YAACqH,SAAS,EAAC;UAAqE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5FxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;YACXC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,0BAA0B;YACtC5D,KAAK,EAAE/C,MAAO;YACd4G,QAAQ,EAAG9D,CAAC,IAAK7C,SAAS,CAAC6C,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;YAC3CqD,SAAS,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxG,OAAA,CAACnB,GAAG;MAAAuH,QAAA,eACFpG,OAAA,CAAClB,GAAG;QAAAsH,QAAA,eACFpG,OAAA,CAAChB,IAAI;UAAAoH,QAAA,eACHpG,OAAA,CAAChB,IAAI,CAACsI,IAAI;YAAAlB,QAAA,EACPzF,OAAO,gBACNX,OAAA;cAAK6G,SAAS,EAAC,kBAAkB;cAAAT,QAAA,gBAC/BpG,OAAA,CAACd,OAAO;gBAACqI,SAAS,EAAC;cAAQ;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BxG,OAAA;gBAAG6G,SAAS,EAAC,MAAM;gBAAAT,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,GACJf,eAAe,CAAC+B,MAAM,KAAK,CAAC,gBAC9BxH,OAAA;cAAK6G,SAAS,EAAC,kBAAkB;cAAAT,QAAA,gBAC/BpG,OAAA,CAACN,MAAM;gBAAC+H,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAAiB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDxG,OAAA;gBAAAoG,QAAA,EAAI;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCxG,OAAA;gBAAG6G,SAAS,EAAC,YAAY;gBAAAT,QAAA,EACtB3F,MAAM,GAAG,sCAAsC,GAAG;cAA2C;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC,EACH,CAAC/F,MAAM,iBACNT,OAAA,CAACtB,MAAM;gBAACsH,OAAO,EAAC,SAAS;gBAACc,OAAO,EAAEnC,eAAgB;gBAAAyB,QAAA,gBACjDpG,OAAA,CAACZ,MAAM;kBAACyH,SAAS,EAAC;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAE7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENxG,OAAA,CAACvB,KAAK;cAACiJ,UAAU;cAACC,KAAK;cAAAvB,QAAA,gBACrBpG,OAAA;gBAAAoG,QAAA,eACEpG,OAAA;kBAAAoG,QAAA,gBACEpG,OAAA;oBAAAoG,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChBxG,OAAA;oBAAAoG,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRxG,OAAA;gBAAAoG,QAAA,EACGX,eAAe,CAACmC,GAAG,CAAE/C,MAAM,iBAC1B7E,OAAA;kBAAAoG,QAAA,gBACEpG,OAAA;oBAAAoG,QAAA,eACEpG,OAAA;sBAAAoG,QAAA,gBACEpG,OAAA;wBAAAoG,QAAA,GAASvB,MAAM,CAAChD,SAAS,EAAC,GAAC,EAACgD,MAAM,CAAC/C,QAAQ;sBAAA;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eACrDxG,OAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNxG,OAAA;wBAAO6G,SAAS,EAAC,YAAY;wBAAAT,QAAA,gBAC3BpG,OAAA,CAACN,MAAM;0BAACmH,SAAS,EAAC;wBAAM;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EAC1B3B,MAAM,CAAC3C,MAAM,IAAI,KAAK;sBAAA;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLxG,OAAA;oBAAAoG,QAAA,eACEpG,OAAA;sBAAAoG,QAAA,gBACEpG,OAAA,CAACL,OAAO;wBAACkH,SAAS,EAAC;sBAAiB;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACtC3B,MAAM,CAAC7C,KAAK;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLxG,OAAA;oBAAAoG,QAAA,eACEpG,OAAA;sBAAAoG,QAAA,gBACEpG,OAAA,CAACJ,UAAU;wBAACiH,SAAS,EAAC;sBAAiB;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACzC3B,MAAM,CAAC9C,KAAK;oBAAA;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLxG,OAAA;oBAAAoG,QAAA,EAAKK,UAAU,CAAC5B,MAAM,CAAC5C,WAAW;kBAAC;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzCxG,OAAA;oBAAAoG,QAAA,EAAKvB,MAAM,CAACjC,UAAU,IAAI;kBAAK;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrCxG,OAAA;oBAAAoG,QAAA,EAAKN,cAAc,CAACjB,MAAM,CAAC/B,MAAM;kBAAC;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxCxG,OAAA;oBAAAoG,QAAA,EAAKK,UAAU,CAAC5B,MAAM,CAACgD,SAAS;kBAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCxG,OAAA;oBAAAoG,QAAA,eACEpG,OAAA;sBAAK6G,SAAS,EAAC,cAAc;sBAAAT,QAAA,gBAC3BpG,OAAA,CAACtB,MAAM;wBACLsH,OAAO,EAAC,cAAc;wBACtByB,IAAI,EAAC,IAAI;wBACTX,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACX,MAAM,CAAE;wBACrCiD,KAAK,EAAC,cAAc;wBAAA1B,QAAA,eAEpBpG,OAAA,CAACT,KAAK;0BAAA8G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACTxG,OAAA,CAACtB,MAAM;wBACLsH,OAAO,EAAC,iBAAiB;wBACzByB,IAAI,EAAC,IAAI;wBACTX,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAACC,MAAM,CAAE;wBACrCiD,KAAK,EAAC,oBAAoB;wBAAA1B,QAAA,eAE1BpG,OAAA,CAACX,MAAM;0BAAAgH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACTxG,OAAA,CAACtB,MAAM;wBACLsH,OAAO,EAAC,gBAAgB;wBACxByB,IAAI,EAAC,IAAI;wBACTX,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACO,MAAM,CAACR,GAAG,CAAE;wBACxCyD,KAAK,EAAC,sBAAsB;wBAAA1B,QAAA,eAE5BpG,OAAA,CAACV,OAAO;0BAAA+G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAtDE3B,MAAM,CAACR,GAAG;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuDf,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxG,OAAA,CAACpB,KAAK;MAACmJ,IAAI,EAAE9G,SAAU;MAAC+G,MAAM,EAAEA,CAAA,KAAM9G,YAAY,CAAC,KAAK,CAAE;MAACuG,IAAI,EAAC,IAAI;MAAArB,QAAA,gBAClEpG,OAAA,CAACpB,KAAK,CAACqJ,MAAM;QAACC,WAAW;QAAA9B,QAAA,eACvBpG,OAAA,CAACpB,KAAK,CAACuJ,KAAK;UAAA/B,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACfxG,OAAA,CAACpB,KAAK,CAAC0I,IAAI;QAAAlB,QAAA,GACRvF,KAAK,iBAAIb,OAAA,CAACf,KAAK;UAAC+G,OAAO,EAAC,QAAQ;UAAAI,QAAA,EAAEvF;QAAK;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjDxG,OAAA,CAACrB,IAAI;UAAAyH,QAAA,gBACHpG,OAAA,CAACnB,GAAG;YAAAuH,QAAA,gBACFpG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzE,IAAI,EAAC,WAAW;kBAChBc,KAAK,EAAE7B,IAAI,CAACE,SAAU;kBACtBwF,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC,kBAAkB;kBAC9BkB,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzE,IAAI,EAAC,UAAU;kBACfc,KAAK,EAAE7B,IAAI,CAACG,QAAS;kBACrBuF,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC,iBAAiB;kBAC7BkB,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA,CAACnB,GAAG;YAAAuH,QAAA,gBACFpG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZzE,IAAI,EAAC,OAAO;kBACZc,KAAK,EAAE7B,IAAI,CAACI,KAAM;kBAClBsF,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC,qBAAqB;kBACjCkB,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,KAAK;kBACVzE,IAAI,EAAC,OAAO;kBACZc,KAAK,EAAE7B,IAAI,CAACK,KAAM;kBAClBqF,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC,oBAAoB;kBAChCkB,QAAQ;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA,CAACnB,GAAG;YAAAuH,QAAA,gBACFpG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzE,IAAI,EAAC,aAAa;kBAClBc,KAAK,EAAE7B,IAAI,CAACM,WAAY;kBACxBoF,QAAQ,EAAE/D;gBAAa;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BxG,OAAA,CAACrB,IAAI,CAAC4J,MAAM;kBACV7F,IAAI,EAAC,QAAQ;kBACbc,KAAK,EAAE7B,IAAI,CAACO,MAAO;kBACnBmF,QAAQ,EAAE/D,YAAa;kBAAA8C,QAAA,gBAEvBpG,OAAA;oBAAQwD,KAAK,EAAC,EAAE;oBAAA4C,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CxG,OAAA;oBAAQwD,KAAK,EAAC,MAAM;oBAAA4C,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCxG,OAAA;oBAAQwD,KAAK,EAAC,QAAQ;oBAAA4C,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxG,OAAA;oBAAQwD,KAAK,EAAC,OAAO;oBAAA4C,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA,CAACnB,GAAG;YAAAuH,QAAA,gBACFpG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXzE,IAAI,EAAC,YAAY;kBACjBc,KAAK,EAAE7B,IAAI,CAACiB,UAAW;kBACvByE,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC;gBAAkB;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxG,OAAA,CAAClB,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTpG,OAAA,CAACrB,IAAI,CAACyJ,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAT,QAAA,gBAC1BpG,OAAA,CAACrB,IAAI,CAAC0J,KAAK;kBAAAjC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxG,OAAA,CAACrB,IAAI,CAACuI,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbzE,IAAI,EAAC,cAAc;kBACnBc,KAAK,EAAE7B,IAAI,CAACkB,YAAa;kBACzBwE,QAAQ,EAAE/D,YAAa;kBACvB8D,WAAW,EAAC;gBAAqB;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbxG,OAAA,CAACpB,KAAK,CAAC4J,MAAM;QAAApC,QAAA,gBACXpG,OAAA,CAACtB,MAAM;UAACsH,OAAO,EAAC,WAAW;UAACc,OAAO,EAAEA,CAAA,KAAM5F,YAAY,CAAC,KAAK,CAAE;UAAAkF,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxG,OAAA,CAACtB,MAAM;UACLsH,OAAO,EAAC,SAAS;UACjBc,OAAO,EAAE/C,UAAW;UACpB0E,QAAQ,EAAEhH,UAAW;UAAA2E,QAAA,EAEpB3E,UAAU,gBACTzB,OAAA,CAAAE,SAAA;YAAAkG,QAAA,gBACEpG,OAAA,CAACd,OAAO;cAACqI,SAAS,EAAC,QAAQ;cAACE,IAAI,EAAC,IAAI;cAACZ,SAAS,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3D;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACpG,EAAA,CA3hBID,YAAY;AAAAuI,EAAA,GAAZvI,YAAY;AA6hBlB,eAAeA,YAAY;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}