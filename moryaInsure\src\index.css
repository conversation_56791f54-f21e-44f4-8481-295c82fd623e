/* Custom styles for Morya Insurance */

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Layout fixes */
.min-vh-100 {
  min-height: 100vh !important;
}

/* Sidebar styles */
.sidebar {
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  z-index: 1000;
}

.sidebar-link {
  border-radius: 5px;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.sidebar-link:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(5px);
}

.active-link {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-left: 4px solid #fff;
}

/* Navbar styles */
.navbar {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Main content area */
.main-content {
  min-height: calc(100vh - 120px); /* Adjust based on navbar and footer height */
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

/* Content area adjustments for sidebar */
.content-with-sidebar {
  margin-left: 260px;
}

.content-with-sidebar.collapsed {
  margin-left: 70px;
}

/* Card hover effects */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

/* Button styles */
.btn {
  transition: all 0.3s ease;
}

/* Footer styles */
footer {
  margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed !important;
    z-index: 1050;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0 !important;
  }
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Loading animation */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form styles */
.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert styles */
.alert {
  border: none;
  border-radius: 8px;
}

/* Dashboard card styles */
.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 15px;
  color: white;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}