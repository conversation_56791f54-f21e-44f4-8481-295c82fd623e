{"ast": null, "code": "var _jsxFileName = \"D:\\\\Wesync Softwares\\\\POC's\\\\Jun23\\\\moryaInsure\\\\src\\\\components\\\\dashboards\\\\CustomerDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Modal, Form, Table } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI, policiesAPI } from '../../services/api';\nimport { FaPlus, FaEye, FaFileAlt, FaCreditCard } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomerDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [customerData, setCustomerData] = useState({\n    activePolicies: 0,\n    pendingClaims: 0,\n    totalClaims: 0,\n    premiumDue: 0,\n    nextPaymentDate: '',\n    coverageAmount: 0,\n    loyaltyPoints: 0,\n    membershipLevel: 'Silver'\n  });\n  const [policies, setPolicies] = useState([]);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // Policy application state\n  const [showPolicyModal, setShowPolicyModal] = useState(false);\n  const [policyForm, setPolicyForm] = useState({\n    type: '',\n    coverageAmount: '',\n    description: ''\n  });\n\n  // Fetch real data from API\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      const response = await reportsAPI.getDashboardStats();\n      if (response.success && response.data.overview) {\n        const {\n          overview,\n          myData\n        } = response.data;\n        setCustomerData({\n          activePolicies: overview.activePolicies || 0,\n          pendingClaims: overview.pendingClaims || 0,\n          totalClaims: overview.totalClaims || 0,\n          premiumDue: overview.premiumDue || 0,\n          nextPaymentDate: overview.nextPaymentDate || '',\n          coverageAmount: overview.coverageAmount || 0,\n          loyaltyPoints: overview.loyaltyPoints || 0,\n          membershipLevel: overview.membershipLevel || 'Bronze'\n        });\n        if (myData) {\n          setPolicies(myData.policies || []);\n          setRecentActivity(myData.tickets || []);\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      // Fallback to existing dummy data\n      fetchDummyData();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchDummyData = () => {\n    const fetchedData = {\n      activePolicies: 0,\n      pendingClaims: 0,\n      totalClaims: 0,\n      premiumDue: 0,\n      nextPaymentDate: '',\n      coverageAmount: 0,\n      loyaltyPoints: 0,\n      membershipLevel: 'Gold'\n    };\n    const fetchedPolicies = [];\n    const fetchedActivity = [];\n    setCustomerData(fetchedData);\n    setPolicies(fetchedPolicies);\n    setRecentActivity(fetchedActivity);\n  };\n\n  // Handle policy application\n  const handlePolicyFormChange = e => {\n    setPolicyForm({\n      ...policyForm,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handlePolicySubmit = async e => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      const response = await policiesAPI.createPolicy(policyForm);\n      if (response.success) {\n        setShowPolicyModal(false);\n        setPolicyForm({\n          type: '',\n          coverageAmount: '',\n          description: ''\n        });\n        fetchDashboardData(); // Refresh data\n        alert('Policy application submitted successfully!');\n      }\n    } catch (error) {\n      console.error('Error submitting policy application:', error);\n      alert('Error submitting policy application. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const cardData = [{\n    label: 'Active Policies',\n    count: customerData.activePolicies,\n    icon: 'bi-shield-check',\n    color: 'primary'\n  }, {\n    label: 'Pending Claims',\n    count: customerData.pendingClaims,\n    icon: 'bi-clock-history',\n    color: 'warning'\n  }, {\n    label: 'Total Coverage',\n    count: `$${customerData.coverageAmount.toLocaleString()}`,\n    icon: 'bi-umbrella',\n    color: 'success'\n  }, {\n    label: 'Loyalty Points',\n    count: customerData.loyaltyPoints,\n    icon: 'bi-star-fill',\n    color: 'info'\n  }];\n  const quickActions = [{\n    title: 'Apply for Policy',\n    description: 'Apply for new insurance policy',\n    icon: 'bi-plus-circle',\n    action: 'apply'\n  }, {\n    title: 'Pay Premium',\n    description: 'Make premium payments',\n    icon: 'bi-credit-card',\n    action: 'payment'\n  }, {\n    title: 'File Claim',\n    description: 'Submit new insurance claim',\n    icon: 'bi-file-plus',\n    action: 'claim'\n  }, {\n    title: 'Contact Support',\n    description: 'Get help from our team',\n    icon: 'bi-headset',\n    action: 'support'\n  }];\n  const handleQuickAction = action => {\n    switch (action) {\n      case 'apply':\n        setShowPolicyModal(true);\n        break;\n      case 'payment':\n        // Navigate to payment page\n        window.location.href = '/payments';\n        break;\n      case 'claim':\n        // Navigate to claims page\n        window.location.href = '/claims';\n        break;\n      case 'support':\n        // Navigate to support page\n        window.location.href = '/tickets';\n        break;\n      default:\n        break;\n    }\n  };\n  const getStatusBadge = status => {\n    const variants = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'completed': 'success',\n      'pending': 'warning'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: variants[status],\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 12\n    }, this);\n  };\n  const getMembershipColor = level => {\n    const colors = {\n      'Bronze': 'warning',\n      'Silver': 'secondary',\n      'Gold': 'warning',\n      'Platinum': 'dark'\n    };\n    return colors[level] || 'primary';\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"fw-bold\",\n        children: \"Customer Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"lead\",\n        children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-primary fw-semibold\",\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 25\n        }, this), \"! Manage your insurance policies and claims.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: cardData.map((item, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: `shadow-sm border-0 bg-${item.color} text-white h-100`,\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"d-flex align-items-center justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"card-title mb-2\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"fw-bold\",\n                children: item.count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${item.icon} fs-2`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"Membership Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi bi-award text-${getMembershipColor(customerData.membershipLevel)} fs-1 mb-3`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: `text-${getMembershipColor(customerData.membershipLevel)}`,\n              children: customerData.membershipLevel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"Member Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Loyalty Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-primary\",\n                children: customerData.loyaltyPoints\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm h-100\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"Premium Due\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-danger\",\n                    children: [\"$\", customerData.premiumDue]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Total Amount Due\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-primary\",\n                    children: customerData.nextPaymentDate\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-muted\",\n                    children: \"Next Payment Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"primary\",\n                    className: \"w-100\",\n                    children: \"Pay Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"fw-bold mb-3\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"g-4 mb-5\",\n      children: quickActions.map((action, idx) => /*#__PURE__*/_jsxDEV(Col, {\n        lg: 3,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm border-0 h-100\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: `bi ${action.icon} text-primary fs-1 mb-3`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"fw-bold\",\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-3 small\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-primary\",\n              size: \"sm\",\n              className: \"w-100\",\n              onClick: () => handleQuickAction(action.action),\n              children: action.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"My Policies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-hover\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Policy ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Premium\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Coverage\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Next Due\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: policies.map(policy => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: policy.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: policy.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: getStatusBadge(policy.status)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [\"$\", policy.premium]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: [\"$\", policy.coverage.toLocaleString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: policy.nextDue\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 25\n                    }, this)]\n                  }, policy.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-bold mb-0\",\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"list-group list-group-flush\",\n              children: recentActivity.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"list-group-item\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"mb-1\",\n                      children: activity.action\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: activity.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: activity.date\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), getStatusBadge(activity.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)\n              }, activity.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPolicyModal,\n      onHide: () => setShowPolicyModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Apply for New Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handlePolicySubmit,\n        children: [/*#__PURE__*/_jsxDEV(Modal.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Policy Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"type\",\n                  value: policyForm.type,\n                  onChange: handlePolicyFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Policy Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"life\",\n                    children: \"Life Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"health\",\n                    children: \"Health Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"auto\",\n                    children: \"Auto Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"home\",\n                    children: \"Home Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"travel\",\n                    children: \"Travel Insurance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Coverage Amount ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"coverageAmount\",\n                  value: policyForm.coverageAmount,\n                  onChange: handlePolicyFormChange,\n                  placeholder: \"Enter coverage amount\",\n                  min: \"1000\",\n                  step: \"1000\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              name: \"description\",\n              value: policyForm.description,\n              onChange: handlePolicyFormChange,\n              placeholder: \"Provide any additional information about your insurance needs...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Note:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), \" This is a preliminary application. Our team will contact you within 24 hours to complete the process and provide a detailed quote.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowPolicyModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            type: \"submit\",\n            disabled: loading,\n            children: loading ? 'Submitting...' : 'Submit Application'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomerDashboard, \"9gkRH66ZJcvxKRHiVTRPj7O8WkY=\", false, function () {\n  return [useAuth];\n});\n_c = CustomerDashboard;\nexport default CustomerDashboard;\nvar _c;\n$RefreshReg$(_c, \"CustomerDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Modal", "Form", "Table", "useNavigate", "useAuth", "reportsAPI", "policiesAPI", "FaPlus", "FaEye", "FaFileAlt", "FaCreditCard", "jsxDEV", "_jsxDEV", "CustomerDashboard", "_s", "user", "customerData", "setCustomerData", "activePolicies", "pendingClaims", "totalClaims", "premiumDue", "nextPaymentDate", "coverageAmount", "loyaltyPoints", "membershipLevel", "policies", "setPolicies", "recentActivity", "setRecentActivity", "loading", "setLoading", "showPolicyModal", "setShowPolicyModal", "policyForm", "setPolicyForm", "type", "description", "fetchDashboardData", "response", "getDashboardStats", "success", "data", "overview", "myData", "tickets", "error", "console", "fetchDummyData", "fetchedData", "fetchedPolicies", "fetchedActivity", "handlePolicyFormChange", "e", "target", "name", "value", "handlePolicySubmit", "preventDefault", "createPolicy", "alert", "cardData", "label", "count", "icon", "color", "toLocaleString", "quickActions", "title", "action", "handleQuickAction", "window", "location", "href", "getStatusBadge", "status", "variants", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getMembershipColor", "level", "colors", "className", "map", "item", "idx", "lg", "md", "Body", "Header", "variant", "size", "onClick", "policy", "id", "premium", "coverage", "nextDue", "activity", "date", "show", "onHide", "closeButton", "Title", "onSubmit", "Group", "Label", "Select", "onChange", "required", "Control", "placeholder", "min", "step", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Wesync Softwares/POC's/Jun23/moryaInsure/src/components/dashboards/CustomerDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Modal, Form, Table } from 'react-bootstrap';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { reportsAPI, policiesAPI } from '../../services/api';\nimport { FaPlus, FaEye, FaFileAlt, FaCreditCard } from 'react-icons/fa';\n\nconst CustomerDashboard = () => {\n  const { user } = useAuth();\n  const [customerData, setCustomerData] = useState({\n    activePolicies: 0,\n    pendingClaims: 0,\n    totalClaims: 0,\n    premiumDue: 0,\n    nextPaymentDate: '',\n    coverageAmount: 0,\n    loyaltyPoints: 0,\n    membershipLevel: 'Silver',\n  });\n\n  const [policies, setPolicies] = useState([]);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // Policy application state\n  const [showPolicyModal, setShowPolicyModal] = useState(false);\n  const [policyForm, setPolicyForm] = useState({\n    type: '',\n    coverageAmount: '',\n    description: ''\n  });\n\n  // Fetch real data from API\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      const response = await reportsAPI.getDashboardStats();\n      if (response.success && response.data.overview) {\n        const { overview, myData } = response.data;\n        setCustomerData({\n          activePolicies: overview.activePolicies || 0,\n          pendingClaims: overview.pendingClaims || 0,\n          totalClaims: overview.totalClaims || 0,\n          premiumDue: overview.premiumDue || 0,\n          nextPaymentDate: overview.nextPaymentDate || '',\n          coverageAmount: overview.coverageAmount || 0,\n          loyaltyPoints: overview.loyaltyPoints || 0,\n          membershipLevel: overview.membershipLevel || 'Bronze',\n        });\n\n        if (myData) {\n          setPolicies(myData.policies || []);\n          setRecentActivity(myData.tickets || []);\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      // Fallback to existing dummy data\n      fetchDummyData();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchDummyData = () => {\n    const fetchedData = {\n      activePolicies: 0,\n      pendingClaims: 0,\n      totalClaims: 0,\n      premiumDue: 0,\n      nextPaymentDate: '',\n      coverageAmount: 0,\n      loyaltyPoints: 0,\n      membershipLevel: 'Gold',\n    };\n\n    const fetchedPolicies = [\n    \n    ];\n\n    const fetchedActivity = [\n      \n    ];\n\n    setCustomerData(fetchedData);\n    setPolicies(fetchedPolicies);\n    setRecentActivity(fetchedActivity);\n  };\n\n  // Handle policy application\n  const handlePolicyFormChange = (e) => {\n    setPolicyForm({\n      ...policyForm,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handlePolicySubmit = async (e) => {\n    e.preventDefault();\n    try {\n      setLoading(true);\n      const response = await policiesAPI.createPolicy(policyForm);\n      if (response.success) {\n        setShowPolicyModal(false);\n        setPolicyForm({ type: '', coverageAmount: '', description: '' });\n        fetchDashboardData(); // Refresh data\n        alert('Policy application submitted successfully!');\n      }\n    } catch (error) {\n      console.error('Error submitting policy application:', error);\n      alert('Error submitting policy application. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const cardData = [\n    { label: 'Active Policies', count: customerData.activePolicies, icon: 'bi-shield-check', color: 'primary' },\n    { label: 'Pending Claims', count: customerData.pendingClaims, icon: 'bi-clock-history', color: 'warning' },\n    { label: 'Total Coverage', count: `$${customerData.coverageAmount.toLocaleString()}`, icon: 'bi-umbrella', color: 'success' },\n    { label: 'Loyalty Points', count: customerData.loyaltyPoints, icon: 'bi-star-fill', color: 'info' },\n  ];\n\n  const quickActions = [\n    { title: 'Apply for Policy', description: 'Apply for new insurance policy', icon: 'bi-plus-circle', action: 'apply' },\n    { title: 'Pay Premium', description: 'Make premium payments', icon: 'bi-credit-card', action: 'payment' },\n    { title: 'File Claim', description: 'Submit new insurance claim', icon: 'bi-file-plus', action: 'claim' },\n    { title: 'Contact Support', description: 'Get help from our team', icon: 'bi-headset', action: 'support' },\n  ];\n\n  const handleQuickAction = (action) => {\n    switch (action) {\n      case 'apply':\n        setShowPolicyModal(true);\n        break;\n      case 'payment':\n        // Navigate to payment page\n        window.location.href = '/payments';\n        break;\n      case 'claim':\n        // Navigate to claims page\n        window.location.href = '/claims';\n        break;\n      case 'support':\n        // Navigate to support page\n        window.location.href = '/tickets';\n        break;\n      default:\n        break;\n    }\n  };\n\n  const getStatusBadge = (status) => {\n    const variants = {\n      'Active': 'success',\n      'Pending': 'warning',\n      'Expired': 'danger',\n      'completed': 'success',\n      'pending': 'warning'\n    };\n    return <Badge bg={variants[status]}>{status}</Badge>;\n  };\n\n  const getMembershipColor = (level) => {\n    const colors = {\n      'Bronze': 'warning',\n      'Silver': 'secondary',\n      'Gold': 'warning',\n      'Platinum': 'dark'\n    };\n    return colors[level] || 'primary';\n  };\n\n  return (\n    <Container className=\"mt-4\">\n      <div className=\"mb-4\">\n        <h2 className=\"fw-bold\">Customer Dashboard</h2>\n        <p className=\"lead\">\n          Welcome back, <span className=\"text-primary fw-semibold\">{user.name}</span>! \n          Manage your insurance policies and claims.\n        </p>\n      </div>\n\n      {/* Overview Cards */}\n      <Row className=\"g-4 mb-5\">\n        {cardData.map((item, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className={`shadow-sm border-0 bg-${item.color} text-white h-100`}>\n              <Card.Body className=\"d-flex align-items-center justify-content-between\">\n                <div>\n                  <h6 className=\"card-title mb-2\">{item.label}</h6>\n                  <h4 className=\"fw-bold\">{item.count}</h4>\n                </div>\n                <i className={`bi ${item.icon} fs-2`}></i>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Membership Status and Premium Due */}\n      <Row className=\"mb-4\">\n        <Col lg={4}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">Membership Status</h5>\n            </Card.Header>\n            <Card.Body className=\"text-center\">\n              <i className={`bi bi-award text-${getMembershipColor(customerData.membershipLevel)} fs-1 mb-3`}></i>\n              <h4 className={`text-${getMembershipColor(customerData.membershipLevel)}`}>\n                {customerData.membershipLevel}\n              </h4>\n              <p className=\"text-muted\">Member Level</p>\n              <div className=\"mt-3\">\n                <small className=\"text-muted\">Loyalty Points</small>\n                <h5 className=\"text-primary\">{customerData.loyaltyPoints}</h5>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col lg={8}>\n          <Card className=\"shadow-sm h-100\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">Premium Due</h5>\n            </Card.Header>\n            <Card.Body>\n              <Row>\n                <Col md={6}>\n                  <div className=\"text-center\">\n                    <h2 className=\"text-danger\">${customerData.premiumDue}</h2>\n                    <p className=\"text-muted\">Total Amount Due</p>\n                  </div>\n                </Col>\n                <Col md={6}>\n                  <div className=\"text-center\">\n                    <h5 className=\"text-primary\">{customerData.nextPaymentDate}</h5>\n                    <p className=\"text-muted\">Next Payment Date</p>\n                    <Button variant=\"primary\" className=\"w-100\">\n                      Pay Now\n                    </Button>\n                  </div>\n                </Col>\n              </Row>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Quick Actions */}\n      <Row className=\"mb-4\">\n        <Col>\n          <h4 className=\"fw-bold mb-3\">Quick Actions</h4>\n        </Col>\n      </Row>\n      <Row className=\"g-4 mb-5\">\n        {quickActions.map((action, idx) => (\n          <Col lg={3} md={6} key={idx}>\n            <Card className=\"shadow-sm border-0 h-100\">\n              <Card.Body className=\"text-center\">\n                <i className={`bi ${action.icon} text-primary fs-1 mb-3`}></i>\n                <h6 className=\"fw-bold\">{action.title}</h6>\n                <p className=\"text-muted mb-3 small\">{action.description}</p>\n                <Button\n                  variant=\"outline-primary\"\n                  size=\"sm\"\n                  className=\"w-100\"\n                  onClick={() => handleQuickAction(action.action)}\n                >\n                  {action.title}\n                </Button>\n              </Card.Body>\n            </Card>\n          </Col>\n        ))}\n      </Row>\n\n      {/* Policies and Recent Activity */}\n      <Row>\n        <Col lg={8}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">My Policies</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"table-responsive\">\n                <table className=\"table table-hover\">\n                  <thead>\n                    <tr>\n                      <th>Policy ID</th>\n                      <th>Type</th>\n                      <th>Status</th>\n                      <th>Premium</th>\n                      <th>Coverage</th>\n                      <th>Next Due</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {policies.map((policy) => (\n                      <tr key={policy.id}>\n                        <td>{policy.id}</td>\n                        <td>{policy.type}</td>\n                        <td>{getStatusBadge(policy.status)}</td>\n                        <td>${policy.premium}</td>\n                        <td>${policy.coverage.toLocaleString()}</td>\n                        <td>{policy.nextDue}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col lg={4}>\n          <Card className=\"shadow-sm\">\n            <Card.Header>\n              <h5 className=\"fw-bold mb-0\">Recent Activity</h5>\n            </Card.Header>\n            <Card.Body>\n              <div className=\"list-group list-group-flush\">\n                {recentActivity.map((activity) => (\n                  <div key={activity.id} className=\"list-group-item\">\n                    <div className=\"d-flex justify-content-between align-items-start\">\n                      <div>\n                        <h6 className=\"mb-1\">{activity.action}</h6>\n                        <small className=\"text-muted\">{activity.description}</small>\n                        <br />\n                        <small className=\"text-muted\">{activity.date}</small>\n                      </div>\n                      {getStatusBadge(activity.status)}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Policy Application Modal */}\n      <Modal show={showPolicyModal} onHide={() => setShowPolicyModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>Apply for New Policy</Modal.Title>\n        </Modal.Header>\n        <Form onSubmit={handlePolicySubmit}>\n          <Modal.Body>\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Policy Type</Form.Label>\n                  <Form.Select\n                    name=\"type\"\n                    value={policyForm.type}\n                    onChange={handlePolicyFormChange}\n                    required\n                  >\n                    <option value=\"\">Select Policy Type</option>\n                    <option value=\"life\">Life Insurance</option>\n                    <option value=\"health\">Health Insurance</option>\n                    <option value=\"auto\">Auto Insurance</option>\n                    <option value=\"home\">Home Insurance</option>\n                    <option value=\"travel\">Travel Insurance</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Coverage Amount ($)</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    name=\"coverageAmount\"\n                    value={policyForm.coverageAmount}\n                    onChange={handlePolicyFormChange}\n                    placeholder=\"Enter coverage amount\"\n                    min=\"1000\"\n                    step=\"1000\"\n                    required\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Additional Information</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                name=\"description\"\n                value={policyForm.description}\n                onChange={handlePolicyFormChange}\n                placeholder=\"Provide any additional information about your insurance needs...\"\n              />\n            </Form.Group>\n            <div className=\"alert alert-info\">\n              <small>\n                <strong>Note:</strong> This is a preliminary application. Our team will contact you within 24 hours to complete the process and provide a detailed quote.\n              </small>\n            </div>\n          </Modal.Body>\n          <Modal.Footer>\n            <Button variant=\"secondary\" onClick={() => setShowPolicyModal(false)}>\n              Cancel\n            </Button>\n            <Button variant=\"primary\" type=\"submit\" disabled={loading}>\n              {loading ? 'Submitting...' : 'Submit Application'}\n            </Button>\n          </Modal.Footer>\n        </Form>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default CustomerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AAC9F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AAC5D,SAASC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC;IAC/C0B,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC;IAC3C4C,IAAI,EAAE,EAAE;IACRb,cAAc,EAAE,EAAE;IAClBc,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA5C,SAAS,CAAC,MAAM;IACd6C,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMlC,UAAU,CAACmC,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,QAAQ,EAAE;QAC9C,MAAM;UAAEA,QAAQ;UAAEC;QAAO,CAAC,GAAGL,QAAQ,CAACG,IAAI;QAC1CzB,eAAe,CAAC;UACdC,cAAc,EAAEyB,QAAQ,CAACzB,cAAc,IAAI,CAAC;UAC5CC,aAAa,EAAEwB,QAAQ,CAACxB,aAAa,IAAI,CAAC;UAC1CC,WAAW,EAAEuB,QAAQ,CAACvB,WAAW,IAAI,CAAC;UACtCC,UAAU,EAAEsB,QAAQ,CAACtB,UAAU,IAAI,CAAC;UACpCC,eAAe,EAAEqB,QAAQ,CAACrB,eAAe,IAAI,EAAE;UAC/CC,cAAc,EAAEoB,QAAQ,CAACpB,cAAc,IAAI,CAAC;UAC5CC,aAAa,EAAEmB,QAAQ,CAACnB,aAAa,IAAI,CAAC;UAC1CC,eAAe,EAAEkB,QAAQ,CAAClB,eAAe,IAAI;QAC/C,CAAC,CAAC;QAEF,IAAImB,MAAM,EAAE;UACVjB,WAAW,CAACiB,MAAM,CAAClB,QAAQ,IAAI,EAAE,CAAC;UAClCG,iBAAiB,CAACe,MAAM,CAACC,OAAO,IAAI,EAAE,CAAC;QACzC;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACAE,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,WAAW,GAAG;MAClB/B,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE;IACnB,CAAC;IAED,MAAMyB,eAAe,GAAG,EAEvB;IAED,MAAMC,eAAe,GAAG,EAEvB;IAEDlC,eAAe,CAACgC,WAAW,CAAC;IAC5BtB,WAAW,CAACuB,eAAe,CAAC;IAC5BrB,iBAAiB,CAACsB,eAAe,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpClB,aAAa,CAAC;MACZ,GAAGD,UAAU;MACb,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAOJ,CAAC,IAAK;IACtCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMjC,WAAW,CAACqD,YAAY,CAACzB,UAAU,CAAC;MAC3D,IAAIK,QAAQ,CAACE,OAAO,EAAE;QACpBR,kBAAkB,CAAC,KAAK,CAAC;QACzBE,aAAa,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEb,cAAc,EAAE,EAAE;UAAEc,WAAW,EAAE;QAAG,CAAC,CAAC;QAChEC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACtBsB,KAAK,CAAC,4CAA4C,CAAC;MACrD;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5Dc,KAAK,CAAC,wDAAwD,CAAC;IACjE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,QAAQ,GAAG,CACf;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE/C,YAAY,CAACE,cAAc;IAAE8C,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3G;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE/C,YAAY,CAACG,aAAa;IAAE6C,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC1G;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE,IAAI/C,YAAY,CAACO,cAAc,CAAC2C,cAAc,CAAC,CAAC,EAAE;IAAEF,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC7H;IAAEH,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE/C,YAAY,CAACQ,aAAa;IAAEwC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAO,CAAC,CACpG;EAED,MAAME,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,kBAAkB;IAAE/B,WAAW,EAAE,gCAAgC;IAAE2B,IAAI,EAAE,gBAAgB;IAAEK,MAAM,EAAE;EAAQ,CAAC,EACrH;IAAED,KAAK,EAAE,aAAa;IAAE/B,WAAW,EAAE,uBAAuB;IAAE2B,IAAI,EAAE,gBAAgB;IAAEK,MAAM,EAAE;EAAU,CAAC,EACzG;IAAED,KAAK,EAAE,YAAY;IAAE/B,WAAW,EAAE,4BAA4B;IAAE2B,IAAI,EAAE,cAAc;IAAEK,MAAM,EAAE;EAAQ,CAAC,EACzG;IAAED,KAAK,EAAE,iBAAiB;IAAE/B,WAAW,EAAE,wBAAwB;IAAE2B,IAAI,EAAE,YAAY;IAAEK,MAAM,EAAE;EAAU,CAAC,CAC3G;EAED,MAAMC,iBAAiB,GAAID,MAAM,IAAK;IACpC,QAAQA,MAAM;MACZ,KAAK,OAAO;QACVpC,kBAAkB,CAAC,IAAI,CAAC;QACxB;MACF,KAAK,SAAS;QACZ;QACAsC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;QAClC;MACF,KAAK,OAAO;QACV;QACAF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;QAChC;MACF,KAAK,SAAS;QACZ;QACAF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU;QACjC;MACF;QACE;IACJ;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,QAAQ,GAAG;MACf,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE,QAAQ;MACnB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE;IACb,CAAC;IACD,oBAAOhE,OAAA,CAACb,KAAK;MAAC8E,EAAE,EAAED,QAAQ,CAACD,MAAM,CAAE;MAAAG,QAAA,EAAEH;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACtD,CAAC;EAED,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,WAAW;MACrB,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE;IACd,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,oBACExE,OAAA,CAAClB,SAAS;IAAC4F,SAAS,EAAC,MAAM;IAAAR,QAAA,gBACzBlE,OAAA;MAAK0E,SAAS,EAAC,MAAM;MAAAR,QAAA,gBACnBlE,OAAA;QAAI0E,SAAS,EAAC,SAAS;QAAAR,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CtE,OAAA;QAAG0E,SAAS,EAAC,MAAM;QAAAR,QAAA,GAAC,gBACJ,eAAAlE,OAAA;UAAM0E,SAAS,EAAC,0BAA0B;UAAAR,QAAA,EAAE/D,IAAI,CAACwC;QAAI;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gDAE7E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtE,OAAA,CAACjB,GAAG;MAAC2F,SAAS,EAAC,UAAU;MAAAR,QAAA,EACtBjB,QAAQ,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACtB7E,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAChBlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAE,yBAAyBE,IAAI,CAACvB,KAAK,mBAAoB;UAAAa,QAAA,eACtElE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAACN,SAAS,EAAC,mDAAmD;YAAAR,QAAA,gBACtElE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAI0E,SAAS,EAAC,iBAAiB;gBAAAR,QAAA,EAAEU,IAAI,CAAC1B;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjDtE,OAAA;gBAAI0E,SAAS,EAAC,SAAS;gBAAAR,QAAA,EAAEU,IAAI,CAACzB;cAAK;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNtE,OAAA;cAAG0E,SAAS,EAAE,MAAME,IAAI,CAACxB,IAAI;YAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GATeO,GAAG;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtE,OAAA,CAACjB,GAAG;MAAC2F,SAAS,EAAC,MAAM;MAAAR,QAAA,gBACnBlE,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACTlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAC,iBAAiB;UAAAR,QAAA,gBAC/BlE,OAAA,CAACf,IAAI,CAACgG,MAAM;YAAAf,QAAA,eACVlE,OAAA;cAAI0E,SAAS,EAAC,cAAc;cAAAR,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACdtE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAACN,SAAS,EAAC,aAAa;YAAAR,QAAA,gBAChClE,OAAA;cAAG0E,SAAS,EAAE,oBAAoBH,kBAAkB,CAACnE,YAAY,CAACS,eAAe,CAAC;YAAa;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpGtE,OAAA;cAAI0E,SAAS,EAAE,QAAQH,kBAAkB,CAACnE,YAAY,CAACS,eAAe,CAAC,EAAG;cAAAqD,QAAA,EACvE9D,YAAY,CAACS;YAAe;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACLtE,OAAA;cAAG0E,SAAS,EAAC,YAAY;cAAAR,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1CtE,OAAA;cAAK0E,SAAS,EAAC,MAAM;cAAAR,QAAA,gBACnBlE,OAAA;gBAAO0E,SAAS,EAAC,YAAY;gBAAAR,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDtE,OAAA;gBAAI0E,SAAS,EAAC,cAAc;gBAAAR,QAAA,EAAE9D,YAAY,CAACQ;cAAa;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtE,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACTlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAC,iBAAiB;UAAAR,QAAA,gBAC/BlE,OAAA,CAACf,IAAI,CAACgG,MAAM;YAAAf,QAAA,eACVlE,OAAA;cAAI0E,SAAS,EAAC,cAAc;cAAAR,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACdtE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAAAd,QAAA,eACRlE,OAAA,CAACjB,GAAG;cAAAmF,QAAA,gBACFlE,OAAA,CAAChB,GAAG;gBAAC+F,EAAE,EAAE,CAAE;gBAAAb,QAAA,eACTlE,OAAA;kBAAK0E,SAAS,EAAC,aAAa;kBAAAR,QAAA,gBAC1BlE,OAAA;oBAAI0E,SAAS,EAAC,aAAa;oBAAAR,QAAA,GAAC,GAAC,EAAC9D,YAAY,CAACK,UAAU;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3DtE,OAAA;oBAAG0E,SAAS,EAAC,YAAY;oBAAAR,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtE,OAAA,CAAChB,GAAG;gBAAC+F,EAAE,EAAE,CAAE;gBAAAb,QAAA,eACTlE,OAAA;kBAAK0E,SAAS,EAAC,aAAa;kBAAAR,QAAA,gBAC1BlE,OAAA;oBAAI0E,SAAS,EAAC,cAAc;oBAAAR,QAAA,EAAE9D,YAAY,CAACM;kBAAe;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChEtE,OAAA;oBAAG0E,SAAS,EAAC,YAAY;oBAAAR,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/CtE,OAAA,CAACd,MAAM;oBAACgG,OAAO,EAAC,SAAS;oBAACR,SAAS,EAAC,OAAO;oBAAAR,QAAA,EAAC;kBAE5C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA,CAACjB,GAAG;MAAC2F,SAAS,EAAC,MAAM;MAAAR,QAAA,eACnBlE,OAAA,CAAChB,GAAG;QAAAkF,QAAA,eACFlE,OAAA;UAAI0E,SAAS,EAAC,cAAc;UAAAR,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtE,OAAA,CAACjB,GAAG;MAAC2F,SAAS,EAAC,UAAU;MAAAR,QAAA,EACtBX,YAAY,CAACoB,GAAG,CAAC,CAAClB,MAAM,EAAEoB,GAAG,kBAC5B7E,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAChBlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAC,0BAA0B;UAAAR,QAAA,eACxClE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAACN,SAAS,EAAC,aAAa;YAAAR,QAAA,gBAChClE,OAAA;cAAG0E,SAAS,EAAE,MAAMjB,MAAM,CAACL,IAAI;YAA0B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DtE,OAAA;cAAI0E,SAAS,EAAC,SAAS;cAAAR,QAAA,EAAET,MAAM,CAACD;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3CtE,OAAA;cAAG0E,SAAS,EAAC,uBAAuB;cAAAR,QAAA,EAAET,MAAM,CAAChC;YAAW;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DtE,OAAA,CAACd,MAAM;cACLgG,OAAO,EAAC,iBAAiB;cACzBC,IAAI,EAAC,IAAI;cACTT,SAAS,EAAC,OAAO;cACjBU,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAACD,MAAM,CAACA,MAAM,CAAE;cAAAS,QAAA,EAE/CT,MAAM,CAACD;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GAfeO,GAAG;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBtB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtE,OAAA,CAACjB,GAAG;MAAAmF,QAAA,gBACFlE,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACTlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAC,WAAW;UAAAR,QAAA,gBACzBlE,OAAA,CAACf,IAAI,CAACgG,MAAM;YAAAf,QAAA,eACVlE,OAAA;cAAI0E,SAAS,EAAC,cAAc;cAAAR,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACdtE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAAAd,QAAA,eACRlE,OAAA;cAAK0E,SAAS,EAAC,kBAAkB;cAAAR,QAAA,eAC/BlE,OAAA;gBAAO0E,SAAS,EAAC,mBAAmB;gBAAAR,QAAA,gBAClClE,OAAA;kBAAAkE,QAAA,eACElE,OAAA;oBAAAkE,QAAA,gBACElE,OAAA;sBAAAkE,QAAA,EAAI;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClBtE,OAAA;sBAAAkE,QAAA,EAAI;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACbtE,OAAA;sBAAAkE,QAAA,EAAI;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACftE,OAAA;sBAAAkE,QAAA,EAAI;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChBtE,OAAA;sBAAAkE,QAAA,EAAI;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjBtE,OAAA;sBAAAkE,QAAA,EAAI;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRtE,OAAA;kBAAAkE,QAAA,EACGpD,QAAQ,CAAC6D,GAAG,CAAEU,MAAM,iBACnBrF,OAAA;oBAAAkE,QAAA,gBACElE,OAAA;sBAAAkE,QAAA,EAAKmB,MAAM,CAACC;oBAAE;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpBtE,OAAA;sBAAAkE,QAAA,EAAKmB,MAAM,CAAC7D;oBAAI;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtBtE,OAAA;sBAAAkE,QAAA,EAAKJ,cAAc,CAACuB,MAAM,CAACtB,MAAM;oBAAC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCtE,OAAA;sBAAAkE,QAAA,GAAI,GAAC,EAACmB,MAAM,CAACE,OAAO;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1BtE,OAAA;sBAAAkE,QAAA,GAAI,GAAC,EAACmB,MAAM,CAACG,QAAQ,CAAClC,cAAc,CAAC,CAAC;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5CtE,OAAA;sBAAAkE,QAAA,EAAKmB,MAAM,CAACI;oBAAO;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GANlBe,MAAM,CAACC,EAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAOd,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtE,OAAA,CAAChB,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACTlE,OAAA,CAACf,IAAI;UAACyF,SAAS,EAAC,WAAW;UAAAR,QAAA,gBACzBlE,OAAA,CAACf,IAAI,CAACgG,MAAM;YAAAf,QAAA,eACVlE,OAAA;cAAI0E,SAAS,EAAC,cAAc;cAAAR,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACdtE,OAAA,CAACf,IAAI,CAAC+F,IAAI;YAAAd,QAAA,eACRlE,OAAA;cAAK0E,SAAS,EAAC,6BAA6B;cAAAR,QAAA,EACzClD,cAAc,CAAC2D,GAAG,CAAEe,QAAQ,iBAC3B1F,OAAA;gBAAuB0E,SAAS,EAAC,iBAAiB;gBAAAR,QAAA,eAChDlE,OAAA;kBAAK0E,SAAS,EAAC,kDAAkD;kBAAAR,QAAA,gBAC/DlE,OAAA;oBAAAkE,QAAA,gBACElE,OAAA;sBAAI0E,SAAS,EAAC,MAAM;sBAAAR,QAAA,EAAEwB,QAAQ,CAACjC;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3CtE,OAAA;sBAAO0E,SAAS,EAAC,YAAY;sBAAAR,QAAA,EAAEwB,QAAQ,CAACjE;oBAAW;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5DtE,OAAA;sBAAAmE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtE,OAAA;sBAAO0E,SAAS,EAAC,YAAY;sBAAAR,QAAA,EAAEwB,QAAQ,CAACC;oBAAI;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,EACLR,cAAc,CAAC4B,QAAQ,CAAC3B,MAAM,CAAC;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC,GATEoB,QAAQ,CAACJ,EAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUhB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA,CAACZ,KAAK;MAACwG,IAAI,EAAExE,eAAgB;MAACyE,MAAM,EAAEA,CAAA,KAAMxE,kBAAkB,CAAC,KAAK,CAAE;MAAC8D,IAAI,EAAC,IAAI;MAAAjB,QAAA,gBAC9ElE,OAAA,CAACZ,KAAK,CAAC6F,MAAM;QAACa,WAAW;QAAA5B,QAAA,eACvBlE,OAAA,CAACZ,KAAK,CAAC2G,KAAK;UAAA7B,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACftE,OAAA,CAACX,IAAI;QAAC2G,QAAQ,EAAEnD,kBAAmB;QAAAqB,QAAA,gBACjClE,OAAA,CAACZ,KAAK,CAAC4F,IAAI;UAAAd,QAAA,gBACTlE,OAAA,CAACjB,GAAG;YAAAmF,QAAA,gBACFlE,OAAA,CAAChB,GAAG;cAAC+F,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTlE,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAR,QAAA,gBAC1BlE,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAhC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCtE,OAAA,CAACX,IAAI,CAAC8G,MAAM;kBACVxD,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEtB,UAAU,CAACE,IAAK;kBACvB4E,QAAQ,EAAE5D,sBAAuB;kBACjC6D,QAAQ;kBAAAnC,QAAA,gBAERlE,OAAA;oBAAQ4C,KAAK,EAAC,EAAE;oBAAAsB,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtE,OAAA;oBAAQ4C,KAAK,EAAC,MAAM;oBAAAsB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtE,OAAA;oBAAQ4C,KAAK,EAAC,QAAQ;oBAAAsB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDtE,OAAA;oBAAQ4C,KAAK,EAAC,MAAM;oBAAAsB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtE,OAAA;oBAAQ4C,KAAK,EAAC,MAAM;oBAAAsB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtE,OAAA;oBAAQ4C,KAAK,EAAC,QAAQ;oBAAAsB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtE,OAAA,CAAChB,GAAG;cAAC+F,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTlE,OAAA,CAACX,IAAI,CAAC4G,KAAK;gBAACvB,SAAS,EAAC,MAAM;gBAAAR,QAAA,gBAC1BlE,OAAA,CAACX,IAAI,CAAC6G,KAAK;kBAAAhC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CtE,OAAA,CAACX,IAAI,CAACiH,OAAO;kBACX9E,IAAI,EAAC,QAAQ;kBACbmB,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEtB,UAAU,CAACX,cAAe;kBACjCyF,QAAQ,EAAE5D,sBAAuB;kBACjC+D,WAAW,EAAC,uBAAuB;kBACnCC,GAAG,EAAC,MAAM;kBACVC,IAAI,EAAC,MAAM;kBACXJ,QAAQ;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtE,OAAA,CAACX,IAAI,CAAC4G,KAAK;YAACvB,SAAS,EAAC,MAAM;YAAAR,QAAA,gBAC1BlE,OAAA,CAACX,IAAI,CAAC6G,KAAK;cAAAhC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CtE,OAAA,CAACX,IAAI,CAACiH,OAAO;cACXI,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRhE,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEtB,UAAU,CAACG,WAAY;cAC9B2E,QAAQ,EAAE5D,sBAAuB;cACjC+D,WAAW,EAAC;YAAkE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eACbtE,OAAA;YAAK0E,SAAS,EAAC,kBAAkB;YAAAR,QAAA,eAC/BlE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBAAAkE,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,uIACxB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACbtE,OAAA,CAACZ,KAAK,CAACwH,MAAM;UAAA1C,QAAA,gBACXlE,OAAA,CAACd,MAAM;YAACgG,OAAO,EAAC,WAAW;YAACE,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;YAAA6C,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtE,OAAA,CAACd,MAAM;YAACgG,OAAO,EAAC,SAAS;YAAC1D,IAAI,EAAC,QAAQ;YAACqF,QAAQ,EAAE3F,OAAQ;YAAAgD,QAAA,EACvDhD,OAAO,GAAG,eAAe,GAAG;UAAoB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACpE,EAAA,CAvZID,iBAAiB;EAAA,QACJT,OAAO;AAAA;AAAAsH,EAAA,GADpB7G,iBAAiB;AAyZvB,eAAeA,iBAAiB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}