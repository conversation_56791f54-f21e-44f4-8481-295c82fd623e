import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [username, setUsername] = useState('Sushil'); // You can update this after login
  const [stats, setStats] = useState({
    totalPolicyHolders: 0,
    pendingPolicies: 0,
    approvedPolicies: 0,
    deniedPolicies: 0,
    totalTickets: 0,
    pendingTickets: 0,
    closedTickets: 0,
    activeFields: 0,
  });

  // Simulate data fetching
  useEffect(() => {
    // Simulated response from backend (you can replace with Axios/Fetch)
    const fetchedStats = {
      totalPolicyHolders: 200,
      pendingPolicies: 20,
      approvedPolicies: 150,
      deniedPolicies: 30,
      totalTickets: 80,
      pendingTickets: 25,
      closedTickets: 40,
      activeFields: 8,
    };

    setStats(fetchedStats);
  }, []);

  const cardData = [
    { label: 'Policy Holders', count: stats.totalPolicyHolders, icon: 'bi-people-fill', color: 'primary' },
    { label: 'Pending Policies', count: stats.pendingPolicies, icon: 'bi-hourglass-split', color: 'warning' },
    { label: 'Approved Policies', count: stats.approvedPolicies, icon: 'bi-check-circle-fill', color: 'success' },
    { label: 'Denied Policies', count: stats.deniedPolicies, icon: 'bi-x-circle-fill', color: 'danger' },
    { label: 'Support Tickets', count: stats.totalTickets, icon: 'bi-ticket-detailed-fill', color: 'info' },
    { label: 'Pending Tickets', count: stats.pendingTickets, icon: 'bi-clock-fill', color: 'warning' },
    { label: 'Closed Tickets', count: stats.closedTickets, icon: 'bi-lock-fill', color: 'secondary' },
    { label: 'Active Fields', count: stats.activeFields, icon: 'bi-lightning-fill', color: 'dark' },
  ];

  return (
    
    <div className="container mt-4">
      <div className="mb-4">    
        <h2 className="fw-bold">Admin Dashboard</h2>
        <p className="lead">Welcome to Admin Dashboard, <span className="text-primary fw-semibold">{username}</span>!</p>
      </div>

      <div className="row g-4">
        {cardData.map((item, idx) => (
          <div className="col-md-3" key={idx}>
            <div className={`card shadow-sm border-0 bg-${item.color} text-white h-100`}>
              <div className="card-body d-flex align-items-center justify-content-between">
                <div>
                  <h6 className="card-title mb-2">{item.label}</h6>
                  <h4 className="fw-bold">{item.count}</h4>
                </div>
                <i className={`bi ${item.icon} fs-2`}></i>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dashboard;
